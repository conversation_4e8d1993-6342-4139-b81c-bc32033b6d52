package space.lzhq.ph;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matcher;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 测试辅助工具类
 * 提供测试中常用的工具方法和数据生成功能
 */
@Slf4j
public class TestHelper {

    private TestHelper() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * JSON 路径提取异常
     */
    public static class JsonPathExtractionException extends RuntimeException {
        public JsonPathExtractionException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static final String CODE_JSON_PATH = "$.code";
    public static final String MSG_JSON_PATH = "$.msg";

    // ==================== JSON 数据提取工具方法 ====================

    /**
     * 从 MvcResult 中提取指定 JSON 路径的字符串值
     *
     * @param result   测试结果
     * @param jsonPath JSON 路径，使用点号分隔，例如 "data.attachment.id"
     * @return 提取到的字符串值，如果路径不存在或值为 null 则返回 null
     * @throws Exception 如果解析 JSON 失败
     */
    public static String extractStringFromJsonPath(MvcResult result, String jsonPath) throws Exception {
        String responseContent = result.getResponse().getContentAsString();
        log.debug("从响应中提取 JSON 路径 '{}' 的值，响应内容: {}", jsonPath, responseContent);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseContent);

            // 按点号分割路径
            String[] pathParts = jsonPath.split("\\.");
            JsonNode currentNode = rootNode;

            // 逐级导航到目标节点
            for (String part : pathParts) {
                if (currentNode == null || currentNode.isNull()) {
                    log.debug("JSON 路径 '{}' 中的节点 '{}' 不存在或为 null", jsonPath, part);
                    return null;
                }
                currentNode = currentNode.get(part);
            }

            // 检查最终节点是否存在且不为 null
            if (currentNode == null || currentNode.isNull()) {
                log.debug("JSON 路径 '{}' 的最终值不存在或为 null", jsonPath);
                return null;
            }

            String value = currentNode.asText();
            log.debug("成功提取 JSON 路径 '{}' 的值: {}", jsonPath, value);
            return value;

        } catch (Exception e) {
            log.error("解析 JSON 路径 '{}' 时发生异常: {}", jsonPath, e.getMessage(), e);
            throw new JsonPathExtractionException("提取 JSON 路径值失败: " + jsonPath, e);
        }
    }

    /**
     * 从 MvcResult 中提取指定 JSON 路径的字符串值（带默认值）
     *
     * @param result       测试结果
     * @param jsonPath     JSON 路径，使用点号分隔，例如 "data.attachment.id"
     * @param defaultValue 当路径不存在或值为 null 时返回的默认值
     * @return 提取到的字符串值，如果路径不存在或值为 null 则返回默认值
     * @throws Exception 如果解析 JSON 失败
     */
    public static String extractStringFromJsonPath(MvcResult result, String jsonPath, String defaultValue) throws Exception {
        String value = extractStringFromJsonPath(result, jsonPath);
        return value != null ? value : defaultValue;
    }

    /**
     * 检查 MvcResult 中指定 JSON 路径的节点是否存在
     *
     * @param result   测试结果
     * @param jsonPath JSON 路径，使用点号分隔
     * @return 如果路径存在且不为 null 则返回 true，否则返回 false
     */
    public static boolean isJsonPathExists(MvcResult result, String jsonPath) {
        try {
            String value = extractStringFromJsonPath(result, jsonPath);
            return value != null;
        } catch (Exception e) {
            log.debug("检查 JSON 路径 '{}' 是否存在时发生异常: {}", jsonPath, e.getMessage());
            return false;
        }
    }

    // ==================== ResultMatcher 工具方法 ====================

    /**
     * 数据字段期望状态枚举
     */
    public enum DataExpectation {
        /**
         * 期望数据字段存在
         */
        EXISTS,
        /**
         * 期望数据字段不存在
         */
        NOT_EXISTS,
        /**
         * 不关心数据字段是否存在
         */
        IGNORE
    }

    /**
     * 创建一个用于验证接口成功响应的 ResultMatcher
     * <p>
     * 该方法封装了标准的成功响应验证逻辑，包括：
     * <ul>
     *   <li>HTTP 状态码为 200 OK</li>
     *   <li>响应内容类型为 application/json</li>
     *   <li>响应 JSON 中 code 字段值为 0</li>
     *   <li>响应 JSON 中 msg 字段值为 "操作成功"</li>
     * </ul>
     *
     * <p>使用示例：</p>
     * <pre>{@code
     * mockMvc.perform(...)
     *        .andExpect(expectSuccessResponse(DataExpectation.EXISTS))
     *        .andExpect(jsonPath("$.data.id").exists());
     * }</pre>
     *
     * @param dataExpectation 数据字段的期望状态
     * @return ResultMatcher 用于验证成功响应的匹配器
     */
    public static ResultMatcher expectSuccessResponse(DataExpectation dataExpectation) {
        return expectSuccessResponse(dataExpectation, "操作成功");
    }

    /**
     * 创建一个用于验证接口成功响应的 ResultMatcher
     * <p>
     * 该方法封装了标准的成功响应验证逻辑，包括：
     * <ul>
     *   <li>HTTP 状态码为 200 OK</li>
     *   <li>响应内容类型为 application/json</li>
     *   <li>响应 JSON 中 code 字段值为 0</li>
     *   <li>响应 JSON 中 msg 字段值为 {@code expectedMessage}</li>
     * </ul>
     *
     * <p>使用示例：</p>
     * <pre>{@code
     * mockMvc.perform(...)
     *        .andExpect(expectSuccessResponse(DataExpectation.EXISTS, "操作成功"))
     *        .andExpect(jsonPath("$.data.id").exists());
     * }</pre>
     *
     * @param dataExpectation 数据字段的期望状态
     * @param expectedMessage 期望的成功消息
     * @return ResultMatcher 用于验证成功响应的匹配器
     */
    public static ResultMatcher expectSuccessResponse(DataExpectation dataExpectation, String expectedMessage) {
        return result -> {
            status().isOk().match(result);
            content().contentType(MediaType.APPLICATION_JSON).match(result);
            jsonPath(CODE_JSON_PATH).value(0).match(result);
            jsonPath(MSG_JSON_PATH).value(expectedMessage).match(result);

            switch (dataExpectation) {
                case EXISTS:
                    jsonPath("$.data").exists().match(result);
                    break;
                case NOT_EXISTS:
                    jsonPath("$.data").doesNotExist().match(result);
                    break;
                case IGNORE:
                    // 不验证 data 字段
                    break;
                default:
                    throw new IllegalArgumentException("不支持的数据期望状态: " + dataExpectation);
            }
        };
    }

    /**
     * 创建一个用于验证接口错误响应的 ResultMatcher
     * <p>
     * 该方法封装了标准的错误响应验证逻辑，包括：
     * <ul>
     *   <li>HTTP 状态码为 200 OK（业务错误通常仍返回 200）</li>
     *   <li>响应内容类型为 application/json</li>
     *   <li>响应 JSON 中 code 字段值为指定的错误码</li>
     *   <li>响应 JSON 中 msg 字段值为指定的错误消息</li>
     * </ul>
     *
     * <p>使用示例：</p>
     * <pre>{@code
     * mockMvc.perform(...)
     *        .andExpect(expectErrorResponse(500, "操作失败"))
     *        .andExpect(jsonPath("$.data").isEmpty());
     * }</pre>
     *
     * @param expectedCode    期望的错误码
     * @param expectedMessage 期望的错误消息
     * @return ResultMatcher 用于验证错误响应的匹配器
     */
    public static ResultMatcher expectErrorResponse(int expectedCode, String expectedMessage) {
        return result -> {
            status().isOk().match(result);
            content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON).match(result);
            jsonPath(CODE_JSON_PATH).value(expectedCode).match(result);
            jsonPath(MSG_JSON_PATH).value(expectedMessage).match(result);
        };
    }

    /**
     * 创建一个用于验证接口错误响应的 ResultMatcher
     * <p>
     * 该方法封装了标准的错误响应验证逻辑，包括：
     * <ul>
     *   <li>HTTP 状态码为 200 OK（业务错误通常仍返回 200）</li>
     *   <li>响应内容类型为 application/json</li>
     *   <li>响应 JSON 中 code 字段值为指定的错误码</li>
     *   <li>响应 JSON 中 msg 字段值为指定的错误消息匹配器</li>
     * </ul>
     *
     * <p>使用示例，请参考 {@link #expectErrorResponse(int, String)} 方法。
     *
     * @param expectedCode   期望的错误码
     * @param messageMatcher 期望的错误消息匹配器
     * @return ResultMatcher 用于验证错误响应的匹配器
     */
    public static ResultMatcher expectErrorResponse(int expectedCode, Matcher<String> messageMatcher) {
        return result -> {
            status().isOk().match(result);
            content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON).match(result);
            jsonPath(CODE_JSON_PATH).value(expectedCode).match(result);
            jsonPath(MSG_JSON_PATH).value(messageMatcher).match(result);
        };
    }

} 