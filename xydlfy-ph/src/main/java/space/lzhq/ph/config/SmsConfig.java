package space.lzhq.ph.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信服务配置类
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 短信服务基础URL
     */
    private String baseUrl;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 机构码
     */
    private String company;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 10000;
} 