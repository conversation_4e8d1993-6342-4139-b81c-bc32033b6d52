<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WxPaymentMapper">

    <resultMap type="space.lzhq.ph.domain.WxPayment" id="WxPaymentResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="type" column="type"/>
        <result property="openid" column="openid"/>
        <result property="patientNo" column="patient_no"/>
        <result property="jzCardNo" column="jz_card_no"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="cardNo" column="card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="zyPayNo" column="zy_pay_no"/>
        <result property="amount" column="amount"/>
        <result property="exrefund" column="exrefund"/>
        <result property="unrefund" column="unrefund"/>
        <result property="wxPayNo" column="wx_pay_no"/>
        <result property="wxTradeStatus" column="wx_trade_status"/>
        <result property="hisTradeStatus" column="his_trade_status"/>
        <result property="hisSuccessTime" column="his_success_time"/>
        <result property="hisReceiptNo" column="his_receipt_no"/>
        <result property="balance" column="balance"/>
        <result property="manualPayState" column="manual_pay_state"/>
        <result property="remark" column="remark"/>
        <result property="reservationId" column="reservation_id"/>
        <result property="clinicNo" column="clinic_no"/>
        <result property="prescriptionNo" column="prescription_no"/>
        <result property="testMode" column="test_mode"/>
    </resultMap>

    <sql id="selectWxPaymentVo">
        select id,
        create_time,
        type,
        openid,
        patient_no,
        jz_card_no,
        id_card_no,
        card_no,
        zhuyuan_no,
        zy_pay_no,
        amount,
        exrefund,
        unrefund,
        wx_pay_no,
        wx_trade_status,
        his_trade_status,
        his_success_time,
        his_receipt_no,
        balance,
        manual_pay_state,
        remark,
        reservation_id,
        clinic_no,
        prescription_no,
        test_mode
        from ph_wx_payment
    </sql>

    <select id="selectWxPaymentList" parameterType="WxPayment" resultMap="WxPaymentResult">
        <include refid="selectWxPaymentVo"/>
        <where>
            <if test="patientNo != null  and patientNo != ''">and patient_no = #{patientNo}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="cardNo != null  and cardNo != ''">and card_no = #{cardNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="zyPayNo != null  and zyPayNo != ''">and zy_pay_no = #{zyPayNo}</if>
            <if test="wxPayNo != null  and wxPayNo != ''">and wx_pay_no = #{wxPayNo}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="manualPayState != null">and manual_pay_state = #{manualPayState}</if>
            <if test="wxTradeStatus != null and wxTradeStatus != ''">and wx_trade_status = #{wxTradeStatus}</if>
            <if test="hisTradeStatus != null and hisTradeStatus!= ''">and his_trade_status = #{hisTradeStatus}</if>
            <if test="hisReceiptNo != null and hisReceiptNo != ''">and his_receipt_no = #{hisReceiptNo}</if>
            <if test="reservationId != null and reservationId != ''">and reservation_id = #{reservationId}</if>
            <if test="clinicNo != null and clinicNo != ''">and clinic_no = #{clinicNo}</if>
            <if test="prescriptionNo != null and prescriptionNo != ''">and prescription_no = #{prescriptionNo}</if>
            <if test="testMode != null">and test_mode = #{testMode}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="params.filterErrorOrder != null and params.filterErrorOrder != ''">
                and ((wx_trade_status = '支付成功' or wx_trade_status = '') and his_trade_status != '充值成功' and
                exrefund = 0)
            </if>
            <if test="params.canRefund != null and params.canRefund == true">
                and (unrefund > 0 and remark != '系统清退余额' and test_mode = 0)
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectWxPaymentById" parameterType="Long" resultMap="WxPaymentResult">
        <include refid="selectWxPaymentVo"/>
        where id = #{id}
    </select>

    <select id="selectWxPaymentByZyPayNo" parameterType="String" resultMap="WxPaymentResult">
        <include refid="selectWxPaymentVo"/>
        where zy_pay_no = #{zyPayNo} limit 1
    </select>

    <select id="sumAmount" parameterType="WxPayment" resultType="Long">
        select sum(amount) as sumAmount
        from ph_wx_payment
        where create_time between #{params.beginCreateTime} and #{params.endCreateTime}
        and wx_pay_no != ''
    </select>

    <insert id="insertWxPayment" parameterType="WxPayment" useGeneratedKeys="true" keyProperty="id">
        insert into ph_wx_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="type != null">type,</if>
            <if test="openid != null">openid,</if>
            <if test="patientNo != null">patient_no,</if>
            <if test="jzCardNo != null">jz_card_no,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="cardNo != null">card_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="zyPayNo != null">zy_pay_no,</if>
            <if test="amount != null">amount,</if>
            <if test="exrefund != null">exrefund,</if>
            unrefund,
            <if test="wxPayNo != null">wx_pay_no,</if>
            <if test="wxTradeStatus != null">wx_trade_status,</if>
            <if test="hisTradeStatus != null">his_trade_status,</if>
            <if test="hisSuccessTime != null">his_success_time,</if>
            <if test="hisReceiptNo != null">his_receipt_no,</if>
            <if test="balance != null">balance,</if>
            <if test="manualPayState != null">manual_pay_state,</if>
            <if test="remark != null">remark,</if>
            <if test="reservationId != null">reservation_id,</if>
            <if test="clinicNo != null">clinic_no,</if>
            <if test="prescriptionNo != null">prescription_no,</if>
            <if test="testMode != null">test_mode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="type != null">#{type},</if>
            <if test="openid != null">#{openid},</if>
            <if test="patientNo != null">#{patientNo},</if>
            <if test="jzCardNo != null">#{jzCardNo},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="cardNo != null">#{cardNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="zyPayNo != null">#{zyPayNo},</if>
            <if test="amount != null">#{amount},</if>
            <if test="exrefund != null">#{exrefund},</if>
            #{unrefund},
            <if test="wxPayNo != null">#{wxPayNo},</if>
            <if test="wxTradeStatus != null">#{wxTradeStatus},</if>
            <if test="hisTradeStatus != null">#{hisTradeStatus},</if>
            <if test="hisSuccessTime != null">#{hisSuccessTime},</if>
            <if test="hisReceiptNo != null">#{hisReceiptNo},</if>
            <if test="balance != null">#{balance},</if>
            <if test="manualPayState != null">#{manualPayState},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reservationId != null">#{reservationId},</if>
            <if test="clinicNo != null">#{clinicNo},</if>
            <if test="prescriptionNo != null">#{prescriptionNo},</if>
            <if test="testMode != null">#{testMode},</if>
        </trim>
    </insert>

    <update id="updateWxPayment" parameterType="WxPayment">
        update ph_wx_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="patientNo != null">patient_no = #{patientNo},</if>
            <if test="jzCardNo != null">jz_card_no = #{jzCardNo},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="cardNo != null">card_no = #{cardNo},</if>
            <if test="zhuyuanNo != null">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="zyPayNo != null">zy_pay_no = #{zyPayNo},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="exrefund != null">exrefund = #{exrefund},</if>
            unrefund = #{unrefund},
            <if test="wxPayNo != null">wx_pay_no = #{wxPayNo},</if>
            <if test="wxTradeStatus != null">wx_trade_status = #{wxTradeStatus},</if>
            <if test="hisTradeStatus != null">his_trade_status = #{hisTradeStatus},</if>
            <if test="hisSuccessTime != null">his_success_time = #{hisSuccessTime},</if>
            <if test="hisReceiptNo != null">his_receipt_no = #{hisReceiptNo},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="manualPayState != null">manual_pay_state = #{manualPayState},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reservationId != null">reservation_id = #{reservationId},</if>
            <if test="clinicNo != null">clinic_no = #{clinicNo},</if>
            <if test="prescriptionNo != null">prescription_no = #{prescriptionNo},</if>
            <if test="testMode != null">test_mode = #{testMode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxPaymentById" parameterType="Long">
        delete
        from ph_wx_payment
        where id = #{id}
    </delete>

    <delete id="deleteWxPaymentByIds" parameterType="String">
        delete from ph_wx_payment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
