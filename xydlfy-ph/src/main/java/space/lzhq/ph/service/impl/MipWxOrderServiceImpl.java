package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.mapper.MipWxOrderMapper;
import space.lzhq.ph.service.IMipWxOrderService;

/**
 * 微信医保订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-11
 */
@Service
public class MipWxOrderServiceImpl extends ServiceImpl<MipWxOrderMapper, MipWxOrder> implements IMipWxOrderService {

    @Override
    public MipWxOrder getOneByPayOrderId(String payOrderId) {
        return new LambdaQueryChainWrapper<>(getBaseMapper())
                .eq(MipWxOrder::getPayOrderId, payOrderId)
                .one();
    }

    @Override
    public MipWxOrder getOneByOutTradeNo(String outTradeNo) {
        return new LambdaQueryChainWrapper<>(getBaseMapper())
                .eq(MipWxOrder::getHospitalOutTradeNo, outTradeNo)
                .one();
    }

    public boolean updateTradeStatus(Long id, WxInsurancePayOrderQueryResult orderQueryResult) {
        return lambdaUpdate()
                .set(MipWxOrder::getMedTradeStatus, orderQueryResult.getMedTradeState())
                .set(MipWxOrder::getCashTradeStatus, orderQueryResult.getCashTradeStatus())
                .set(MipWxOrder::getInsuranceTradeStatus, orderQueryResult.getInsuranceTradeStatus())
                .eq(MipWxOrder::getId, id)
                .update();
    }
    
}
