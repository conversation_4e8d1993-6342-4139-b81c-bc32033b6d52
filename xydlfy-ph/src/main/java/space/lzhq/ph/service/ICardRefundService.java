package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.domain.WxTransfer;

import java.util.List;

public interface ICardRefundService extends IService<CardRefund> {

    List<CardRefund> getListByApplicationId(long applicationId);

    boolean existByApplicationIdAndCardNo(long applicationId, String cardNo);

    List<WxTransfer> createWxTransfer(CardRefund cardRefund);

}
