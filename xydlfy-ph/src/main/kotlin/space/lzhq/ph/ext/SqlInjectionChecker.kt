package space.lzhq.ph.ext

import com.alibaba.druid.DbType
import com.alibaba.druid.wall.spi.MySqlWallProvider
import com.alibaba.druid.wall.spi.OracleWallProvider

object SqlInjectionChecker {

    private val mysqlWallProvider = MySqlWallProvider()
    private val oracleWallProvider = OracleWallProvider()

    /**
     * 检查参数是否安全
     * @param param 需要检查的参数
     * @param dbType 数据库类型
     * @return 如果参数安全返回true，否则返回false
     */
    fun isSafe(param: String, dbType: DbType): Boolean {
        val provider = when (dbType) {
            DbType.mysql -> mysqlWallProvider
            DbType.oracle -> oracleWallProvider
            else -> throw IllegalArgumentException("Unsupported database type: $dbType")
        }

        return provider.checkValid("SELECT * FROM test WHERE name = '$param'")
    }

}
