<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.LisReportItemMapper">

    <resultMap id="LisReportItemResult" type="space.lzhq.ph.domain.LisReportItem">
        <result property="inspectionId" column="INSPECTION_ID"/>
        <result property="itemName" column="CHINESE_NAME"/>
        <result property="resultValue" column="QUANTITATIVE_RESULT"/>
        <result property="unit" column="TEST_ITEM_UNIT"/>
        <result property="referenceValue" column="TEST_ITEM_REFERENCE"/>
        <result property="remark" column="REMARK"/>
    </resultMap>

    <sql id="selectLisReportItemVo">
        select INSPECTION_ID, CHINESE_NAME, QUANTITATIVE_RESULT, TEST_ITEM_UNIT, TEST_ITEM_REFERENCE, REMARK
        from XHLIS.VIEW_RESULT_INFO
    </sql>

    <select id="selectLisReportItemsByInspectionId" resultMap="LisReportItemResult">
        <include refid="selectLisReportItemVo"/>
        where INSPECTION_ID = #{inspectionId}
    </select>
</mapper>
