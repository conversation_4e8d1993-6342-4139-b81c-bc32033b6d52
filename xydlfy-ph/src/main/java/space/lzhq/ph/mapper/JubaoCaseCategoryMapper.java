package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.JubaoCaseCategory;

import java.util.List;

/**
 * 问题类型Mapper接口
 */
@Repository
public interface JubaoCaseCategoryMapper {
    /**
     * 查询问题类型
     *
     * @param id 问题类型主键
     * @return 问题类型
     */
    JubaoCaseCategory selectJubaoCaseCategoryById(Long id);

    /**
     * 查询问题类型列表
     *
     * @param jubaoCaseCategory 问题类型
     * @return 问题类型集合
     */
    List<JubaoCaseCategory> selectJubaoCaseCategoryList(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 新增问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    int insertJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 修改问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    int updateJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory);

    /**
     * 删除问题类型
     *
     * @param id 问题类型主键
     * @return 结果
     */
    int deleteJubaoCaseCategoryById(Long id);

    /**
     * 批量删除问题类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteJubaoCaseCategoryByIds(String[] ids);
}
