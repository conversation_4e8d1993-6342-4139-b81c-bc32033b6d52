<!-- Generated by SpecStory -->

# Update regex pattern for keyword validation (2025-06-28 09:11Z)

_**User**_

In xydlfy-ph/src/main/java/space/lzhq/ph/service/impl/DrugServiceImpl.java around lines 29 and 155-163, the current regex pattern restricts keywords to only Chinese characters and English letters, excluding numbers, spaces, and common punctuation. Update the VALID_KEYWORD_PATTERN to include digits, spaces, and relevant punctuation marks commonly found in drug names, such as hyphens or parentheses, to allow more flexible and realistic keyword validation.


---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user wants me to update the regex pattern in the DrugServiceImpl class to be more flexible and realistic for drug names. Currently, the pattern only allows Chinese characters ([\u4e00-\u9fa5]) and English letters ([a-zA-Z]). They want to include:

1. Digits (0-9)
2. Spaces
3. Common punctuation marks found in drug names like hyphens and parentheses

Let me look at the current pattern:
```java
private static final Pattern VALID_KEYWORD_PATTERN = Pattern.compile("^[\u4e00-\u9fa5a-zA-Z]+$");
```

This pattern uses:
- `^` - start of string
- `[\u4e00-\u9fa5a-zA-Z]` - character class with Chinese characters and English letters
- `+` - one or more occurrences
- `$` - end of string

I need to expand this to include:
- Digits: `0-9`
- Spaces: ` ` (space character)
- Hyphens: `-`
- Parentheses: `()`
- Maybe other common punctuation in drug names

For drug names, common punctuation might include:
- Hyphens (-)
- Parentheses ()
- Periods (.)
- Commas (,)
- Forward slashes (/)
- Apostrophes (')

Let me create a more comprehensive pattern. I should also update the comment to reflect the changes.

The new pattern could be:
```java
private static final Pattern VALID_KEYWORD_PATTERN = Pattern.compile("^[\u4e00-\u9fa5a-zA-Z0-9\\s\\-().,'/_]+$");
```

Wait, I need to be careful with escaping. In character classes:
- `\s` matches whitespace
- `\-` or `-` at the end matches hyphen


- Certain characters require special handling in regex character classes

I'll carefully construct the pattern to ensure all desired characters are correctly included while maintaining proper regex syntax. The goal is to create a flexible pattern that captures the complexity of drug name formats, including various languages, numbers, and punctuation marks.

The pattern uses Unicode range for Chinese characters, allows alphanumeric characters, and includes specific punctuation marks with careful placement of special characters like the hyphen. I'll ensure the backslash is properly escaped and the character class is constructed to match the intended input precisely.

The final regex pattern provides a robust validation mechanism for keyword matching across different character types and languages.</details></think>

---

I'll help you update the regex pattern to be more flexible and realistic for drug names. Let me modify the `VALID_KEYWORD_PATTERN` to include digits, spaces, and common punctuation marks found in drug names.

---



---

<think><details><summary>Thought Process</summary>Now I need to update the comment in the validateDrugKeyword method and the error message in the listByKeyword method to reflect the new validation rules.</details></think>

---



---

<think><details><summary>Thought Process</summary>Now I need to update the comment in the validateDrugKeyword method to reflect the new validation rules.</details></think>

---



---

<think><details><summary>Thought Process</summary>Perfect! I've successfully updated the DrugServiceImpl class to make the keyword validation more flexible and realistic for drug names. Let me summarize what I changed:

1. **Updated the regex pattern** from `^[\u4e00-\u9fa5a-zA-Z]+$` to `^[\u4e00-\u9fa5a-zA-Z0-9 ().,'/\\-_]+$`
   - Added `0-9` for digits
   - Added ` ` (space) for spaces
   - Added `().,'/\\-_` for common punctuation marks found in drug names (parentheses, periods, commas, apostrophes, forward slashes, hyphens, underscores)

2. **Updated the comment** for the VALID_KEYWORD_PATTERN to document all the new allowed characters

3. **Updated the error message** in the `listByKeyword` method to reflect the new validation rules

4. **Updated the method comment** for `validateDrugKeyword` to document the new validation rules

5. **Updated the inline comment** in the `validateDrugKeyword` method

The new pattern is much more realistic for drug names, which often contain:
- Numbers (like "5mg", "Vitamin B12")
- Spaces (like "Tylenol Extra Strength")
- Hyphens (like "Co-trimoxazole")
- Parentheses (like "Acetaminophen (Tylenol)")
- Other punctuation for various formatting needs

The changes maintain backward compatibility while allowing more flexible keyword validation that better matches real-world drug naming conventions.</details></think>

---

I've successfully updated the `DrugServiceImpl` class to make the keyword validation more flexible and realistic for drug names. Here's a summary of the changes made:

## Changes Made:

### 1. **Updated the Regex Pattern**
- **Before**: `^[\u4e00-\u9fa5a-zA-Z]+$` (only Chinese characters and English letters)
- **After**: `^[\u4e00-\u9fa5a-zA-Z0-9 ().,'/\\-_]+$`

### 2. **New Allowed Characters**
The pattern now includes:
- **Digits**: `0-9` (for drug dosages like "5mg", "Vitamin B12")
- **Spaces**: ` ` (for multi-word drug names like "Tylenol Extra Strength")
- **Common punctuation**: `().,'/\-_`
  - Parentheses `()` for alternate names like "Acetaminophen (Tylenol)"
  - Periods `.` for abbreviations
  - Commas `,` for compound names
  - Apostrophes `'` for possessive forms
  - Forward slashes `/` for combinations
  - Hyphens `-` for compound drugs like "Co-trimoxazole"
  - Underscores `_` for technical naming

### 3. **Updated Documentation**
- Enhanced the regex pattern comment to document all allowed character types
- Updated the error message to reflect the new validation rules
- Updated the `validateDrugKeyword` method documentation and inline comments

### 4. **Benefits of the Changes**
- **More realistic validation**: Accommodates real-world drug naming conventions
- **Better user experience**: Reduces false validation errors
- **Maintained security**: Still prevents injection attacks while being more permissive
- **Backward compatibility**: Existing valid keywords continue to work

The updated pattern now supports drug names like:
- "阿司匹林 100mg" (Chinese with dosage)
- "Tylenol Extra Strength" (multi-word with spaces)
- "Co-trimoxazole" (hyphenated compounds)
- "Vitamin B-12" (with numbers and hyphens)
- "Acetaminophen (Tylenol)" (with parentheses)

This makes the keyword validation much more practical for pharmaceutical applications while maintaining data integrity and security.

---

