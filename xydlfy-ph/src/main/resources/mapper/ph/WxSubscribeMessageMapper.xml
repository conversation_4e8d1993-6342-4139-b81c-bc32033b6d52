<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WxSubscribeMessageMapper">

    <resultMap type="WxSubscribeMessage" id="WxSubscribeMessageResult">
        <result property="id"    column="id"    />
        <result property="templateId"    column="template_id"    />
        <result property="openid"    column="openid"    />
        <result property="data"    column="data"    />
        <result property="type"    column="type"    />
        <result property="companionId"    column="companion_id"    />
        <result property="sendTime"    column="send_time"    />
        <result property="sendStatus"    column="send_status"    />
        <result property="error"    column="error"    />
    </resultMap>

    <sql id="selectWxSubscribeMessageVo">
        select id, template_id, openid, data, type, companion_id, send_time, send_status, error from ph_wx_subscribe_message
    </sql>

    <select id="selectWxSubscribeMessageList" parameterType="WxSubscribeMessage" resultMap="WxSubscribeMessageResult">
        <include refid="selectWxSubscribeMessageVo"/>
        <where>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="companionId != null  and companionId != ''"> and companion_id = #{companionId}</if>
            <if test="params.beginSendTime != null and params.beginSendTime != '' and params.endSendTime != null and params.endSendTime != ''"> and send_time between #{params.beginSendTime} and #{params.endSendTime}</if>
            <if test="sendStatus != null "> and send_status = #{sendStatus}</if>
        </where>
    </select>

    <select id="selectWxSubscribeMessageById" parameterType="Long" resultMap="WxSubscribeMessageResult">
        <include refid="selectWxSubscribeMessageVo"/>
        where id = #{id}
    </select>

    <insert id="insertWxSubscribeMessage" parameterType="WxSubscribeMessage" useGeneratedKeys="true" keyProperty="id">
        insert into ph_wx_subscribe_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null  and templateId != ''">template_id,</if>
            <if test="openid != null  and openid != ''">openid,</if>
            <if test="data != null  and data != ''">data,</if>
            <if test="type != null  and type != ''">type,</if>
            <if test="companionId != null  and companionId != ''">companion_id,</if>
            <if test="sendTime != null ">send_time,</if>
            <if test="sendStatus != null ">send_status,</if>
            <if test="error != null  and error != ''">error,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null  and templateId != ''">#{templateId},</if>
            <if test="openid != null  and openid != ''">#{openid},</if>
            <if test="data != null  and data != ''">#{data},</if>
            <if test="type != null  and type != ''">#{type},</if>
            <if test="companionId != null  and companionId != ''">#{companionId},</if>
            <if test="sendTime != null ">#{sendTime},</if>
            <if test="sendStatus != null ">#{sendStatus},</if>
            <if test="error != null  and error != ''">#{error},</if>
        </trim>
    </insert>

    <update id="updateWxSubscribeMessage" parameterType="WxSubscribeMessage">
        update ph_wx_subscribe_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateId != null  and templateId != ''">template_id = #{templateId},</if>
            <if test="openid != null  and openid != ''">openid = #{openid},</if>
            <if test="data != null  and data != ''">data = #{data},</if>
            <if test="type != null  and type != ''">type = #{type},</if>
            <if test="companionId != null  and companionId != ''">companion_id = #{companionId},</if>
            <if test="sendTime != null ">send_time = #{sendTime},</if>
            <if test="sendStatus != null ">send_status = #{sendStatus},</if>
            <if test="error != null  and error != ''">error = #{error},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxSubscribeMessageById" parameterType="Long">
        delete from ph_wx_subscribe_message where id = #{id}
    </delete>

    <delete id="deleteWxSubscribeMessageByIds" parameterType="String">
        delete from ph_wx_subscribe_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>