package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.JubaoCase;
import space.lzhq.ph.domain.JubaoCaseCategory;
import space.lzhq.ph.service.IJubaoCaseCategoryService;
import space.lzhq.ph.service.IJubaoCaseService;

import java.util.List;

/**
 * 监督Controller
 */
@Controller
@RequestMapping("/ph/jubaoCase")
public class JubaoCaseAdminController extends BaseController {

    private final IJubaoCaseService jubaoCaseService;
    private final IJubaoCaseCategoryService jubaoCaseCategoryService;

    public JubaoCaseAdminController(IJubaoCaseService jubaoCaseService, IJubaoCaseCategoryService jubaoCaseCategoryService) {
        this.jubaoCaseService = jubaoCaseService;
        this.jubaoCaseCategoryService = jubaoCaseCategoryService;
    }

    @RequiresPermissions("ph:jubaoCase:view")
    @GetMapping()
    public String jubaoCase() {
        return "ph/jubaoCase/jubaoCase";
    }

    @RequiresPermissions("ph:jubaoCase:view")
    @GetMapping("/view/{id}")
    public String view(@PathVariable Long id, ModelMap mmap) {
        JubaoCase jubaoCase = jubaoCaseService.selectJubaoCaseById(id);
        mmap.put("jubaoCase", jubaoCase);

        JubaoCaseCategory category1 = null;
        if (jubaoCase.getCategory1() != null) {
            category1 = jubaoCaseCategoryService.selectJubaoCaseCategoryById(jubaoCase.getCategory1());
        }
        mmap.put("category1", category1 == null ? "" : category1.getName());

        JubaoCaseCategory category2 = null;
        if (jubaoCase.getCategory2() != null) {
            category2 = jubaoCaseCategoryService.selectJubaoCaseCategoryById(jubaoCase.getCategory2());
        }
        mmap.put("category2", category2 == null ? "" : category2.getName());

        return "ph/jubaoCase/view";
    }

    /**
     * 查询监督列表
     */
    @RequiresPermissions("ph:jubaoCase:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(JubaoCase jubaoCase) {
        startPage();
        List<JubaoCase> list = jubaoCaseService.selectJubaoCaseList(jubaoCase);
        return getDataTable(list);
    }

    /**
     * 导出监督列表
     */
    @RequiresPermissions("ph:jubaoCase:export")
    @Log(title = "监督", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(JubaoCase jubaoCase) {
        List<JubaoCase> list = jubaoCaseService.selectJubaoCaseList(jubaoCase);
        ExcelUtil<JubaoCase> util = new ExcelUtil<>(JubaoCase.class);
        return util.exportEasyExcel(list, "监督数据");
    }

    /**
     * 新增监督
     */
    @GetMapping("/add")
    public String add() {
        return "ph/jubaoCase/add";
    }

    /**
     * 新增保存监督
     */
    @RequiresPermissions("ph:jubaoCase:add")
    @Log(title = "监督", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(JubaoCase jubaoCase) {
        return toAjax(jubaoCaseService.insertJubaoCase(jubaoCase));
    }

    /**
     * 修改监督
     */
    @RequiresPermissions("ph:jubaoCase:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        JubaoCase jubaoCase = jubaoCaseService.selectJubaoCaseById(id);
        mmap.put("jubaoCase", jubaoCase);
        return "ph/jubaoCase/edit";
    }

    /**
     * 修改保存监督
     */
    @RequiresPermissions("ph:jubaoCase:edit")
    @Log(title = "监督", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(JubaoCase jubaoCase) {
        return toAjax(jubaoCaseService.updateJubaoCase(jubaoCase));
    }

    /**
     * 删除监督
     */
    @RequiresPermissions("ph:jubaoCase:remove")
    @Log(title = "监督", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(jubaoCaseService.deleteJubaoCaseByIds(ids));
    }

    /**
     * 受理监督
     */
    @RequiresPermissions("ph:jubaoCase:accept")
    @Log(title = "监督", businessType = BusinessType.UPDATE)
    @PostMapping("/accept")
    @ResponseBody
    public AjaxResult accept(String ids) {
        return toAjax(jubaoCaseService.acceptJubaoCaseByIds(ids));
    }

    /**
     * 办结监督
     */
    @RequiresPermissions("ph:jubaoCase:finish")
    @Log(title = "监督", businessType = BusinessType.UPDATE)
    @PostMapping("/finish")
    @ResponseBody
    public AjaxResult finish(String ids) {
        return toAjax(jubaoCaseService.finishJubaoCaseByIds(ids));
    }
}
