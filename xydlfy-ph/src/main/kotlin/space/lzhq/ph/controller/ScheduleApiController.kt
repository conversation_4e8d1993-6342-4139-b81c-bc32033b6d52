package space.lzhq.ph.controller

import com.ruoyi.common.annotation.ValidDepartmentCode
import com.ruoyi.common.annotation.ValidDoctorCode
import com.ruoyi.common.annotation.ValidScheduleId
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.DoctorResponse
import org.mospital.dongruan.bean.ScheduleResponseV1
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.common.Values
import space.lzhq.ph.service.DepartmentKit
import space.lzhq.ph.service.DepartmentKit.getDepartmentsFromHisV1
import space.lzhq.ph.service.DepartmentKit.indexDepartments
import space.lzhq.ph.service.IDepartmentService
import space.lzhq.ph.service.IDoctorService
import java.time.LocalDate

@RestController
@RequestMapping("/open/schedule")
@Validated
class ScheduleApiController {

    @Autowired
    private lateinit var departmentService: IDepartmentService

    @Autowired
    private lateinit var doctorService: IDoctorService

    @GetMapping("indexedDepartments")
    fun indexedDepartments(
        @RequestParam(required = false, defaultValue = "false") forceReload: Boolean,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        if (testMode) {
            val departments = getDepartmentsFromHisV1(testMode = true)
            return AjaxResult.success(indexDepartments(departments))
        }

        if (!forceReload && Values.INDEXED_DEPARTMENTS.isNotEmpty()) {
            return AjaxResult.success(Values.INDEXED_DEPARTMENTS)
        }

        DepartmentKit.sync()
        return AjaxResult.success(Values.INDEXED_DEPARTMENTS)
    }

    /**
     * 查询排班医生
     * @param departmentCode 科室代码
     * @param date 排班日期
     */
    @GetMapping("doctors")
    fun doctors(
        @RequestParam @ValidDepartmentCode departmentCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
    ): AjaxResult {
        val doctorResponse: DoctorResponse =
            DongruanService.getDoctors(deptCode = departmentCode, date = date, testMode = testMode)
        return if (doctorResponse.isOk()) {
            AjaxResult.success(doctorResponse.doctors)
        } else {
            AjaxResult.error(doctorResponse.msg)
        }
    }

    /**
     * 查询排班号源
     * @param departmentCode 科室代码
     * @param doctorCode 医生代码
     * @param date 排班日期
     */
    @GetMapping("tickets/v1")
    fun ticketsV1(
        @RequestParam @ValidDepartmentCode departmentCode: String,
        @RequestParam @ValidDoctorCode doctorCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
    ): AjaxResult {
        val scheduleResponse: ScheduleResponseV1 = DongruanService.getSchedulesV1(
            deptCode = departmentCode,
            doctCode = doctorCode,
            date = date,
            testMode = testMode
        )
        return if (scheduleResponse.isOk()) {
            AjaxResult.success(scheduleResponse.schedules)
        } else {
            AjaxResult.error(scheduleResponse.msg)
        }
    }

    @GetMapping("ticketItems")
    fun ticketItems(
        @RequestParam @ValidScheduleId schid: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
    ): AjaxResult {
        val scheduleItemResponse = DongruanService.getScheduleItems(schid = schid, testMode = testMode)
        val scheduleItems = scheduleItemResponse.scheduleItems.filter { it.isAvailable }
        return if (scheduleItemResponse.isOk()) {
            if (scheduleItems.isNotEmpty()) {
                AjaxResult.success(scheduleItems)
            } else {
                AjaxResult.error("无可用号源或已挂满")
            }
        } else {
            AjaxResult.error(scheduleItemResponse.msg)
        }
    }

    /**
     * 查询医生简介
     */
    @GetMapping("doctor")
    fun doctor(
        @RequestParam @ValidDoctorCode doctorCode: String,
    ): AjaxResult {
        if ("None".equals(doctorCode, ignoreCase = true)) {
            return AjaxResult.error("普通号无医生简介")
        }

        val doctor = doctorService.selectDoctorById(doctorCode)
        return if (doctor != null) {
            AjaxResult.success(doctor)
        } else {
            AjaxResult.error("未找到医生简介")
        }
    }

}
