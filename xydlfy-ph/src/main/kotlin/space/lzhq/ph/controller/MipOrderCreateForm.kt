package space.lzhq.ph.controller

import java.math.BigDecimal

data class MipOrderCreateForm(
    // 门诊号
    val clinicNo: String,
    // 费用明细
    val feeList: List<Fee> = emptyList(),
    // 医保授权码
    val mipAuthCode: String,
    // 个人账户使用标志：0=不使用，1=使用
    val acctUsedFlag: String = "1",
    val testMode: Boolean = false,
) {
    data class Fee(
        val prescriptionNo: String,
        val amount: BigDecimal,
    )

    fun validate(): String? {
        if (clinicNo.isBlank()) {
            return "门诊号不能为空"
        }
        if (feeList.isEmpty()) {
            return "费用明细不能为空"
        }
        if (mipAuthCode.isBlank()) {
            return "医保授权码不能为空"
        }
        return null
    }

    val totalFee: BigDecimal
        get() = feeList.sumOf { it.amount }
}
