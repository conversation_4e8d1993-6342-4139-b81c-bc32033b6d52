create table ph_nat_patient
(
    id          int auto_increment comment '编号'
        primary key,
    department  int                                    null comment '科室',
    name        varchar(20)                            not null comment '姓名',
    id_card_no  varchar(18)                            not null comment '身份证号',
    mobile      varchar(11)                            not null comment '手机号',
    nation      int                                    null comment '民族',
    type        varchar(10)                            null comment '类型',
    days        int                                    null comment '几天一检',
    address     varchar(100) default ''                not null comment '现住址',
    create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    sort_no     int          default 9999              not null comment '排序号',
    constraint nat_patient_id_card_no_uindex
        unique (id_card_no)
)
    comment '人员';

