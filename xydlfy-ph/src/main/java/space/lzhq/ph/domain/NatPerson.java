package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;

/**
 * 人员对象 ph_nat_person
 *
 * <AUTHOR>
 * @date 2021-12-11
 */
public class NatPerson extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 科室
     */
    @Excel(name = "科室", dictType = "nat_department")
    private Integer department;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 民族
     */
    @Excel(name = "民族", dictType = "minzu")
    private Integer nation;

    /**
     * 类型
     */
    @Excel(name = "类型", dictType = "nat_person_type")
    private String type;

    /**
     * 几天一检
     */
    @Excel(name = "几天一检")
    private Long days;

    /**
     * 现住址
     */
    @Excel(name = "现住址")
    private String address;

    /**
     * 排序号
     */
    private Integer sortNo;

    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setNation(Integer nation) {
        this.nation = nation;
    }

    public Integer getNation() {
        return nation;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setDays(Long days) {
        this.days = days;
    }

    public Long getDays() {
        return days;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("idCardNo", getIdCardNo())
                .append("mobile", getMobile())
                .append("nation", getNation())
                .append("type", getType())
                .append("days", getDays())
                .append("address", getAddress())
                .append("createTime", getCreateTime())
                .toString();
    }
}
