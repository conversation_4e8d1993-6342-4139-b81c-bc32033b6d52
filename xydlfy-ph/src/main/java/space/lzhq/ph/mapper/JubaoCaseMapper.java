package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.JubaoCase;

import java.util.List;

/**
 * 监督Mapper接口
 */
@Repository
public interface JubaoCaseMapper {
    /**
     * 查询监督
     *
     * @param id 监督主键
     * @return 监督
     */
    JubaoCase selectJubaoCaseById(Long id);

    /**
     * 查询监督列表
     *
     * @param jubaoCase 监督
     * @return 监督集合
     */
    List<JubaoCase> selectJubaoCaseList(JubaoCase jubaoCase);

    List<JubaoCase> selectListByOpenId(String openId);

    /**
     * 新增监督
     *
     * @param jubaoCase 监督
     * @return 结果
     */
    int insertJubaoCase(JubaoCase jubaoCase);

    /**
     * 修改监督
     *
     * @param jubaoCase 监督
     * @return 结果
     */
    int updateJubaoCase(JubaoCase jubaoCase);

    /**
     * 删除监督
     *
     * @param id 监督主键
     * @return 结果
     */
    int deleteJubaoCaseById(Long id);

    /**
     * 批量删除监督
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteJubaoCaseByIds(String[] ids);

    int acceptJubaoCaseByIds(String[] ids);

    int finishJubaoCaseByIds(String[] ids);

}
