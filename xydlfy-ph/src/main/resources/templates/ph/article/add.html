<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增文章')"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: summernote-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-article-add">
        <input name="categoryName" id="categoryName" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">栏目：</label>
            <div class="col-sm-8">
                <select id="categoryId" name="categoryId" class="form-control m-b" required>
                    <option value="">请选择栏目</option>
                    <!--/*@thymesVar id="categories" type="java.util.List<space.lzhq.ph.domain.Category>"*/-->
                    <option th:each="category:${categories}"
                            th:value="${category.id}"
                            th:text="${category.name}"
                    ></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">标题：</label>
            <div class="col-sm-8">
                <input name="title" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">摘要：</label>
            <div class="col-sm-8">
                <textarea id="summary" name="summary" class="form-control" type="text" maxlength="500"
                          required></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">内容：</label>
            <div class="col-sm-8">
                <input id="content" name="content" type="hidden">
                <div class="summernote"></div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: summernote-js"/>
<script type="text/javascript">
    var prefix = ctx + "ph/article";

    $(function () {
        $('#categoryId').on('change.select2', function (e) {
            var selections = $(this).select2('data');
            if (selections.length === 0) {
                $('#categoryName').val('');
            } else {
                $('#categoryName').val(selections[0]['text']);
            }
        });

        $('.summernote').summernote({
            placeholder: '请输入内容',
            height: 400,
            lang: 'zh-CN',
            followingToolbar: false,
            callbacks: {
                onImageUpload: function (files) {
                    sendFile(files[0], this);
                }
            }
        });

        $("#form-article-add").validate({
            focusCleanup: true
        });
    });

    // 上传文件
    function sendFile(file, obj) {
        var data = new FormData();
        data.append("file", file);
        $.ajax({
            type: "POST",
            url: ctx + "common/upload",
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $(obj).summernote('editor.insertImage', result.url, result.fileName);
                } else {
                    $.modal.alertError(result.msg);
                }
            },
            error: function (error) {
                $.modal.alertWarning("图片上传失败。");
            }
        });
    }

    function submitHandler() {
        if ($.validate.form()) {
            $("#content").val($('.summernote').summernote('code'));
            $.operate.save(prefix + "/add", $('#form-article-add').serialize());
        }
    }
</script>
</body>
</html>