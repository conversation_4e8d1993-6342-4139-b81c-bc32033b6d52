package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.NatSampleSummaryByMixType;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface NatSampleSummaryByMixTypeMapper {

    /**
     * 根据时间段按人数和混合类型统计样品
     *
     * @param minCollectTime 最小采集时间
     * @param maxCollectTime 最大采集时间
     * @return 样品统计结果
     */
    List<NatSampleSummaryByMixType> summary(
            @Param("minCollectTime") Date minCollectTime,
            @Param("maxCollectTime") Date maxCollectTime
    );

    /**
     * 根据时间段按人数和混合类型统计样品
     *
     * @param minCollectTime 最小采集时间
     * @param maxCollectTime 最大采集时间
     * @return 样品统计结果
     */
    List<NatSampleSummaryByMixType> summary1(
            @Param("minCollectTime") Date minCollectTime,
            @Param("maxCollectTime") Date maxCollectTime
    );

}
