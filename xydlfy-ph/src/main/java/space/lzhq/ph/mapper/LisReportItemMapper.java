package space.lzhq.ph.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.LisReportItem;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@DataSource(value = DataSourceType.LIS)
public interface LisReportItemMapper {

    /**
     * 根据报告单号查询报告项
     *
     * @return 报告项
     */
    List<LisReportItem> selectLisReportItemsByInspectionId(@Param("inspectionId") String inspectionId);

}
