<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
  <th:block th:include="include :: header('采样日报列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
  <div class="row">
    <div class="col-sm-12 search-collapse">
      <form id="formId">
        <div class="select-list">
          <ul>
            <li class="select-time">
              <label>日期：</label>
              <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginDay]"/>
              <span>-</span>
              <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endDay]"/>
            </li>
            <li>
              <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
              <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
            </li>
          </ul>
        </div>
      </form>
    </div>

    <div class="btn-group-sm" id="toolbar" role="group">
      <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:natSampleSummary:add">
        <i class="fa fa-plus"></i> 添加
      </a>
      <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
         shiro:hasPermission="ph:natSampleSummary:edit">
        <i class="fa fa-edit"></i> 修改
      </a>
      <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
         shiro:hasPermission="ph:natSampleSummary:remove">
        <i class="fa fa-remove"></i> 删除
      </a>
      <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:natSampleSummary:export">
        <i class="fa fa-download"></i> 导出
      </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
      <table id="bootstrap-table"></table>
    </div>
  </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
  var editFlag = [[${@permission.hasPermi('ph:natSampleSummary:edit')}]];
  var removeFlag = [[${@permission.hasPermi('ph:natSampleSummary:remove')}]];
  var viewWeijianFlag = [[${@permission.hasPermi('ph:natPerson:weijian')}]];
  var prefix = ctx + "ph/natSampleSummary";

  $(function () {
    var options = {
      url: prefix + "/list",
      createUrl: prefix + "/add",
      updateUrl: prefix + "/edit/{id}",
      removeUrl: prefix + "/remove",
      exportUrl: prefix + "/export",
      modalName: "采样日报",
      columns: [{
        checkbox: true
      },
        {
          field: 'id',
          title: 'ID',
          visible: false
        },
        {
          field: 'day',
          title: '日期'
        },
        {
          field: 'yingCai',
          title: '应采'
        },
        {
          field: 'shiCai',
          title: '实采'
        },
        {
          field: 'weiCai',
          title: '未采',
          formatter: function (value, row, index) {
            if (Number(value) > 0) {
              return '<a class="btn btn-danger btn-xs ' + viewWeijianFlag + '" href="javascript:void(0)" onclick="viewWeijian(\'' + row.day + '\')">' + value + '</a>';
            } else {
              return value;
            }
          }
        },
        {
          field: 'pushOk',
          title: '推送成功'
        },
        {
          field: 'pushFail',
          title: '推送失败'
        },
        {
          field: 'pushTodo',
          title: '待推送'
        },
      ]
    };
    $.table.init(options);
  });

  function viewWeijian(date) {
    var url = '/ph/natPerson/weijian?date=' + date;
    $.modal.openTab("应检未检人员表（" + date + "）", url);
  }
</script>
</body>
</html>