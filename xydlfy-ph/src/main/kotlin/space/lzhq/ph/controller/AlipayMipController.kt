package space.lzhq.ph.controller

import com.alibaba.fastjson.JSONObject
import com.alipay.api.domain.MedicalNationalPayAuthInfo
import com.alipay.api.response.*
import com.ruoyi.common.constant.Constants.CITY_CODE_PREFIX
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.dromara.hutool.core.date.DatePattern
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.alipay.GrantType
import org.mospital.alipay.InsuredCity
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.AvailableRegistrationResponseV1
import org.mospital.dongruan.bean.PrescriptionDetailResponse
import org.mospital.dongruan.bean.PrescriptionResponse
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.*
import org.mospital.jackson.JacksonKit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.controller.MipOrderCreateForm.Fee
import space.lzhq.ph.domain.MipAlipayOrder
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IMipAlipayOrderService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/api/alipayMip")
class AlipayMipController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var mipAlipayOrderService: IMipAlipayOrderService

    private fun getEnvByTestMode(testMode: Boolean): String {
        return if (testMode) "AlipayTest" else "AlipayProd"
    }

    /**
     * 获取待结算费用清单
     */
    @GetMapping("/pendingSettlements/v1")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlementsV1(
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        if (!testMode && !sysConfigService.getBoolean("bool-zfbxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val availableRegistrationResponse: AvailableRegistrationResponseV1 =
            DongruanService.queryAvailableRegistrationsV1(cardNo = currentPatient.cardNo, testMode = testMode)
        if (!availableRegistrationResponse.isOk()) {
            return AjaxResult.error(availableRegistrationResponse.msg)
        }

        val minRegisterTime = LocalDate.now().minusDays(2).atStartOfDay()
            .format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN))
        val availableRegistrations: List<AvailableRegistrationResponseV1.Registration> =
            availableRegistrationResponse.registrations.filter {
                it.registerTime >= minRegisterTime
            }
        availableRegistrations.forEach { registration ->
            val prescriptionResponse: PrescriptionResponse =
                DongruanService.queryPrescriptions(
                    clinicNo = registration.clinicNo,
                    testMode = testMode
                )
            if (prescriptionResponse.isOk()) {
                registration.prescriptions = prescriptionResponse.prescriptions
                    .filter {
                        // 返回未收费、非慢病、非急诊的处方
                        !it.isCharged() && !it.isChronic() && !it.isEmergency()
                    }
                    .onEach { it.registerTime = registration.registerTime }
                    .toMutableList()
            } else {
                logger.warn("Failed to query prescriptions for clinicNo=${registration.clinicNo}")
            }
        }
        return AjaxResult.success(availableRegistrations.filter { it.prescriptions.isNotEmpty() })
    }

    /**
     * 获取待结算费用明细
     */
    @GetMapping("/prescriptionDetail")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPrescriptionDetail(
        @RequestParam clinicNo: String,
        @RequestParam prescriptionNo: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val prescriptionDetailResponse: PrescriptionDetailResponse = DongruanService.queryPrescriptionDetails(
            clinicNo = clinicNo,
            prescriptionNo = prescriptionNo,
            testMode = testMode
        )
        return if (prescriptionDetailResponse.isOk()) {
            AjaxResult.success(prescriptionDetailResponse.prescriptionDetails)
        } else {
            AjaxResult.error(prescriptionDetailResponse.msg)
        }
    }

    /**
     * 支付宝授权
     * 小程序获取用户授权时，确保 scopes 为 ['nhsamp', 'auth_user']
     * 参见：https://opendocs.alipay.com/pre-open/02iqci
     */
    @PostMapping("/authAlipay")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun authAlipay(
        @RequestParam authCode: String,
    ): AjaxResult {
        val oauthTokenResponse: AlipaySystemOauthTokenResponse = AlipayService.getOrRefreshAccessToken(
            authCode = authCode,
            grantType = GrantType.AUTHORIZATION_CODE
        )
        if (!oauthTokenResponse.isSuccess) {
            return AjaxResult.error(oauthTokenResponse.subCode + "-" + oauthTokenResponse.subMsg)
        }

        val infoShareResponse: AlipayUserInfoShareResponse =
            AlipayService.getUserInfoShare(oauthTokenResponse.accessToken)
        logger.debug("AlipayUserInfoShareResponse：${JacksonKit.writeValueAsString(infoShareResponse)}")
        if (!infoShareResponse.isSuccess) {
            return AjaxResult.error(infoShareResponse.subCode + "-" + infoShareResponse.subMsg)
        }
        if (infoShareResponse.isCertified != "T") {
            return AjaxResult.error("您的支付宝账户未通过实名认证")
        }

        val mipAlipayOrder = MipAlipayOrder(authCode, oauthTokenResponse.accessToken, infoShareResponse)
        if (infoShareResponse.certNo != request.getCurrentPatient().idCardNo) {
            // 支付宝账户与当前就诊人身份不一致，可能是使用亲情账户
            mipAlipayOrder.userName = request.getCurrentPatient().name
            mipAlipayOrder.certNo = request.getCurrentPatient().idCardNo
        }
        mipAlipayOrderService.save(mipAlipayOrder)
        return AjaxResult.success()
    }


    /**
     * 支付宝医保授权
     */
    @PostMapping("/authAlipayMip")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun authAlipayMip(
        @RequestParam authCode: String,
        @RequestParam latitude: String,
        @RequestParam longitude: String,
        @RequestParam(required = false) callbackPage: String?
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder? = mipAlipayOrderService.getOneByAuthCode(authCode)
        if (mipAlipayOrder == null || mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("获取支付宝授权失败")
        }

        val medicalAuthinfoAuthQueryResponse: AlipayCommerceMedicalAuthinfoAuthQueryResponse =
            AlipayService.commerceMedicalAuthInfoQuery(
                alipayUserId = mipAlipayOrder.userId,
                accessToken = mipAlipayOrder.accessToken,
                patientName = mipAlipayOrder.userName,
                patientCertNo = mipAlipayOrder.certNo,
                callbackPage = callbackPage
            )
        if (!medicalAuthinfoAuthQueryResponse.isSuccess) {
            return AjaxResult.error("${medicalAuthinfoAuthQueryResponse.subCode}: ${medicalAuthinfoAuthQueryResponse.subMsg}")
        }
        val medicalNationalPayAuthInfo: MedicalNationalPayAuthInfo = medicalAuthinfoAuthQueryResponse.data
        if (medicalNationalPayAuthInfo.authStas != "1") {
            return if (!medicalNationalPayAuthInfo.authUrl.isNullOrBlank()) {
                AjaxResult.success(
                    mapOf(
                        "state" to "MIP_AUTH_REDIRECT",
                        "url" to medicalNationalPayAuthInfo.authUrl
                    )
                )
            } else {
                AjaxResult.success(
                    mapOf(
                        "state" to "fail",
                        "msg" to "用户授权状态异常：${medicalNationalPayAuthInfo.authStas}"
                    )
                )
            }
        }

        // 获取电子医保凭证信息，主要用于获取参保地信息
        val cardInfoQuery: AlipayCommerceMedicalCardAuthQueryResponse =
            AlipayService.commerceMedicalCardAuthQuery(mipAlipayOrder.accessToken)
        if (!cardInfoQuery.isSuccess) {
            return AjaxResult.error("支付宝电子医保凭证信息获取失败：${cardInfoQuery.subMsg}")
        }
        if (cardInfoQuery.data?.status == "NOT_BIND" || cardInfoQuery.data?.insuredStatus == "NOT_INSURED") {
            return AjaxResult.error("您的支付宝账户未激活电子医保凭证")
        }

        val cities: List<InsuredCity> = AlipayService.parseInsuredCities(cardInfoQuery)
        var city = cities.firstOrNull { it.code.startsWith(CITY_CODE_PREFIX) }
        if (city == null) {
            city = cities.firstOrNull { it.isDefaultCity() }
        }
        if (city == null) {
            city = cities.firstOrNull()
        }
        if (city == null) {
            return AjaxResult.error("未查询到医保参保地信息")
        }
        if (!city.code.startsWith("65")) {
            return AjaxResult.error("目前仅支持新疆维吾尔自治区区医保，您当前的参保地为：${city.name}")
        }

        mipAlipayOrder.payAuthNo = medicalNationalPayAuthInfo.payAuthNo
        mipAlipayOrder.medicalCardInstId = medicalNationalPayAuthInfo.medicalCardInstId
        mipAlipayOrder.medicalCardId = medicalNationalPayAuthInfo.medicalCardId
        mipAlipayOrder.latitude = latitude
        mipAlipayOrder.longitude = longitude
        mipAlipayOrder.cityId = city.code
        mipAlipayOrderService.updateById(mipAlipayOrder)

        return AjaxResult.success(
            mapOf(
                "state" to "ok"
            )
        )
    }

    private fun doCreateOrder(form: MipOrderCreateForm): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val validateResult = form.validate()
        if (validateResult != null) {
            return AjaxResult.error(validateResult)
        }

        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder? = mipAlipayOrderService.getOneByAuthCode(form.mipAuthCode)
        if (mipAlipayOrder == null || mipAlipayOrder.userId != currentPatient.openId || mipAlipayOrder.payAuthNo.isNullOrBlank()) {
            return AjaxResult.error("获取支付宝授权失败")
        }

        val fees: List<CreateOrderForm.Fee> = form.feeList.stream().distinct().map {
            CreateOrderForm.Fee(
                prescriptionNo = it.prescriptionNo,
                amount = it.amount,
            )
        }.toList()
        val createOrderResponse: Response<CreateOrderResult> = DongruanYibaoService.createOrder(
            form = CreateOrderForm(
                clinicNo = form.clinicNo,
                feeList = fees,
                totalFee = form.totalFee,
                payAuthNo = mipAlipayOrder.payAuthNo!!,
                ecToken = "",
                personName = mipAlipayOrder.userName!!,
                idCardNo = mipAlipayOrder.certNo!!,
                insuCode = mipAlipayOrder.cityId!!,
                medType = "11",
                acctUsedFlag = form.acctUsedFlag,
                latlnt = mipAlipayOrder.latitude + "," + mipAlipayOrder.longitude
            ),
            thirdType = ThirdType.ALIPAY,
            extendParams = Request.ExtendParams(appId = AlipaySetting.mipOrgAppId),
            env = getEnvByTestMode(form.testMode)
        )
        return if (createOrderResponse.isOk()) {
            val preSettlement: CreateOrderResult.PreSettlement = createOrderResponse.data!!.preSettlement
            mipAlipayOrder.clinicNo = form.clinicNo
            mipAlipayOrder.feesJson = JacksonKit.writeValueAsString(fees)
            mipAlipayOrder.payToken = createOrderResponse.data!!.payToken
            mipAlipayOrder.medOrgOrd = createOrderResponse.data!!.medOrgOrd
            mipAlipayOrder.payOrderId = preSettlement.payOrderId
            mipAlipayOrder.feeSumAmount = preSettlement.feeSumamt
            mipAlipayOrder.ownPayAmount = preSettlement.ownPayAmt
            mipAlipayOrder.personalAccountPayAmount = preSettlement.psnAcctPay
            mipAlipayOrder.fundPayAmount = preSettlement.fundPay
            mipAlipayOrder.testMode = form.testMode
            mipAlipayOrderService.updateById(mipAlipayOrder)
            AjaxResult.success(createOrderResponse.data!!)
        } else {
            AjaxResult.error(createOrderResponse.message)
        }
    }

    /**
     * 创建订单
     * @param acctUsedFlag 个人账户使用标志：0=不使用，1=使用
     */
    @PostMapping("createOrder")
    @RequireSession(clientType = space.lzhq.ph.common.ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrder(
        @RequestParam clinicNo: String,
        @RequestParam prescriptionNo: String,
        @RequestParam amount: BigDecimal,
        @RequestParam authCode: String,
        @RequestParam(required = false, defaultValue = "1") acctUsedFlag: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val form = MipOrderCreateForm(
            clinicNo = clinicNo,
            feeList = listOf(Fee(prescriptionNo = prescriptionNo, amount = amount)),
            mipAuthCode = authCode,
            acctUsedFlag = acctUsedFlag,
            testMode = testMode
        )
        return doCreateOrder(form)
    }

    @PostMapping("createOrder/v1")
    @RequireSession(clientType = space.lzhq.ph.common.ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrderV1(@RequestBody form: MipOrderCreateForm): AjaxResult {
        return doCreateOrder(form)
    }

    /**
     * 支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @PostMapping("payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun payOrder(
        @RequestParam payOrderId: String,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        try {
            val outTradeNo = PaymentKit.newOrderId(ServiceType.YB)
            val createOrderResponse: AlipayTradeAppPayResponse = AlipayService.createMipOrder(
                outTradeNo = outTradeNo,
                // 此处必须传入总金额，而不是自费金额
                totalAmount = mipAlipayOrder.feeSumAmount,
                medOrgOrd = mipAlipayOrder.medOrgOrd,
                payOrderId = mipAlipayOrder.payOrderId,
                payAuthNo = mipAlipayOrder.payAuthNo,
                medicalCardInstId = mipAlipayOrder.medicalCardInstId,
                medicalCardId = mipAlipayOrder.medicalCardId,
                subject = AlipaySetting.mipOrgName + "-" + ServiceType.YB.description,
                medicalRequestContent = JSONObject()
            )
            return if (createOrderResponse.isSuccess) {
                mipAlipayOrder.outTradeNo = outTradeNo
                mipAlipayOrder.cashOrderCreateTime = LocalDateTime.now()
                mipAlipayOrderService.updateById(mipAlipayOrder)

                AjaxResult.success("ok", createOrderResponse.body)
            } else {
                AjaxResult.error("下单失败：${createOrderResponse.subCode}-${createOrderResponse.subMsg}")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("下单失败：${e.message}")
        }
    }

    /**
     * 查询订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @GetMapping("queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOrder(
        @RequestParam payOrderId: String
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        // 如果已经有hisInvoiceNo，说明已经结算成功
        if (!mipAlipayOrder.hisInvoiceNo.isNullOrBlank()) {
            val queryOrderResponse: AlipayTradeQueryResponse =
                AlipayService.queryOrder(
                    outTradeNo = mipAlipayOrder.outTradeNo,
                    queryOptions = listOf("fund_bill_list")
                )
            return if (queryOrderResponse.isSuccess) {
                AjaxResult.success(queryOrderResponse)
            } else {
                AjaxResult.error("${queryOrderResponse.subCode}-${queryOrderResponse.subMsg}")
            }
        }

        try {
            val queryOrderResponse: AlipayTradeQueryResponse =
                AlipayService.queryOrder(
                    outTradeNo = mipAlipayOrder.outTradeNo,
                    queryOptions = listOf("fund_bill_list")
                )
            if (queryOrderResponse.tradeStatus == "WAIT_BUYER_PAY") {
                return AjaxResult.error("RETRY_REQUIRED")
            }
            if (queryOrderResponse.tradeStatus != "TRADE_SUCCESS") {
                return AjaxResult.success(queryOrderResponse)
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("RETRY_REQUIRED")
        }

        val yibaoOrderStatusResponse: Response<QueryOrderResult> = DongruanYibaoService.queryOrder(
            form = QueryOrderForm(
                clinicNo = mipAlipayOrder.clinicNo,
                payOrderId = mipAlipayOrder.payOrderId,
                personName = mipAlipayOrder.userName,
                idCardNo = mipAlipayOrder.certNo
            ),
            thirdType = ThirdType.ALIPAY,
            extendParams = Request.ExtendParams(appId = AlipaySetting.mipOrgAppId),
            env = getEnvByTestMode(mipAlipayOrder.testMode)
        )

        return if (yibaoOrderStatusResponse.isOk()) {
            if (yibaoOrderStatusResponse.data?.orderStatus == "6") {
                try {
                    HisExt.settleAlipayMipOrder(mipAlipayOrder.outTradeNo)
                    val queryOrderResponse: AlipayTradeQueryResponse =
                        AlipayService.queryOrder(
                            outTradeNo = mipAlipayOrder.outTradeNo,
                            queryOptions = listOf("fund_bill_list")
                        )
                    AjaxResult.success(queryOrderResponse)
                } catch (e: Exception) {
                    logger.debug(e.message, e)
                    AjaxResult.error(e.message)
                }
            } else {
                AjaxResult.error("RETRY_REQUIRED")
            }
        } else {
            if (mipAlipayOrder.ownPayAmount > BigDecimal.ZERO) {
                CoroutineScope(Dispatchers.IO).launch {
                    AlipayExt.refundAlipay(mipAlipayOrder)
                }
                AjaxResult.error("HIS执行失败，将自动退还您的钱款")
            } else {
                AjaxResult.error("HIS执行失败，请稍后重试")
            }
        }
    }

    data class RefundOrderForm(
        val payOrderId: String,
    )

    @PostMapping("refundOrder")
    fun refundOrder(
        @RequestBody form: RefundOrderForm,
    ): AjaxResult {
        try {
            val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(form.payOrderId)
                ?: return AjaxResult.error("订单不存在")
            val refundResponse: AlipayTradeRefundApplyResponse = AlipayExt.refundAlipay(mipAlipayOrder)
            return if (refundResponse.isSuccess) {
                AjaxResult.success(refundResponse)
            } else {
                AjaxResult.error(refundResponse.subCode + ":" + refundResponse.subMsg)
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("退款失败：${e.message}")
        }
    }
}
