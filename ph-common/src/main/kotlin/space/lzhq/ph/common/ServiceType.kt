package space.lzhq.ph.common

enum class ServiceType(val code: Int, val description: String) {
    /**
     * 门诊预交金
     */
    MZ(1, "门诊预交金"),

    /**
     * 住院预交金
     */
    ZY(2, "住院预交金"),


    /**
     * 诊间缴费
     */
    ZJ(3, "诊间缴费"),

    /**
     * 医保移动支付
     */
    YB(4, "医保移动支付"),

    /**
     * 挂号
     */
    GH(5, "挂号"),

    /**
     * 余额清退
     */
    TF(6, "余额清退"),

    /**
     * 余额清退（系统主动）
     */
    QT(7, "余额清退"),
    ;

    companion object {
        private val map = values().associateBy { it.code }
        fun fromCode(code: Int): ServiceType? = map[code]
    }

}
