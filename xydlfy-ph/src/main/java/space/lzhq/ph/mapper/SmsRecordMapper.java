package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import space.lzhq.ph.domain.SmsRecord;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 短信发送记录Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SmsRecordMapper extends BaseMapper<SmsRecord> {

    /**
     * 根据业务类型和业务ID查询短信记录
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 短信记录列表
     */
    default List<SmsRecord> selectByBusinessTypeAndId(String businessType, Long businessId) {
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        if (StringUtils.isBlank(businessType)) {
            return Collections.emptyList();
        }

        if (businessId != null) {
            wrapper.eq(SmsRecord::getBusinessId, businessId);
        }
        wrapper.eq(SmsRecord::getBusinessType, businessType);

        wrapper.orderByDesc(SmsRecord::getCreateTime);
        return selectList(wrapper);
    }

    /**
     * 根据手机号码和时间范围查询短信记录
     *
     * @param mobile    手机号码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 短信记录列表
     */
    default List<SmsRecord> selectByMobileAndTimeRange(String mobile, LocalDateTime startTime, LocalDateTime endTime) {
        if (StringUtils.isBlank(mobile)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SmsRecord::getMobile, mobile);
        if (startTime != null) {
            wrapper.ge(SmsRecord::getSendTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(SmsRecord::getSendTime, endTime);
        }
        wrapper.orderByDesc(SmsRecord::getSendTime);
        return selectList(wrapper);
    }

    /**
     * 根据发送状态查询短信记录
     *
     * @param sendStatus 发送状态
     * @param limit      限制数量
     * @return 短信记录列表
     */
    default List<SmsRecord> selectBySendStatus(Integer sendStatus, Integer limit) {
        if (sendStatus == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SmsRecord::getSendStatus, sendStatus);
        wrapper.orderByAsc(SmsRecord::getCreateTime);
        if (limit != null && limit > 0) {
            Page<SmsRecord> page = new Page<>(1, limit);
            return selectPage(page, wrapper).getRecords();
        }
        return selectList(wrapper);
    }

    /**
     * 统计指定时间范围内的短信发送情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    default Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        if (startTime != null) {
            wrapper.ge(SmsRecord::getSendTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(SmsRecord::getSendTime, endTime);
        }
        return selectCount(wrapper);
    }

    /**
     * 根据短信ID查询记录
     *
     * @param sismsid 短信ID
     * @return 短信记录
     */
    default SmsRecord selectBySismsid(String sismsid) {
        if (StringUtils.isBlank(sismsid)) {
            return null;
        }
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SmsRecord::getSismsid, sismsid);
        return selectOne(wrapper);
    }
}