package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NatCollection;
import space.lzhq.ph.mapper.NatCollectionMapper;
import space.lzhq.ph.service.INatCollectionService;

import java.util.Date;
import java.util.List;

@Service
public class NatCollectionServiceImpl implements INatCollectionService {

    private final NatCollectionMapper mapper;

    public NatCollectionServiceImpl(NatCollectionMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<NatCollection> selectNatCollections(String idCardNo, Date minCollectTime) {
        return mapper.selectNatCollections(idCardNo, minCollectTime);
    }

}
