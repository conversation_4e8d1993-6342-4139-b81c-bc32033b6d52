package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.core.text.Convert;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.JubaoCaseCategory;
import space.lzhq.ph.mapper.JubaoCaseCategoryMapper;
import space.lzhq.ph.service.IJubaoCaseCategoryService;

import java.util.ArrayList;
import java.util.List;

/**
 * 问题类型Service业务层处理
 */
@Service
public class JubaoCaseCategoryServiceImpl implements IJubaoCaseCategoryService {

    private final JubaoCaseCategoryMapper jubaoCaseCategoryMapper;

    public JubaoCaseCategoryServiceImpl(JubaoCaseCategoryMapper jubaoCaseCategoryMapper) {
        this.jubaoCaseCategoryMapper = jubaoCaseCategoryMapper;
    }

    /**
     * 查询问题类型
     *
     * @param id 问题类型主键
     * @return 问题类型
     */
    @Override
    public JubaoCaseCategory selectJubaoCaseCategoryById(Long id) {
        return jubaoCaseCategoryMapper.selectJubaoCaseCategoryById(id);
    }

    /**
     * 查询问题类型列表
     *
     * @param jubaoCaseCategory 问题类型
     * @return 问题类型
     */
    @Override
    public List<JubaoCaseCategory> selectJubaoCaseCategoryList(JubaoCaseCategory jubaoCaseCategory) {
        return jubaoCaseCategoryMapper.selectJubaoCaseCategoryList(jubaoCaseCategory);
    }

    /**
     * 新增问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    @Override
    public int insertJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory) {
        return jubaoCaseCategoryMapper.insertJubaoCaseCategory(jubaoCaseCategory);
    }

    /**
     * 修改问题类型
     *
     * @param jubaoCaseCategory 问题类型
     * @return 结果
     */
    @Override
    public int updateJubaoCaseCategory(JubaoCaseCategory jubaoCaseCategory) {
        return jubaoCaseCategoryMapper.updateJubaoCaseCategory(jubaoCaseCategory);
    }

    /**
     * 批量删除问题类型
     *
     * @param ids 需要删除的问题类型主键
     * @return 结果
     */
    @Override
    public int deleteJubaoCaseCategoryByIds(String ids) {
        return jubaoCaseCategoryMapper.deleteJubaoCaseCategoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除问题类型信息
     *
     * @param id 问题类型主键
     * @return 结果
     */
    @Override
    public int deleteJubaoCaseCategoryById(Long id) {
        return jubaoCaseCategoryMapper.deleteJubaoCaseCategoryById(id);
    }

    /**
     * 查询问题类型树列表
     *
     * @return 所有问题类型信息
     */
    @Override
    public List<Ztree> selectJubaoCaseCategoryTree() {
        List<JubaoCaseCategory> jubaoCaseCategoryList = jubaoCaseCategoryMapper.selectJubaoCaseCategoryList(new JubaoCaseCategory());
        List<Ztree> ztrees = new ArrayList<Ztree>();

        Ztree root = new Ztree();
        root.setId(0L);
        root.setpId(null);
        root.setName("无");
        root.setTitle("无");
        ztrees.add(root);

        for (JubaoCaseCategory jubaoCaseCategory : jubaoCaseCategoryList) {
            if (!jubaoCaseCategory.isLevel1()) {
                continue;
            }
            Ztree ztree = new Ztree();
            ztree.setId(jubaoCaseCategory.getId());
            ztree.setpId(jubaoCaseCategory.getParentId());
            ztree.setName(jubaoCaseCategory.getName());
            ztree.setTitle(jubaoCaseCategory.getName());
            ztrees.add(ztree);
        }
        return ztrees;
    }
}
