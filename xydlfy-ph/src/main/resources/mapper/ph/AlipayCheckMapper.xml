<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.AlipayCheckMapper">

    <resultMap type="AlipayCheck" id="AlipayCheckResult">
        <result property="id" column="id"/>
        <result property="alipayPayAmount" column="alipay_pay_amount"/>
        <result property="alipayMzPayAmount" column="alipay_mz_pay_amount"/>
        <result property="alipayZyPayAmount" column="alipay_zy_pay_amount"/>
        <result property="hisPayAmount" column="his_pay_amount"/>
        <result property="diffPayAmount" column="diff_pay_amount"/>
        <result property="payBalanced" column="pay_balanced"/>
        <result property="alipayRefundAmount" column="alipay_refund_amount"/>
        <result property="alipayMzRefundAmount" column="alipay_mz_refund_amount"/>
        <result property="alipayZyRefundAmount" column="alipay_zy_refund_amount"/>
        <result property="hisRefundAmount" column="his_refund_amount"/>
        <result property="diffRefundAmount" column="diff_refund_amount"/>
        <result property="refundBalanced" column="refund_balanced"/>
        <result property="alipayNetIn" column="alipay_net_in"/>
        <result property="hisNetIn" column="his_net_in"/>
        <result property="mipPayAmount" column="mip_pay_amount"/>
        <result property="mipRefundAmount" column="mip_refund_amount"/>
        <result property="mipNetIn" column="mip_net_in"/>
        <result property="totalNetIn" column="total_net_in"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAlipayCheckVo">
        select id,
        alipay_pay_amount,
        alipay_mz_pay_amount,
        alipay_zy_pay_amount,
        his_pay_amount,
        diff_pay_amount,
        pay_balanced,
        alipay_refund_amount,
        alipay_mz_refund_amount,
        alipay_zy_refund_amount,
        his_refund_amount,
        diff_refund_amount,
        refund_balanced,
        alipay_net_in,
        his_net_in,
        mip_pay_amount,
        mip_refund_amount,
        mip_net_in,
        total_net_in,
        remark
        from alipay_check
    </sql>

    <select id="selectAlipayCheckList" parameterType="AlipayCheck" resultMap="AlipayCheckResult">
        <include refid="selectAlipayCheckVo"/>
        <where>
            <if test="params.minId != null and params.minId != ''">
                and id >= #{params.minId}
            </if>
            <if test="params.maxId != null and params.maxId != ''">
                and id &lt;= #{params.maxId}
            </if>
            <if test="id != null ">and id = #{id}</if>
            <if test="payBalanced != null ">and pay_balanced = #{payBalanced}</if>
            <if test="refundBalanced != null ">and refund_balanced = #{refundBalanced}</if>
        </where>
    </select>

    <select id="selectAlipayCheckById" parameterType="Long" resultMap="AlipayCheckResult">
        <include refid="selectAlipayCheckVo"/>
        where id = #{id}
    </select>

    <insert id="insertAlipayCheck" parameterType="AlipayCheck">
        insert into alipay_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="alipayPayAmount != null">alipay_pay_amount,</if>
            <if test="alipayMzPayAmount != null">alipay_mz_pay_amount,</if>
            <if test="alipayZyPayAmount != null">alipay_zy_pay_amount,</if>
            <if test="hisPayAmount != null">his_pay_amount,</if>
            <if test="diffPayAmount != null">diff_pay_amount,</if>
            <if test="payBalanced != null">pay_balanced,</if>
            <if test="alipayRefundAmount != null">alipay_refund_amount,</if>
            <if test="alipayMzRefundAmount != null">alipay_mz_refund_amount,</if>
            <if test="alipayZyRefundAmount != null">alipay_zy_refund_amount,</if>
            <if test="hisRefundAmount != null">his_refund_amount,</if>
            <if test="diffRefundAmount != null">diff_refund_amount,</if>
            <if test="refundBalanced != null">refund_balanced,</if>
            <if test="alipayNetIn != null">alipay_net_in,</if>
            <if test="hisNetIn != null">his_net_in,</if>
            <if test="mipPayAmount != null">mip_pay_amount,</if>
            <if test="mipRefundAmount != null">mip_refund_amount,</if>
            <if test="mipNetIn != null">mip_net_in,</if>
            <if test="totalNetIn != null">total_net_in,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="alipayPayAmount != null">#{alipayPayAmount},</if>
            <if test="alipayMzPayAmount != null">#{alipayMzPayAmount},</if>
            <if test="alipayZyPayAmount != null">#{alipayZyPayAmount},</if>
            <if test="hisPayAmount != null">#{hisPayAmount},</if>
            <if test="diffPayAmount != null">#{diffPayAmount},</if>
            <if test="payBalanced != null">#{payBalanced},</if>
            <if test="alipayRefundAmount != null">#{alipayRefundAmount},</if>
            <if test="alipayMzRefundAmount != null">#{alipayMzRefundAmount},</if>
            <if test="alipayZyRefundAmount != null">#{alipayZyRefundAmount},</if>
            <if test="hisRefundAmount != null">#{hisRefundAmount},</if>
            <if test="diffRefundAmount != null">#{diffRefundAmount},</if>
            <if test="refundBalanced != null">#{refundBalanced},</if>
            <if test="alipayNetIn != null">#{alipayNetIn},</if>
            <if test="hisNetIn != null">#{hisNetIn},</if>
            <if test="mipPayAmount != null">#{mipPayAmount},</if>
            <if test="mipRefundAmount != null">#{mipRefundAmount},</if>
            <if test="mipNetIn != null">#{mipNetIn},</if>
            <if test="totalNetIn != null">#{totalNetIn},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateAlipayCheck" parameterType="AlipayCheck">
        update alipay_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="alipayPayAmount != null">alipay_pay_amount = #{alipayPayAmount},</if>
            <if test="alipayMzPayAmount != null">alipay_mz_pay_amount = #{alipayMzPayAmount},</if>
            <if test="alipayZyPayAmount != null">alipay_zy_pay_amount = #{alipayZyPayAmount},</if>
            <if test="hisPayAmount != null">his_pay_amount = #{hisPayAmount},</if>
            <if test="diffPayAmount != null">diff_pay_amount = #{diffPayAmount},</if>
            <if test="payBalanced != null">pay_balanced = #{payBalanced},</if>
            <if test="alipayRefundAmount != null">alipay_refund_amount = #{alipayRefundAmount},</if>
            <if test="alipayMzRefundAmount != null">alipay_mz_refund_amount = #{alipayMzRefundAmount},</if>
            <if test="alipayZyRefundAmount != null">alipay_zy_refund_amount = #{alipayZyRefundAmount},</if>
            <if test="hisRefundAmount != null">his_refund_amount = #{hisRefundAmount},</if>
            <if test="diffRefundAmount != null">diff_refund_amount = #{diffRefundAmount},</if>
            <if test="refundBalanced != null">refund_balanced = #{refundBalanced},</if>
            <if test="alipayNetIn != null">alipay_net_in = #{alipayNetIn},</if>
            <if test="hisNetIn != null">his_net_in = #{hisNetIn},</if>
            <if test="mipPayAmount != null">mip_pay_amount = #{mipPayAmount},</if>
            <if test="mipRefundAmount != null">mip_refund_amount = #{mipRefundAmount},</if>
            <if test="mipNetIn != null">mip_net_in = #{mipNetIn},</if>
            <if test="totalNetIn != null">total_net_in = #{totalNetIn},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlipayCheckById" parameterType="Long">
        delete
        from alipay_check
        where id = #{id}
    </delete>

    <delete id="deleteAlipayCheckByIds" parameterType="String">
        delete from alipay_check where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
