package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.LisReport;
import space.lzhq.ph.mapper.LisReportMapper;
import space.lzhq.ph.service.ILisReportService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LisReportServiceImpl implements ILisReportService {

    private final LisReportMapper mapper;

    public LisReportServiceImpl(LisReportMapper mapper) {
        this.mapper = mapper;
    }

    /**
     * 查门诊报告使用实体卡号，查住院报告使用住院号
     */
    @Override
    public List<LisReport> selectLisReports(String inPatientId, String outPatientId) {
        return mapper.selectLisReports(inPatientId, outPatientId);
    }

}
