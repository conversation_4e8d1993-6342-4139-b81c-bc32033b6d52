package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.LisReportItem;
import space.lzhq.ph.mapper.LisReportItemMapper;
import space.lzhq.ph.service.ILisReportItemService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LisReportItemServiceImpl implements ILisReportItemService {

    private final LisReportItemMapper mapper;

    public LisReportItemServiceImpl(LisReportItemMapper mapper) {
        this.mapper = mapper;
    }


    @Override
    public List<LisReportItem> selectLisReportItemsByInspectionId(String inspectionId) {
        return mapper.selectLisReportItemsByInspectionId(inspectionId);
    }
}
