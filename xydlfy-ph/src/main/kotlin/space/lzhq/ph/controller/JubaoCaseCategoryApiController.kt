package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.domain.JubaoCaseCategory
import space.lzhq.ph.service.IJubaoCaseCategoryService

@RestController
class JubaoCaseCategoryApiController {

    @Autowired
    private lateinit var jubaoCaseCategoryService: IJubaoCaseCategoryService

    @GetMapping("/open/jubao/caseCategories")
    fun getCaseCategories(): AjaxResult {
        val result: Map<Long, List<JubaoCaseCategory>> =
            jubaoCaseCategoryService.selectJubaoCaseCategoryList(JubaoCaseCategory())
                .groupBy { it.parentId }
                .onEach {
                    it.value.sortedBy { x -> x.sortNo }
                }
        return AjaxResult.success(result)
    }

}