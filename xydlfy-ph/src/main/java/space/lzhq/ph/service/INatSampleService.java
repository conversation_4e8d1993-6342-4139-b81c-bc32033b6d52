package space.lzhq.ph.service;

import space.lzhq.ph.domain.NatSample;

import java.util.Date;
import java.util.List;

/**
 * 样品Service接口
 *
 * <AUTHOR>
 * @date 2021-12-12
 */
public interface INatSampleService {
    /**
     * 查询样品
     *
     * @param id 样品ID
     * @return 样品
     */
    NatSample selectNatSampleById(Long id);

    /**
     * 查询样品列表
     *
     * @param natSample 样品
     * @return 样品集合
     */
    List<NatSample> selectNatSampleList(NatSample natSample);

    /**
     * 统计试管数量
     *
     * @param natSample 样品
     * @return 试管数量
     */
    Long countCodes(NatSample natSample);

    /**
     * 根据混样编码查询样品信息
     *
     * @param code 混样编码
     * @return 样品集合
     */
    List<NatSample> selectListByCode(String code);

    List<NatSample> selectListByIdCardNo(String idCardNo);

    /**
     * 查询样品列表
     *
     * @param personId    样品人ID
     * @param collectDate 采样日期
     * @return 样品集合
     */
    List<NatSample> selectListByPersonIdAndCollectDate(Long personId, Date collectDate);

    /**
     * 新增样品
     *
     * @param natSample 样品
     * @return 结果
     */
    int insertNatSample(NatSample natSample);

    /**
     * 修改样品
     *
     * @param natSample 样品
     * @return 结果
     */
    int updateNatSample(NatSample natSample);

    /**
     * 批量删除样品
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteNatSampleByIds(String ids);

    /**
     * 删除样品信息
     *
     * @param id 样品ID
     * @return 结果
     */
    int deleteNatSampleById(Long id);

    /**
     * 更新采样日报
     *
     * @param collectDate 采样日期
     */
    void updateSummary(Date collectDate);

    /**
     * 更新混样类型
     *
     * @param code 混样编码
     */
    void updateMixType(String code);
}
