package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.DictUtils
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.data.id.IdUtil
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.core.lang.Validator
import org.dromara.hutool.core.text.StrUtil
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.HealthCardRegisterRequestV2
import org.mospital.dongruan.bean.HealthCardRegisterResponse
import org.mospital.erhc.ylz.ErhcService
import org.mospital.erhc.ylz.RegisterOrQueryForm
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.UserInfo
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IAlipayUserInfoService
import space.lzhq.ph.service.IPatientService

@RestController
@RequestMapping("/api/erhcCard")
class ErhcCardApiController : BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    @Autowired
    private lateinit var alipayUserInfoService: IAlipayUserInfoService

    /**
     * 查询或注册健康卡
     * @param idCardNo 身份证号
     * @param name 姓名
     * @param mobile 手机号
     * @param nationCode 民族代码，如 01 表示汉族，参考 /open/dict/queryByType?type=minzu
     */
    @PostMapping("getOrRegister/v1")
    @RequireSession
    fun getOrRegisterV1(
        @RequestParam idCardNo: String,
        @RequestParam name: String,
        @RequestParam mobile: String,
        @RequestParam nationCode: String,
        @RequestParam address: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo = alipayUserInfoService.fillUserInfo(session, UserInfo(idCardNo, name, mobile))

        if (!IdcardUtil.isValidCard(userInfo.idCardNo)) {
            return AjaxResult.error("身份证号不正确")
        }

        val birthday = IdcardUtil.getBirthDate(userInfo.idCardNo)
        if (birthday.isAfter(DateTime.now())) {
            return AjaxResult.error("身份证号不正确")
        }

        if (!Validator.isMobile(userInfo.mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        synchronized(userInfo.idCardNo.intern()) {
            val erhcPatients = patientService.selectPatientListByIdCard(userInfo.idCardNo)
                .filter { it.hasErhcCard() && it.clientTypeEnum == session.clientTypeEnum }
            if (erhcPatients.isNotEmpty() && erhcPatients.any { it.openId == session.openId }) {
                return AjaxResult.error("您已持有电子健康卡，无需重复领取")
            }

            val fixedNationCode = StrUtil.padPre(nationCode, 2, "0")
            val healthCardResult = ErhcService.getOrCreateHealthCard(
                idCardNo = userInfo.idCardNo,
                name = userInfo.name,
                mobile = userInfo.mobile,
                nationCode = fixedNationCode,
            )

            var hospitalCardResponse =
                DongruanService.queryHospitalCard(markNo = userInfo.idCardNo, testMode = testMode)
            if (hospitalCardResponse.isOk() && hospitalCardResponse.markType == "5") {
                val regex = Regex("[^\\u4e00-\\u9fa5]+")
                if (regex.replace(hospitalCardResponse.name, "") != regex.replace(userInfo.name, "")) {
                    return AjaxResult.error("姓名不正确")
                }

                val patient = Patient(session, hospitalCardResponse, userInfo.mobile)
                patient.active = if (patientService.selectPatientListByOpenId(session.openId).isEmpty()) 1 else 0
                patient.jzCardNo = hospitalCardResponse.markNo
                patient.nation = nationCode

                // HIS 改版后，不再返回健康卡号，改为从电子健康卡平台获取
                patient.erhcCardNo = healthCardResult.data?.erhcCardNo.orEmpty()
                patient.empi = healthCardResult.data?.empi.orEmpty()

                patientService.insertPatient(patient)
                return AjaxResult.success(patient)
            }

            if (!healthCardResult.ok) {
                return AjaxResult.error(healthCardResult.msg)
            }

            val nationDict = DictUtils.getDictCache("minzu").firstOrNull {
                StrUtil.padPre(it.dictValue, 2, "0") == fixedNationCode
            }
            if (nationDict == null) {
                return AjaxResult.error("民族不正确")
            }

            val healthCard = healthCardResult.data!!
            val hisRegisterResponse: HealthCardRegisterResponse = DongruanService.registerHealthCardV2(
                HealthCardRegisterRequestV2().apply {
                    this.name = userInfo.name
                    this.phone = userInfo.mobile
                    this.idCardNo = userInfo.idCardNo
                    this.nation = fixedNationCode
                    this.sex = if (IdcardUtil.getGender(userInfo.idCardNo) == 1) "1" else "2"
                    this.address = address
                    this.markNo = healthCard.erhcCardNo
                    this.birthday = birthday.toString(DatePattern.NORM_DATE_FORMAT)
                    this.babyFlag = 0
                    this.jhrIdNo = ""
                    this.jhrName = ""
                    this.transModel =
                        HealthCardRegisterRequestV2.TransModel(bankTransNo = IdUtil.fastSimpleUUID().take(30))
                },
                testMode = testMode
            )
            if (!hisRegisterResponse.isOk()
                && "该电子健康卡信息本地已存在" !in hisRegisterResponse.msg
                // 保存就诊卡信息失败:执行产生错误!ORA-20003: 检测到异常！已经存在绑定电子健康卡记录，无法再次插入。(TRG_FIN_OPB_ACCOUNTCard_Double)ORA-06512: 在 "HIS.TRG_FIN_OPB_ACCOUNTCARD_DOUBLE", line 16ORA-04088: 触发器 'HIS.TRG_FIN_OPB_ACCOUNTCARD_DOUBLE' 执行过程中出错
                && "已经存在绑定电子健康卡记录" !in hisRegisterResponse.msg
            ) {
                return AjaxResult.error(hisRegisterResponse.msg)
            }

            hospitalCardResponse =
                DongruanService.queryHospitalCard(
                    markNo = hisRegisterResponse.cardNo,
                    testMode = testMode
                )
            if (!hospitalCardResponse.isOk()) {
                return AjaxResult.error(hospitalCardResponse.msg)
            }

            val patient = Patient(session, hospitalCardResponse, userInfo.mobile)
            patient.active = if (patientService.selectPatientListByOpenId(session.openId).isEmpty()) 1 else 0
            patient.empi = healthCard.empi
            patient.erhcCardNo = healthCard.erhcCardNo
            patient.nation = fixedNationCode
            patientService.insertPatient(patient)
            return AjaxResult.success(patient)
        }
    }

    /**
     * 获取健康卡二维码
     * @param jzCardNo 就诊卡号
     */
    @GetMapping("getQrCode")
    @RequireSession
    fun getQrCode(@RequestParam jzCardNo: String): AjaxResult {
        val session = request.getClientSession()!!
        val patient = patientService.getOneByJzCardNoAndOpenId(jzCardNo, session.openId)
            ?: return AjaxResult.error("指定就诊人不存在或者未关联健康卡")
        if (!patient.hasErhcCard()) {
            return AjaxResult.error("此卡不是电子健康卡")
        }

        val ret = ErhcService.registerOrQuery(
            RegisterOrQueryForm(
                idNo = patient.idCardNo,
                userName = patient.name,
                mobile = patient.mobile,
                nation = StrUtil.padPre(patient.nation, 2, "0")
            )
        )
        return if (ret.ok) {
            AjaxResult.success(ret.data)
        } else {
            AjaxResult.error(ret.msg)
        }
    }

}
