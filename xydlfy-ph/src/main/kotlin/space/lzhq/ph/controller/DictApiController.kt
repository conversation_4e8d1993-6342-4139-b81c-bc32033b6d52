package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.framework.web.service.DictService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/open/dict")
class DictApiController {

    @Autowired
    private lateinit var dictService: DictService

    @GetMapping("/{type}")
    fun findByType(@PathVariable type: String): AjaxResult {
        return AjaxResult.success(dictService.getType(type))
    }

}
