package space.lzhq.ph.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import space.lzhq.ph.BaseIntegrationTest;
import space.lzhq.ph.domain.CardRefundApplication;
import space.lzhq.ph.domain.SmsRecord;
import space.lzhq.ph.dto.sms.SmsResponse;
import space.lzhq.ph.service.impl.SmsServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 短信记录功能测试控制器
 *
 * <AUTHOR>
 */
@Slf4j
@DisplayName("短信记录功能测试")
@ActiveProfiles("prod")
class SmsRecordServiceTest extends BaseIntegrationTest {

    @Autowired
    private ISmsService smsService;

    @Autowired
    private ISmsRecordService smsRecordService;

    @Autowired
    private ICardRefundApplicationService cardRefundApplicationService;

    private static final String TEST_MOBILE = "***********";

    @Test
    @DisplayName("测试短信记录基础功能")
    void testSmsRecordBasicOperations() {
        log.info("开始测试短信记录基础功能");

        // 测试保存短信记录
        String sismsid = smsService.generateSmsId().toString();
        String content = "测试短信内容";
        String businessType = SmsRecord.BusinessType.GENERAL_NOTIFICATION;
        Long businessId = 1L;

        SmsRecord savedRecord = smsRecordService.saveSmsRecord(
                sismsid, TEST_MOBILE, content,
                SmsRecord.SendMethod.NORMAL, "",
                businessType, businessId
        );

        assertNotNull(savedRecord);
        assertNotNull(savedRecord.getId());
        assertEquals(sismsid, savedRecord.getSismsid());
        assertEquals(TEST_MOBILE, savedRecord.getMobile());
        assertEquals(content, savedRecord.getContent());
        assertEquals(businessType, savedRecord.getBusinessType());
        assertEquals(businessId, savedRecord.getBusinessId());
        assertEquals(SmsRecord.SendStatus.PENDING.getCode(), savedRecord.getSendStatus());

        log.info("短信记录保存成功: recordId={}, sismsid={}", savedRecord.getId(), sismsid);

        // 测试根据sismsid查询
        SmsRecord foundRecord = smsRecordService.getBySismsid(sismsid);
        assertNotNull(foundRecord);
        assertEquals(savedRecord.getId(), foundRecord.getId());

        // 测试根据业务类型和业务ID查询
        List<SmsRecord> businessRecords = smsRecordService.getByBusinessTypeAndId(businessType, businessId);
        assertFalse(businessRecords.isEmpty());
        assertTrue(businessRecords.stream().anyMatch(r -> r.getId().equals(savedRecord.getId())));

        // 测试更新短信发送结果
        SmsResponse mockResponse = new SmsResponse();
        mockResponse.setCode("0");
        mockResponse.setMessage("发送成功");

        smsRecordService.updateSmsResult(sismsid, mockResponse);

        SmsRecord updatedRecord = smsRecordService.getBySismsid(sismsid);
        assertEquals(SmsRecord.SendStatus.SUCCESS.getCode(), updatedRecord.getSendStatus());
        assertEquals("0", updatedRecord.getResponseCode());
        assertEquals("发送成功", updatedRecord.getResponseMessage());

        log.info("短信记录基础功能测试完成");
    }

    @Test
    @DisplayName("测试短信发送与记录集成")
    void testSmsServiceWithRecord() {
        log.info("开始测试短信发送与记录集成");

        if (!(smsService instanceof SmsServiceImpl smsServiceImpl)) {
            log.warn("当前短信服务不是SmsServiceImpl实例，跳过测试");
            return;
        }

        String content = "集成测试短信内容：您的验证码是123456";
        String businessType = SmsRecord.BusinessType.VERIFICATION_CODE;
        Long businessId = 2L;

        try {
            // 发送短信并记录
            SmsResponse response = smsServiceImpl.sendSmsWithRecord(
                    TEST_MOBILE, content, "",
                    SmsRecord.SendMethod.VERIFICATION_CODE,
                    businessType, businessId
            );

            // 验证短信发送结果
            assertNotNull(response);
            log.info("短信发送响应: success={}, code={}, message={}",
                    response.isSuccess(), response.getCode(), response.getMessage());

            // 查询短信记录
            List<SmsRecord> records = smsRecordService.getByBusinessTypeAndId(businessType, businessId);
            assertFalse(records.isEmpty());

            SmsRecord latestRecord = records.getFirst();
            assertEquals(TEST_MOBILE, latestRecord.getMobile());
            assertEquals(content, latestRecord.getContent());
            assertEquals(businessType, latestRecord.getBusinessType());
            assertEquals(businessId, latestRecord.getBusinessId());

            log.info("短信记录查询成功: recordId={}, status={}, sismsid={}",
                    latestRecord.getId(), latestRecord.getSendStatus(), latestRecord.getSismsid());

        } catch (Exception e) {
            log.warn("短信发送测试异常（可能是配置问题）: {}", e.getMessage());
            // 在测试环境中，短信服务可能未完全配置，异常是正常的
        }

        log.info("短信发送与记录集成测试完成");
    }

    @Test
    @DisplayName("测试余额清退申请短信通知记录")
    void testCardRefundApplicationSmsRecord() {
        log.info("开始测试余额清退申请短信通知记录");

        try {
            // 查询最近的已审核通过的申请
            List<CardRefundApplication> approvedApplications = cardRefundApplicationService.lambdaQuery()
                    .eq(CardRefundApplication::getAuditStatus, CardRefundApplication.AuditStatus.APPROVED)
                    .orderByDesc(CardRefundApplication::getId)
                    .last("LIMIT 1")
                    .list();

            if (approvedApplications.isEmpty()) {
                log.info("未找到已审核通过的余额清退申请，跳过测试");
                return;
            }

            CardRefundApplication application = approvedApplications.getFirst();
            log.info("找到测试申请: applicationId={}, mobile={}",
                    application.getId(), application.getMobile());

            // 查询相关的短信记录
            List<SmsRecord> smsRecords = smsRecordService.getByBusinessTypeAndId(
                    SmsRecord.BusinessType.CARD_REFUND_APPLICATION,
                    application.getId()
            );

            log.info("找到相关短信记录数量: {}", smsRecords.size());

            for (SmsRecord smsRecord : smsRecords) {
                log.info("短信记录详情: recordId={}, mobile={}, status={}, sendTime={}, content={}",
                        smsRecord.getId(), smsRecord.getMobile(),
                        SmsRecord.SendStatus.valueOf(smsRecord.getSendStatus()).getDescription(),
                        smsRecord.getSendTime(),
                        smsRecord.getContent().length() > 50 ?
                                smsRecord.getContent().substring(0, 50) + "..." : smsRecord.getContent());
            }

            // 测试时间范围查询
            LocalDateTime startTime = LocalDateTime.now().minusDays(1);
            LocalDateTime endTime = LocalDateTime.now().plusDays(1);

            List<SmsRecord> timeRangeRecords = smsRecordService.getByMobileAndTimeRange(
                    application.getMobile(), startTime, endTime
            );

            log.info("时间范围内该手机号的短信记录数量: {}", timeRangeRecords.size());

        } catch (Exception e) {
            log.error("测试余额清退申请短信通知记录异常", e);
        }

        log.info("余额清退申请短信通知记录测试完成");
    }

    @Test
    @DisplayName("测试短信发送统计功能")
    void testSmsStatistics() {
        log.info("开始测试短信发送统计功能");

        // 统计最近24小时的短信发送量
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        LocalDateTime endTime = LocalDateTime.now();

        Long totalCount = smsRecordService.countByTimeRange(startTime, endTime);
        log.info("最近24小时短信发送总量: {}", totalCount);

        // 查询发送失败的短信
        List<SmsRecord> failedRecords = smsRecordService.getBySendStatus(
                SmsRecord.SendStatus.FAILED.getCode(), 10
        );
        log.info("最近发送失败的短信数量: {}", failedRecords.size());

        // 查询待发送的短信
        List<SmsRecord> pendingRecords = smsRecordService.getBySendStatus(
                SmsRecord.SendStatus.PENDING.getCode(), 10
        );
        log.info("当前待发送的短信数量: {}", pendingRecords.size());

        log.info("短信发送统计功能测试完成");
    }

    @Test
    @DisplayName("测试短信重试功能（验证不创建重复记录）")
    void testSmsRetryWithoutDuplicateRecords() {
        log.info("开始测试短信重试功能");

        // 1. 先创建一个失败的短信记录
        String sismsid = smsService.generateSmsId().toString();
        String content = "重试测试短信内容";
        String businessType = SmsRecord.BusinessType.GENERAL_NOTIFICATION;
        Long businessId = 999L;

        smsRecordService.saveSmsRecord(
                sismsid, TEST_MOBILE, content,
                SmsRecord.SendMethod.NORMAL, "",
                businessType, businessId
        );

        // 2. 模拟短信发送失败
        smsRecordService.updateSmsError(sismsid, "模拟发送失败");

        // 3. 验证记录状态为失败
        SmsRecord failedRecord = smsRecordService.getBySismsid(sismsid);
        assertEquals(SmsRecord.SendStatus.FAILED.getCode(), failedRecord.getSendStatus());
        assertEquals(1, failedRecord.getRetryCount().intValue());

        log.info("原始失败记录创建成功: recordId={}, sismsid={}, retryCount={}",
                failedRecord.getId(), sismsid, failedRecord.getRetryCount());

        // 4. 查询重试前该业务的记录数量
        List<SmsRecord> recordsBeforeRetry = smsRecordService.getByBusinessTypeAndId(businessType, businessId);
        int countBeforeRetry = recordsBeforeRetry.size();
        long originalRecordCount = recordsBeforeRetry.stream()
                .filter(r -> r.getSismsid().equals(sismsid))
                .count();

        log.info("重试前记录统计: 总数={}, 该sismsid记录数={}", countBeforeRetry, originalRecordCount);

        // 5. 执行重试（注意：在测试环境中可能会因为短信服务配置问题而失败，这是正常的）
        int retryCountBeforeRetry = failedRecord.getRetryCount();
        try {
            boolean retryResult = smsRecordService.retrySendSms(failedRecord.getId());
            log.info("重试执行结果: {}", retryResult);
        } catch (Exception e) {
            log.warn("重试执行异常（测试环境正常现象）: {}", e.getMessage());
        }

        // 6. 验证重试后记录数量没有增加
        List<SmsRecord> recordsAfterRetry = smsRecordService.getByBusinessTypeAndId(businessType, businessId);
        int countAfterRetry = recordsAfterRetry.size();
        long finalRecordCount = recordsAfterRetry.stream()
                .filter(r -> r.getSismsid().equals(sismsid))
                .count();

        log.info("重试后记录统计: 总数={}, 该sismsid记录数={}", countAfterRetry, finalRecordCount);

        // 7. 断言验证
        assertEquals(countBeforeRetry, countAfterRetry, "重试后记录总数应该保持不变");
        assertEquals(originalRecordCount, finalRecordCount, "同一sismsid的记录数应该保持为1");
        assertEquals(1, finalRecordCount, "应该只有一条记录");

        // 8. 验证重试次数只递增一次
        SmsRecord retriedRecord = smsRecordService.getBySismsid(sismsid);
        assertNotNull(retriedRecord);
        int expectedRetryCount = retryCountBeforeRetry + 1;
        assertEquals(expectedRetryCount, retriedRecord.getRetryCount().intValue(),
                "重试次数应该只递增1次，从" + retryCountBeforeRetry + "变为" + expectedRetryCount);

        log.info("重试功能测试完成: recordId={}, 重试前次数={}, 重试后次数={}, 记录状态={}",
                retriedRecord.getId(), retryCountBeforeRetry, retriedRecord.getRetryCount(),
                SmsRecord.SendStatus.valueOf(retriedRecord.getSendStatus()).getDescription());
    }

    @Test
    @DisplayName("测试事务优化后的短信重试功能")
    void testTransactionOptimizedSmsRetry() {
        log.info("开始测试事务优化后的短信重试功能");

        // 1. 创建一个失败的短信记录
        String sismsid = smsService.generateSmsId().toString();
        String content = "事务优化重试测试短信内容";
        String businessType = SmsRecord.BusinessType.GENERAL_NOTIFICATION;
        Long businessId = 1000L;

        smsRecordService.saveSmsRecord(
                sismsid, TEST_MOBILE, content,
                SmsRecord.SendMethod.NORMAL, "",
                businessType, businessId
        );

        // 2. 模拟短信发送失败
        smsRecordService.updateSmsError(sismsid, "模拟发送失败用于测试事务优化");

        // 3. 验证初始状态
        SmsRecord failedRecord = smsRecordService.getBySismsid(sismsid);
        assertEquals(SmsRecord.SendStatus.FAILED.getCode(), failedRecord.getSendStatus());
        assertEquals(1, failedRecord.getRetryCount().intValue());
        assertNotNull(failedRecord.getErrorMessage());

        log.info("初始失败记录: recordId={}, status={}, retryCount={}, error={}",
                failedRecord.getId(), failedRecord.getSendStatus(),
                failedRecord.getRetryCount(), failedRecord.getErrorMessage());

        // 4. 测试独立事务的状态更新方法
        if (smsRecordService instanceof space.lzhq.ph.service.impl.SmsRecordServiceImpl smsRecordServiceImpl) {
            log.info("测试独立事务的updateRecordToPending方法");

            // 调用独立事务方法更新状态为PENDING
            boolean updateSuccess = smsRecordServiceImpl.updateRecordToPending(failedRecord.getId());
            assertTrue(updateSuccess, "updateRecordToPending应该成功");

            // 验证状态已更新为PENDING
            SmsRecord pendingRecord = smsRecordService.getBySismsid(sismsid);
            assertEquals(SmsRecord.SendStatus.PENDING.getCode(), pendingRecord.getSendStatus());
            assertEquals("", pendingRecord.getErrorMessage(), "错误信息应该清空");
            assertEquals(1, pendingRecord.getRetryCount().intValue(), "retryCount在此阶段不应该变化");

            log.info("PENDING状态更新成功: recordId={}, status={}, retryCount={}",
                    pendingRecord.getId(), pendingRecord.getSendStatus(), pendingRecord.getRetryCount());

            // 5. 测试异常情况的事务处理
            log.info("测试异常情况的事务处理");

            String errorMessage = "测试异常处理的错误信息";
            boolean exceptionUpdateSuccess = smsRecordServiceImpl.updateRecordWithException(
                    failedRecord.getId(), errorMessage);
            assertTrue(exceptionUpdateSuccess, "updateRecordWithException应该成功");

            // 验证异常处理结果
            SmsRecord exceptionRecord = smsRecordService.getBySismsid(sismsid);
            assertEquals(SmsRecord.SendStatus.FAILED.getCode(), exceptionRecord.getSendStatus());
            assertEquals(errorMessage, exceptionRecord.getErrorMessage());
            assertEquals(2, exceptionRecord.getRetryCount().intValue(), "异常处理后retryCount应该递增");

            log.info("异常处理验证成功: recordId={}, status={}, retryCount={}, error={}",
                    exceptionRecord.getId(), exceptionRecord.getSendStatus(),
                    exceptionRecord.getRetryCount(), exceptionRecord.getErrorMessage());

            // 6. 测试成功结果的事务处理
            log.info("测试成功结果的事务处理");

            // 先重置为PENDING状态
            smsRecordServiceImpl.updateRecordToPending(failedRecord.getId());

            // 模拟成功的SMS响应
            space.lzhq.ph.dto.sms.SmsResponse successResponse = new space.lzhq.ph.dto.sms.SmsResponse();
            successResponse.setCode("0");
            successResponse.setMessage("发送成功");

            boolean successUpdateSuccess = smsRecordServiceImpl.updateRecordWithResult(
                    failedRecord.getId(), successResponse);
            assertTrue(successUpdateSuccess, "updateRecordWithResult应该成功");

            // 验证成功处理结果
            SmsRecord successRecord = smsRecordService.getBySismsid(sismsid);
            assertEquals(SmsRecord.SendStatus.SUCCESS.getCode(), successRecord.getSendStatus());
            assertEquals("0", successRecord.getResponseCode());
            assertEquals("发送成功", successRecord.getResponseMessage());
            assertEquals(3, successRecord.getRetryCount().intValue(), "成功处理后retryCount应该再次递增");

            log.info("成功处理验证完成: recordId={}, status={}, retryCount={}, responseCode={}",
                    successRecord.getId(), successRecord.getSendStatus(),
                    successRecord.getRetryCount(), successRecord.getResponseCode());

        } else {
            log.warn("当前服务不是SmsRecordServiceImpl实例，跳过事务方法测试");
        }

        // 7. 测试完整的重试流程
        log.info("测试完整的事务优化重试流程");

        // 重置为失败状态以便测试完整流程
        smsRecordService.updateSmsError(sismsid, "重置为失败状态用于完整流程测试");

        SmsRecord resetRecord = smsRecordService.getBySismsid(sismsid);
        int retryCountBeforeFullRetry = resetRecord.getRetryCount();

        try {
            // 执行完整的重试流程
            boolean fullRetryResult = smsRecordService.retrySendSms(resetRecord.getId());
            log.info("完整重试流程执行结果: {}", fullRetryResult);

            // 验证最终状态
            SmsRecord finalRecord = smsRecordService.getBySismsid(sismsid);
            assertNotNull(finalRecord);
            assertEquals(retryCountBeforeFullRetry + 1, finalRecord.getRetryCount().intValue(),
                    "完整重试流程后retryCount应该只递增1次");

            log.info("完整重试流程验证完成: recordId={}, finalStatus={}, finalRetryCount={}",
                    finalRecord.getId(), finalRecord.getSendStatus(), finalRecord.getRetryCount());

        } catch (Exception e) {
            log.warn("完整重试流程异常（测试环境正常现象）: {}", e.getMessage());
        }

        log.info("事务优化后的短信重试功能测试完成");
    }
} 