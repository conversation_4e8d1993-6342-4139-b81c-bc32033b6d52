package space.lzhq.ph.service

import com.ruoyi.common.utils.PinyinUtils
import com.ruoyi.system.service.ISysConfigService
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.runBlocking
import org.mospital.dongruan.roc.DeptInfo
import org.mospital.dongruan.roc.DongruanRocService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.Collator
import java.time.LocalDate
import java.util.*
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

@Service
class ScheduleService {

    private val log = LoggerFactory.getLogger(ScheduleService::class.java)

    private val lock = ReentrantReadWriteLock()

    private val departments: MutableList<DeptInfo> = mutableListOf()
    private val indexedDepartments: MutableList<Map<String, Any>> = mutableListOf()

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @PostConstruct
    fun init() {
        log.info("首次加载排班科室信息...")
        try {
            syncAndIndexDepartments()
        } catch (e: Exception) {
            log.error("初始化排班科室信息失败，将在后续调用中重试", e)
        }
    }

    fun syncAndIndexDepartments() {
        lock.write {
            syncDepartmentsFromHis()
            indexDepartments()
        }
    }

    fun getIndexedDepartments(forceReload: Boolean = false): List<Map<String, Any>> {
        // 第一次检查：使用读锁快速检查是否需要重新加载
        val needsReload = lock.read { forceReload || indexedDepartments.isEmpty() }

        if (needsReload) {
            // 获取写锁以执行同步操作
            lock.write {
                // 双重检查：在写锁内再次检查是否仍需要重新加载
                // 防止多个线程同时进入此代码块时重复执行同步操作
                if (forceReload || indexedDepartments.isEmpty()) {
                    syncDepartmentsFromHis()
                    indexDepartments()
                }
            }
        }

        return lock.read { indexedDepartments.toList() }
    }

    private fun syncDepartmentsFromHis() {
        val result = runBlocking {
            DongruanRocService.queryAvailableDepts(
                beginDate = LocalDate.now(),
                endDate = LocalDate.now().plusDays(15)
            )
        }
        result.fold(
            onSuccess = { hisDepartments ->
                this.departments.clear()
                this.departments.addAll(filterDepartments(hisDepartments))
                log.info("同步排班科室成功，科室数量：${this.departments.size}")
            },
            onFailure = { exception ->
                log.error("同步排班科室失败：${exception.message}", exception)
            }
        )
    }

    private fun filterDepartments(departments: List<DeptInfo>): List<DeptInfo> {
        val excludedDepartments = sysConfigService.excludedDepartments
        return departments
            .distinctBy { it.deptCode }
            .filter {
                !excludedDepartments.contains(it.deptName)
            }
    }

    private fun indexDepartments() {
        val indexedDepartments = this.departments
            .groupBy {
                val pinyin = PinyinUtils.getSimplePinyin(it.deptName, "", true)
                if (pinyin.isNotEmpty()) pinyin.first().uppercase() else "#"
            }
            .map {
                mapOf(
                    "index" to it.key,
                    "departments" to it.value.sortedWith { d1: DeptInfo, d2: DeptInfo ->
                        Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(d1.deptName, d2.deptName)
                    }
                )
            }
            .sortedWith { o1: Map<String, Any>, o2: Map<String, Any> ->
                (o1["index"] as String).compareTo(o2["index"] as String)
            }
        this.indexedDepartments.clear()
        this.indexedDepartments.addAll(indexedDepartments)
    }

}