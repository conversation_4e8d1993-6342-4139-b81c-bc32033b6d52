<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.NatPersonMapper">

    <resultMap type="NatPerson" id="NatPersonResult">
        <result property="id" column="id"/>
        <result property="department" column="department"/>
        <result property="name" column="name"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="mobile" column="mobile"/>
        <result property="nation" column="nation"/>
        <result property="type" column="type"/>
        <result property="days" column="days"/>
        <result property="address" column="address"/>
        <result property="createTime" column="create_time"/>
        <result property="sortNo" column="sort_no"/>
    </resultMap>

    <sql id="selectNatPersonVo">
        select id,
               department,
               name,
               id_card_no,
               mobile,
               nation,
               type,
               days,
               address,
               create_time,
               sort_no
        from ph_nat_person
    </sql>

    <select id="selectNatPersonList" parameterType="NatPerson" resultMap="NatPersonResult">
        <include refid="selectNatPersonVo"/>
        <where>
            <if test="department != null">and department = #{department}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectNatPersonById" parameterType="Long" resultMap="NatPersonResult">
        <include refid="selectNatPersonVo"/>
        where id = #{id}
    </select>

    <select id="selectNatPersonByIdCardNo" parameterType="String" resultMap="NatPersonResult">
        <include refid="selectNatPersonVo"/>
        where id_card_no = #{idCardNo}
    </select>

    <insert id="insertNatPerson" parameterType="NatPerson" useGeneratedKeys="true" keyProperty="id">
        insert into ph_nat_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="department != null">department,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="nation != null">nation,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="days != null">days,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sortNo != null">sort_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="department != null">#{department},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="idCardNo != null and idCardNo != ''">#{idCardNo},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="nation != null">#{nation},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="days != null">#{days},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sortNo != null">#{sortNo},</if>
        </trim>
    </insert>

    <update id="updateNatPerson" parameterType="NatPerson">
        update ph_nat_person
        <trim prefix="SET" suffixOverrides=",">
            <if test="department != null">department = #{department},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="days != null">days = #{days},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sortNo != null">sort_no = #{sortNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNatPersonById" parameterType="Long">
        delete
        from ph_nat_person
        where id = #{id}
    </delete>

    <delete id="deleteNatPersonByIds" parameterType="String">
        delete from ph_nat_person where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByMaxCreateTime" resultType="java.lang.Integer"><![CDATA[
        select count(*)
        from ph_nat_person
        where create_time <= #{maxCreateTime}
        ]]>
    </select>

    <select id="selectWeijianPersons" resultMap="NatPersonResult">
        <include refid="selectNatPersonVo"/>
        where create_time &lt; #{maxTime}
        <if test="department != null">and department = #{department}</if>
        and not exists(
        select 1
        from ph_nat_sample s
        where s.person_id = ph_nat_person.id
        and s.collect_time between #{minTime} and #{maxTime}
        )
    </select>
</mapper>