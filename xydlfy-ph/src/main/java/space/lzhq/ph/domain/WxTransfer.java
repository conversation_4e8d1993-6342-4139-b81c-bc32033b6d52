package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ph_wx_transfer")
public class WxTransfer {

    @TableField(exist = false)
    public static final List<String> FINAL_STATES = Arrays.asList(
            "SUCCESS",           // 转账成功
            "FAILED",            // 转账失败
            "BANK_FAIL"          // 银行退票
    );

    @TableField(exist = false)
    public static final String WAIT_USER_CONFIRM = "WAIT_USER_CONFIRM";

    public WxTransfer(CardRefund cardRefund, BigDecimal refundAmount) {
        this.openId = cardRefund.getOpenId();
        this.applicationId = cardRefund.getApplicationId();
        this.cardRefundId = cardRefund.getId();
        this.cardNo = cardRefund.getCardNo();
        this.idCardNo = cardRefund.getIdCardNo();
        this.name = cardRefund.getName();
        this.mobile = cardRefund.getMobile();
        this.amount = refundAmount;
        this.createTime = LocalDateTime.now();
        this.hisRegisterId = cardRefund.getHisRegisterId();
        this.hisNotificationState = 0;
        this.hisNotificationTime = null;
        this.hisNotificationResult = "";
    }

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微信用户ID
     */
    private String openId;

    /**
     * 申请记录ID
     */
    private Long applicationId;

    /**
     * 卡退款记录的ID
     */
    private Long cardRefundId;

    /**
     * HIS申请ID
     */
    private String hisRegisterId;

    /**
     * 就诊卡号
     */
    private String cardNo;

    /**
     * 患者身份证号
     */
    private String idCardNo;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 患者手机号
     */
    private String mobile;

    /**
     * 转账金额
     */
    private BigDecimal amount;

    /**
     * 商户转账单号
     */
    private String outBillNo;

    /**
     * 原始商户转账单号
     */
    private String originalOutBillNo;

    /**
     * 微信转账单号
     */
    private String transferBillNo;

    /**
     * 转账状态
     */
    private String transferState;

    /**
     * 跳转领取页面的package信息
     */
    private String transferPackageInfo;

    /**
     * 转账失败原因
     */
    private String transferFailReason;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * HIS通知状态: 0=未通知, 1=通知成功, 2=通知失败
     */
    private Integer hisNotificationState;

    /**
     * HIS通知时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hisNotificationTime;

    /**
     * HIS通知结果
     */
    private String hisNotificationResult;

    public boolean isSuccessTransfer() {
        return "SUCCESS".equals(transferState);
    }

    public boolean isSuccessNotifyHis() {
        return hisNotificationState != null && hisNotificationState == 1;
    }

}
