package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NatSampleSummaryByPersonDaysAndMixType;
import space.lzhq.ph.mapper.NatSampleSummaryByPersonDaysAndMixTypeMapper;
import space.lzhq.ph.service.INatSampleSummaryByPersonDaysAndMixTypeService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class NatSampleSummaryByPersonDaysAndMixTypeServiceImpl implements INatSampleSummaryByPersonDaysAndMixTypeService {

    private final NatSampleSummaryByPersonDaysAndMixTypeMapper mapper;

    public NatSampleSummaryByPersonDaysAndMixTypeServiceImpl(NatSampleSummaryByPersonDaysAndMixTypeMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<NatSampleSummaryByPersonDaysAndMixType> summary(Date minCollectTime, Date maxCollectTime) {
        return mapper.summary(minCollectTime, maxCollectTime);
    }

    @Override
    public List<NatSampleSummaryByPersonDaysAndMixType> summary1(Date minCollectTime, Date maxCollectTime) {
        return mapper.summary1(minCollectTime, maxCollectTime);
    }

}
