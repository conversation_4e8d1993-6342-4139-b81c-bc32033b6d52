package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;

/**
 * 人员对象 ph_nat_patient
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
public class NatPatient extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 科室
     */
    private Integer department;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 民族
     */
    @Excel(name = "民族", dictType = "minzu")
    private Integer nation;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 几天一检
     */
    @Excel(name = "几天一检")
    private Long days;

    /**
     * 现住址
     */
    @Excel(name = "现住址")
    private String address;

    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 排序号
     */
    private Integer sortNo;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setNation(Integer nation) {
        this.nation = nation;
    }

    public Integer getNation() {
        return nation;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setDays(Long days) {
        this.days = days;
    }

    public Long getDays() {
        return days;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }
    
    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("idCardNo", getIdCardNo())
                .append("mobile", getMobile())
                .append("nation", getNation())
                .append("type", getType())
                .append("days", getDays())
                .append("address", getAddress())
                .append("createTime", getCreateTime())
                .toString();
    }
}
