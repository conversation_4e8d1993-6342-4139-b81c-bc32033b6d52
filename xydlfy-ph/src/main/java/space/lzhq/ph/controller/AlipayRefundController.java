package space.lzhq.ph.controller;

import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mospital.alipay.AlipayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.AlipayRefund;
import space.lzhq.ph.service.IAlipayRefundService;

import java.util.Arrays;
import java.util.List;

/**
 * 支付宝退款记录Controller
 *
 * <AUTHOR>
 * @date 2022-09-10
 */
@Controller
@RequestMapping("/ph/alipayRefund")
public class AlipayRefundController extends BaseController {
    private String prefix = "ph/alipayRefund";

    @Autowired
    private IAlipayRefundService alipayRefundService;

    @RequiresPermissions("ph:alipayRefund:view")
    @GetMapping()
    public String alipayRefund() {
        return prefix + "/alipayRefund";
    }

    /**
     * 查询支付宝退款记录列表
     */
    @RequiresPermissions("ph:alipayRefund:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AlipayRefund alipayRefund) {
        startPage("id desc");
        List<AlipayRefund> list = alipayRefundService.selectAlipayRefundList(alipayRefund);
        return getDataTable(list);
    }

    /**
     * 导出支付宝退款记录列表
     */
    @RequiresPermissions("ph:alipayRefund:export")
    @Log(title = "支付宝退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AlipayRefund alipayRefund) {
        startPage("id desc");
        List<AlipayRefund> list = alipayRefundService.selectAlipayRefundList(alipayRefund);
        ExcelUtil<AlipayRefund> util = new ExcelUtil<AlipayRefund>(AlipayRefund.class);
        return util.exportExcel(list, "支付宝退款记录");
    }

    @RequiresPermissions("ph:alipayRefund:queryAlipayOrder")
    @GetMapping("/queryAlipayOrder/{id}")
    @ResponseBody
    public AjaxResult queryAlipayOrder(@PathVariable Long id) {
        AlipayRefund refund = alipayRefundService.selectAlipayRefundById(id);
        AlipayTradeFastpayRefundQueryResponse queryRefundResponse =
                AlipayService.INSTANCE.queryRefund(
                        refund.getOutRefundNo(),
                        refund.getOutTradeNo(),
                        refund.getTradeNo(),
                        Arrays.asList("refund_detail_item_list", "gmt_refund_pay", "deposit_back_info")
                );
        alipayRefundService.updateOnAlipayRefund(refund, queryRefundResponse);
        return AjaxResult.success(queryRefundResponse);
    }

    /**
     * 申请支付宝退款
     */
    @RequiresPermissions("ph:alipayRefund:requestAlipayRefund")
    @PostMapping("/requestAlipayRefund/{id}")
    @ResponseBody
    public AjaxResult requestAlipayRefund(@PathVariable Long id) {
        AlipayRefund refund = alipayRefundService.selectAlipayRefundById(id);
        if (!Constants.REFUND_OK.equals(refund.getHisTradeStatus())
                || Constants.REFUND_SUCCESS.equals(refund.getAlipayTradeStatus())
                || Constants.MANUAL_INIT != refund.getManualRefundState()
        ) {
            return AjaxResult.error("此订单不能申请支付宝退款");
        }

        refund.setManualRefundState(Constants.MANUAL_REQUESTED);
        boolean ok = 1 == alipayRefundService.updateAlipayRefund(refund);
        return ok ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 放行支付宝退款
     */
    @RequiresPermissions("ph:alipayRefund:approveAlipayRefund")
    @PostMapping("/approveAlipayRefund/{id}")
    @ResponseBody
    public AjaxResult approveAlipayRefund(@PathVariable Long id) {
        AlipayRefund refund = alipayRefundService.selectAlipayRefundById(id);
        if (!Constants.REFUND_OK.equals(refund.getHisTradeStatus())
                || Constants.REFUND_SUCCESS.equals(refund.getAlipayTradeStatus())
                || Constants.MANUAL_REQUESTED != refund.getManualRefundState()
        ) {
            return AjaxResult.error("此订单不能申请支付宝退款");
        }
        alipayRefundService.refundAlipay(refund);
        return AjaxResult.success();
    }

}
