package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NatPatient;
import space.lzhq.ph.mapper.NatPatientMapper;
import space.lzhq.ph.service.INatPatientService;

import java.util.List;

/**
 * 人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-11
 */
@Service
public class NatPatientServiceImpl implements INatPatientService {

    private final NatPatientMapper natPatientMapper;

    public NatPatientServiceImpl(NatPatientMapper natPatientMapper) {
        this.natPatientMapper = natPatientMapper;
    }

    /**
     * 查询人员
     *
     * @param id 人员ID
     * @return 人员
     */
    @Override
    public NatPatient selectNatPatientById(Long id) {
        return natPatientMapper.selectNatPatientById(id);
    }

    @Override
    public NatPatient selectNatPatientByIdCardNo(String idCardNo) {
        return natPatientMapper.selectNatPatientByIdCardNo(idCardNo);
    }

    @Override
    public boolean isIdCardNoUnique(String idCardNo, Long id) {
        NatPatient natPatient = selectNatPatientByIdCardNo(idCardNo);
        if (natPatient == null) {
            return true;
        } else {
            return natPatient.getId().equals(id);
        }
    }

    /**
     * 查询人员列表
     *
     * @param natPatient 人员
     * @return 人员
     */
    @Override
    public List<NatPatient> selectNatPatientList(NatPatient natPatient) {
        return natPatientMapper.selectNatPatientList(natPatient);
    }

    /**
     * 新增人员
     *
     * @param natPatient 人员
     * @return 结果
     */
    @Override
    public int insertNatPatient(NatPatient natPatient) {
        natPatient.setCreateTime(DateUtils.getNowDate());
        return natPatientMapper.insertNatPatient(natPatient);
    }

    /**
     * 修改人员
     *
     * @param natPatient 人员
     * @return 结果
     */
    @Override
    public int updateNatPatient(NatPatient natPatient) {
        return natPatientMapper.updateNatPatient(natPatient);
    }

    /**
     * 删除人员对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteNatPatientByIds(String ids) {
        return natPatientMapper.deleteNatPatientByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除人员信息
     *
     * @param id 人员ID
     * @return 结果
     */
    @Override
    public int deleteNatPatientById(Long id) {
        return natPatientMapper.deleteNatPatientById(id);
    }

}
