package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NatReport;
import space.lzhq.ph.mapper.NatReportMapper;
import space.lzhq.ph.service.INatReportService;

import java.util.Date;
import java.util.List;

@Service
public class NatReportServiceImpl implements INatReportService {

    private final NatReportMapper mapper;

    public NatReportServiceImpl(NatReportMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<NatReport> selectNatReports(String idCardNo, Date minCollectTime) {
        return mapper.selectNatReports(idCardNo, minCollectTime);
    }

}
