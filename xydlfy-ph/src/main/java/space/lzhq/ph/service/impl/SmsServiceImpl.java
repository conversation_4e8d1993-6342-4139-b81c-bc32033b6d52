package space.lzhq.ph.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import space.lzhq.ph.config.SmsConfig;
import space.lzhq.ph.domain.SmsRecord;
import space.lzhq.ph.dto.sms.SmsRequest;
import space.lzhq.ph.dto.sms.SmsResponse;
import space.lzhq.ph.dto.sms.SmsStatusRequest;
import space.lzhq.ph.dto.sms.SmsStatusResponse;
import space.lzhq.ph.exception.SmsException;
import space.lzhq.ph.service.ISmsRecordService;
import space.lzhq.ph.service.ISmsService;
import space.lzhq.ph.util.MaskingUtil;
import space.lzhq.ph.util.SmsSignUtil;
import space.lzhq.ph.util.TraceIdUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 短信服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SmsServiceImpl implements ISmsService {

    private static final Logger logger = LoggerFactory.getLogger(SmsServiceImpl.class);

    /**
     * 短信ID生成器
     */
    private static final AtomicLong SMS_ID_GENERATOR = new AtomicLong(System.currentTimeMillis());

    private final SmsConfig smsConfig;

    private final RestTemplate restTemplate;

    private final ISmsRecordService smsRecordService;

    public SmsServiceImpl(SmsConfig smsConfig, RestTemplate restTemplate, ISmsRecordService smsRecordService) {
        this.smsConfig = smsConfig;
        this.restTemplate = restTemplate;
        this.smsRecordService = smsRecordService;
    }

    @Override
    public SmsResponse sendSms(String mobile, String content) {
        return TraceIdUtil.executeWithTraceId(() -> {
            logger.info("发送普通短信: mobile={}, contentLength={}", 
                    MaskingUtil.maskMobile(mobile), content != null ? content.length() : 0);
            return sendSmsWithRecord(mobile, content, "", SmsRecord.SendMethod.NORMAL,
                    SmsRecord.BusinessType.GENERAL_NOTIFICATION, null);
        });
    }

    @Override
    public SmsResponse sendVerificationCode(String mobile, String content) {
        return TraceIdUtil.executeWithTraceId(() -> {
            logger.info("发送验证码短信: mobile={}, contentLength={}", 
                    MaskingUtil.maskMobile(mobile), content != null ? content.length() : 0);
            return sendSmsWithRecord(mobile, content, "", SmsRecord.SendMethod.VERIFICATION_CODE,
                    SmsRecord.BusinessType.VERIFICATION_CODE, null);
        });
    }

    @Override
    public SmsResponse sendSms(String mobile, String content, String extcode, SmsRecord.SendMethod sendmethod) {
        return TraceIdUtil.executeWithTraceId(() -> {
            logger.info("发送短信: mobile={}, contentLength={}, extcode={}, sendmethod={}", 
                    MaskingUtil.maskMobile(mobile), content != null ? content.length() : 0, extcode, sendmethod);
            return sendSmsWithRecord(mobile, content, extcode, sendmethod,
                    SmsRecord.BusinessType.GENERAL_NOTIFICATION, null);
        });
    }

    /**
     * 发送短信并保存记录
     *
     * @param mobile       手机号码
     * @param content      短信内容
     * @param extcode      扩展码
     * @param sendmethod   发送方式
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 发送结果
     */
    public SmsResponse sendSmsWithRecord(String mobile, String content, String extcode,
                                         SmsRecord.SendMethod sendmethod, String businessType, Long businessId) {
        return TraceIdUtil.executeWithTraceId(() -> {
            // 参数验证
            validateSendParams(mobile, content);

            // 构建请求
            SmsRequest request = buildSmsRequest(mobile, content, extcode, sendmethod);
            String sismsid = request.getSismsid().toString();

            logger.info("发送短信: sismsid={}, mobile={}, businessType={}, businessId={}", 
                    sismsid, MaskingUtil.maskMobile(mobile), businessType, businessId);

            // 保存短信记录
            SmsRecord smsRecord = null;
            try {
                smsRecord = smsRecordService.saveSmsRecord(sismsid, mobile, content,
                        sendmethod, extcode, businessType, businessId);
            } catch (Exception e) {
                logger.error("保存短信记录失败: sismsid={}, error={}", sismsid, e.getMessage());
                // 记录保存失败不影响短信发送，继续执行
            }

            // 发送请求
            String url = smsConfig.getBaseUrl() + "/sms/api/v4/orgsinglesms";

            try {
                HttpHeaders headers = buildHeaders();
                HttpEntity<SmsRequest> entity = new HttpEntity<>(request, headers);

                ResponseEntity<SmsResponse> responseEntity = restTemplate.exchange(
                        url, HttpMethod.POST, entity, SmsResponse.class);

                SmsResponse response = responseEntity.getBody();

                if (response != null) {
                    logger.info("短信发送结果: sismsid={}, success={}, code={}, message={}", 
                            sismsid, response.isSuccess(), response.getCode(), response.getMessage());

                    // 更新短信记录
                    try {
                        if (smsRecord != null) {
                            smsRecordService.updateSmsResult(sismsid, response);
                        }
                    } catch (Exception e) {
                        logger.error("更新短信记录失败: sismsid={}, error={}", sismsid, e.getMessage());
                    }

                    if (!response.isSuccess()) {
                        logger.warn("短信发送失败: sismsid={}, code={}, message={}", 
                                sismsid, response.getCode(), response.getMessage());
                    }
                } else {
                    logger.error("短信发送响应为空: sismsid={}", sismsid);
                    try {
                        if (smsRecord != null) {
                            smsRecordService.updateSmsError(sismsid, "短信发送响应为空");
                        }
                    } catch (Exception e) {
                        logger.error("更新短信错误记录失败: sismsid={}, error={}", sismsid, e.getMessage());
                    }
                    throw new SmsException("短信发送响应为空");
                }

                return response;

            } catch (RestClientException e) {
                logger.error("短信发送请求异常: sismsid={}, mobile={}, error={}", 
                        sismsid, MaskingUtil.maskMobile(mobile), e.getMessage());
                // 更新错误记录
                try {
                    if (smsRecord != null) {
                        smsRecordService.updateSmsError(sismsid, "短信发送请求异常: " + e.getMessage());
                    }
                } catch (Exception updateError) {
                    logger.error("更新短信错误记录失败: sismsid={}, error={}", sismsid, updateError.getMessage());
                }
                throw new SmsException("短信发送请求异常: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public SmsStatusResponse querySmsStatus(Long sismsid) {
        return TraceIdUtil.executeWithTraceId(() -> {
            if (sismsid == null) {
                logger.error("短信ID不能为空");
                throw new IllegalArgumentException("短信ID不能为空");
            }

            logger.info("查询短信状态: sismsid={}", sismsid);

            SmsStatusRequest request = new SmsStatusRequest();
            request.setSismsid(sismsid);

            String url = smsConfig.getBaseUrl() + "/sms/api/v4/smsstatus";

            try {
                HttpHeaders headers = buildHeaders();
                HttpEntity<SmsStatusRequest> entity = new HttpEntity<>(request, headers);

                ResponseEntity<SmsStatusResponse> responseEntity = restTemplate.exchange(
                        url, HttpMethod.POST, entity, SmsStatusResponse.class);

                SmsStatusResponse response = responseEntity.getBody();

                if (response != null) {
                    logger.info("短信状态查询结果: sismsid={}, success={}, sentresult={}, smsstatus={}", 
                            sismsid, response.isSuccess(), 
                            response.getData() != null ? response.getData().getSentresult() : "null",
                            response.getData() != null ? response.getData().getSmsstatus() : "null");
                } else {
                    logger.error("短信状态查询响应为空: sismsid={}", sismsid);
                    throw new SmsException("短信状态查询响应为空");
                }

                return response;

            } catch (RestClientException e) {
                logger.error("短信状态查询异常: sismsid={}, error={}", sismsid, e.getMessage());
                throw new SmsException("短信状态查询异常: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public SmsStatusResponse querySmsStatus(String sismsid) {
        return TraceIdUtil.executeWithTraceId(() -> {
            if (StringUtils.isBlank(sismsid)) {
                logger.error("短信ID不能为空");
                throw new IllegalArgumentException("短信ID不能为空");
            }

            try {
                Long id = Long.parseLong(sismsid);
                return querySmsStatus(id);
            } catch (NumberFormatException e) {
                logger.error("短信ID格式不正确: sismsid={}", sismsid);
                throw new IllegalArgumentException("短信ID格式不正确: " + sismsid);
            }
        });
    }

    @Override
    public SmsResponse retrySendSms(String sismsid, String mobile, String content, String extcode, Integer sendmethod) {
        return TraceIdUtil.executeWithTraceId(() -> {
            logger.info("重试发送短信: sismsid={}, mobile={}", sismsid, MaskingUtil.maskMobile(mobile));

            // 参数验证
            validateSendParams(mobile, content);

            if (StringUtils.isBlank(sismsid)) {
                logger.error("重试发送时sismsid不能为空");
                throw new IllegalArgumentException("重试发送时sismsid不能为空");
            }

            // 构建请求（使用现有的sismsid）
            SmsRequest request = buildSmsRequestForRetry(sismsid, mobile, content, extcode, sendmethod);

            // 发送请求
            String url = smsConfig.getBaseUrl() + "/sms/api/v4/orgsinglesms";

            try {
                HttpHeaders headers = buildHeaders();
                HttpEntity<SmsRequest> entity = new HttpEntity<>(request, headers);

                ResponseEntity<SmsResponse> responseEntity = restTemplate.exchange(
                        url, HttpMethod.POST, entity, SmsResponse.class);

                SmsResponse response = responseEntity.getBody();

                if (response != null) {
                    logger.info("重试短信发送结果: sismsid={}, success={}, code={}, message={}", 
                            sismsid, response.isSuccess(), response.getCode(), response.getMessage());
                } else {
                    logger.error("重试短信发送响应为空: sismsid={}", sismsid);
                    throw new SmsException("重试短信发送响应为空");
                }

                return response;

            } catch (RestClientException e) {
                logger.error("重试短信发送请求异常: sismsid={}, mobile={}, error={}", 
                        sismsid, MaskingUtil.maskMobile(mobile), e.getMessage());
                throw new SmsException("重试短信发送请求异常: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 构建重试短信请求（使用现有sismsid）
     */
    private SmsRequest buildSmsRequestForRetry(String sismsid, String mobile, String content, String extcode, Integer sendmethod) {
        SmsRequest request = new SmsRequest();
        request.setApplicationid(smsConfig.getAppId());
        request.setCompanyId(smsConfig.getCompany());
        request.setDestaddr(mobile);
        request.setExtcode(StringUtils.isNotBlank(extcode) ? extcode : "");
        request.setMessagecontent(content);
        request.setMsgfmt(0);
        request.setReqdeliveryreport(0);
        request.setRequesttime(getCurrentTimeString());
        request.setSendmethod(sendmethod != null ? sendmethod : 0);

        // 使用现有的sismsid而不是生成新的
        try {
            Long sismsidLong = Long.parseLong(sismsid);
            request.setSismsid(sismsidLong);
        } catch (NumberFormatException e) {
            logger.error("sismsid格式不正确: sismsid={}", sismsid);
            throw new IllegalArgumentException("sismsid格式不正确: " + sismsid);
        }

        return request;
    }

    /**
     * 验证发送参数
     */
    private void validateSendParams(String mobile, String content) {
        if (StringUtils.isBlank(mobile)) {
            logger.error("手机号码不能为空");
            throw new IllegalArgumentException("手机号码不能为空");
        }

        if (!mobile.matches("^1[3-9]\\d{9}$")) {
            logger.error("手机号码格式不正确: mobile={}", MaskingUtil.maskMobile(mobile));
            throw new IllegalArgumentException("手机号码格式不正确");
        }

        if (StringUtils.isBlank(content)) {
            logger.error("短信内容不能为空");
            throw new IllegalArgumentException("短信内容不能为空");
        }

        if (StringUtils.isBlank(smsConfig.getAppId())) {
            logger.error("短信服务未配置appId");
            throw new SmsException("短信服务未配置appId");
        }

        if (StringUtils.isBlank(smsConfig.getAppSecret())) {
            logger.error("短信服务未配置appSecret");
            throw new SmsException("短信服务未配置appSecret");
        }

        if (StringUtils.isBlank(smsConfig.getCompany())) {
            logger.error("短信服务未配置company");
            throw new SmsException("短信服务未配置company");
        }
    }

    /**
     * 构建短信请求
     */
    private SmsRequest buildSmsRequest(String mobile, String content, String extcode, SmsRecord.SendMethod sendmethod) {
        SmsRequest request = new SmsRequest();
        request.setApplicationid(smsConfig.getAppId());
        request.setCompanyId(smsConfig.getCompany());
        request.setDestaddr(mobile);
        request.setExtcode(StringUtils.isNotBlank(extcode) ? extcode : "");
        request.setMessagecontent(content);
        request.setMsgfmt(0);
        request.setReqdeliveryreport(0);
        request.setRequesttime(getCurrentTimeString());
        request.setSendmethod(sendmethod != null ? sendmethod.getCode() : 0);

        Long sismsid = generateSmsId();
        request.setSismsid(sismsid);

        return request;
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildHeaders() {
        String timestamp = SmsSignUtil.generateTimestamp();
        String sign = SmsSignUtil.generateSign(smsConfig.getAppId(), smsConfig.getAppSecret(), timestamp);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appId", smsConfig.getAppId());
        headers.set("timestamp", timestamp);
        headers.set("sign", sign);
        headers.set("company", smsConfig.getCompany());

        return headers;
    }

    /**
     * 获取当前时间字符串
     */
    private String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 生成短信ID
     */
    public Long generateSmsId() {
        return SMS_ID_GENERATOR.incrementAndGet();
    }
}