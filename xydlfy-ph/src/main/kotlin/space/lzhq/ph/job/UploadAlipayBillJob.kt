package space.lzhq.ph.job

import org.dromara.hutool.core.date.DatePattern
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.util.FTPUtil
import java.time.LocalDate

/**
 * 上传支付宝账单文件
 * 日账单一般会在次日 9 点生成，特殊情况可能延迟。参见：https://opendocs.alipay.com/support/01ray2
 * 0 30 10 * * ?
 */
@Component("uploadAlipayBillJob")
class UploadAlipayBillJob {

    private val log = LoggerFactory.getLogger(UploadAlipayBillJob::class.java)

    @Value("\${bill-ftp.host}")
    private lateinit var host: String

    @Value("\${bill-ftp.port}")
    private var port: Int = 21

    @Value("\${bill-ftp.user-name}")
    private lateinit var userName: String

    @Value("\${bill-ftp.password}")
    private lateinit var password: String

    fun upload() {
        uploadByBillDate()
    }

    fun uploadByBillDate(billDate: LocalDate = LocalDate.now().minusDays(1)) {
        val billFileName = "${billDate.format(DatePattern.PURE_DATE_FORMATTER)}_alipay.zip"

        try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            FTPUtil.uploadFile(host, port, userName, password, "/", billFileName, billFile.toPath())
        } catch (e: Exception) {
            log.error("上传支付宝账单文件失败", e)
        }
    }

}