<!DOCTYPE html>
<html lang="zh">
<head>
    <th:block th:include="include :: header('文件上传')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>文件上传控件 <small>https://github.com/kartik-v/bootstrap-fileinput</small></h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="font-noraml">单文件上传</label>
                        <div class="file-loading">
                            <input id="singleFile" name="file" type="file">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="font-noraml">多文件上传</label>
                        <div class="file-loading">
                            <input id="multipleFile" name="files" type="file" multiple>
                        </div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <label class="font-noraml">相关参数详细信息</label>
                        <div><a href="http://doc.ruoyi.vip/ruoyi/document/zjwd.html#bootstrap-fileinput"
                                target="_blank">http://doc.ruoyi.vip/ruoyi/document/zjwd.html#bootstrap-fileinput</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: bootstrap-fileinput-js}"/>
<script th:inline="javascript">

    $(document).ready(function () {
        // 单图上传
        $("#singleFile").fileinput({
            uploadUrl: ctx + 'common/upload',
            maxFileCount: 1,
            autoReplace: true
        }).on('fileuploaded', function (event, data, previewId, index) {
            var rsp = data.response;
            log.info("return url：" + rsp.url)
            log.info("reutrn fileName：" + rsp.fileName)
            log.info("reutrn newFileName：" + rsp.newFileName)
            log.info("return originalFilename：" + rsp.originalFilename)
        }).on('fileremoved', function (event, id, index) {
            $("input[name='" + event.currentTarget.id + "']").val('')
        })

        // 多图上传
        $("#multipleFile").fileinput({
            uploadUrl: ctx + 'common/uploads',
            uploadAsync: false
        }).on('filebatchuploadsuccess', function (event, data, previewId, index) {
            var rsp = data.response;
            log.info("return urls：" + rsp.urls)
            log.info("reutrn fileNames：" + rsp.fileNames)
            log.info("reutrn newFileNames：" + rsp.newFileNames)
            log.info("return originalFilenames：" + rsp.originalFilenames)
        }).on('fileremoved', function (event, id, index) {
            $("input[name='" + event.currentTarget.id + "']").val('')
        })
    });
</script>
</body>
</html>
