<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('问题类型列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>名称：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.treeTable.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:jubaoCaseCategory:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary" onclick="$.operate.edit()" shiro:hasPermission="ph:jubaoCaseCategory:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-info" id="expandAllBtn">
                <i class="fa fa-exchange"></i> 展开/折叠
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-tree-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    const addFlag = [[${@permission.hasPermi('ph:jubaoCaseCategory:add')}]];
    const editFlag = [[${@permission.hasPermi('ph:jubaoCaseCategory:edit')}]];
    const removeFlag = [[${@permission.hasPermi('ph:jubaoCaseCategory:remove')}]];
    const prefix = ctx + "ph/jubaoCaseCategory";

    $(function () {
        const options = {
            code: "id",
            parentCode: "parentId",
            expandColumn: "3",
            uniqueId: "id",
            url: prefix + "/list?orderByColumn=sortNo&isAsc=asc&pageSize=999999",
            createUrl: prefix + "/add/{id}",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove/{id}",
            modalName: "问题类型",
            columns: [{
                field: 'selectItem',
                radio: true
            },
                {
                    field: 'id',
                    title: '父ID',
                    align: 'left',
                    visible: false
                },
                {
                    field: 'parentId',
                    title: '父ID',
                    align: 'left',
                    visible: false
                },
                {
                    field: 'name',
                    title: '名称',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (row.parentId === 0) {
                            return '<span style="font-weight: bold">' + value + '</span>';
                        } else {
                            return '<span>&nbsp;&nbsp;&nbsp;&nbsp;' + value + '</span>';
                        }
                    }
                },
                {
                    field: 'sortNo',
                    title: '序号',
                    align: 'left'
                },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        const actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        if (row.parentId === 0) {
                            actions.push('<a class="btn btn-info  btn-xs ' + addFlag + '" href="javascript:void(0)" onclick="$.operate.add(\'' + row.id + '\')"><i class="fa fa-plus"></i>新增</a> ');
                        }
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.treeTable.init(options);
    });


</script>
</body>
</html>