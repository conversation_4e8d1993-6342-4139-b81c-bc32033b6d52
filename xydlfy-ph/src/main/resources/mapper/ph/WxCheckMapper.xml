<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WxCheckMapper">

    <resultMap type="space.lzhq.ph.domain.WxCheck" id="WxCheckResult">
        <result property="id" column="id"/>
        <result property="wxPayAmount" column="wx_pay_amount"/>
        <result property="wxMzPayAmount" column="wx_mz_pay_amount"/>
        <result property="wxZyPayAmount" column="wx_zy_pay_amount"/>
        <result property="hisPayAmount" column="his_pay_amount"/>
        <result property="diffPayAmount" column="diff_pay_amount"/>
        <result property="payBalanced" column="pay_balanced"/>
        <result property="wxRefundAmount" column="wx_refund_amount"/>
        <result property="wxMzRefundAmount" column="wx_mz_refund_amount"/>
        <result property="wxZyRefundAmount" column="wx_zy_refund_amount"/>
        <result property="hisRefundAmount" column="his_refund_amount"/>
        <result property="diffRefundAmount" column="diff_refund_amount"/>
        <result property="refundBalanced" column="refund_balanced"/>
        <result property="wxNetIn" column="wx_net_in"/>
        <result property="hisNetIn" column="his_net_in"/>
        <result property="mipPayAmount" column="mip_pay_amount"/>
        <result property="mipRefundAmount" column="mip_refund_amount"/>
        <result property="mipNetIn" column="mip_net_in"/>
        <result property="totalNetIn" column="total_net_in"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectWxCheckVo">
        select id,
        wx_pay_amount,
        wx_mz_pay_amount,
        wx_zy_pay_amount,
        his_pay_amount,
        diff_pay_amount,
        pay_balanced,
        wx_refund_amount,
        wx_mz_refund_amount,
        wx_zy_refund_amount,
        his_refund_amount,
        diff_refund_amount,
        refund_balanced,
        wx_net_in,
        his_net_in,
        mip_pay_amount,
        mip_refund_amount,
        mip_net_in,
        total_net_in,
        remark
        from ph_wx_check
    </sql>

    <select id="selectWxCheckList" parameterType="WxCheck" resultMap="WxCheckResult">
        <include refid="selectWxCheckVo"/>
        <where>
            <if test="params.minId != null and params.minId != ''">
                and id >= #{params.minId}
            </if>
            <if test="params.maxId != null and params.maxId != ''">
                and id &lt;= #{params.maxId}
            </if>
            <if test="id != null ">and id = #{id}</if>
            <if test="payBalanced != null ">and pay_balanced = #{payBalanced}</if>
            <if test="refundBalanced != null ">and refund_balanced = #{refundBalanced}</if>
        </where>
    </select>

    <select id="selectWxCheckById" parameterType="Long" resultMap="WxCheckResult">
        <include refid="selectWxCheckVo"/>
        where id = #{id}
    </select>

    <insert id="insertWxCheck" parameterType="WxCheck">
        insert into ph_wx_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="wxPayAmount != null ">wx_pay_amount,</if>
            <if test="wxMzPayAmount != null ">wx_mz_pay_amount,</if>
            <if test="wxZyPayAmount != null ">wx_zy_pay_amount,</if>
            <if test="hisPayAmount != null ">his_pay_amount,</if>
            <if test="diffPayAmount != null ">diff_pay_amount,</if>
            <if test="payBalanced != null ">pay_balanced,</if>
            <if test="wxRefundAmount != null ">wx_refund_amount,</if>
            <if test="wxMzRefundAmount != null ">wx_mz_refund_amount,</if>
            <if test="wxZyRefundAmount != null ">wx_zy_refund_amount,</if>
            <if test="hisRefundAmount != null ">his_refund_amount,</if>
            <if test="diffRefundAmount != null ">diff_refund_amount,</if>
            <if test="refundBalanced != null ">refund_balanced,</if>
            <if test="wxNetIn != null ">wx_net_in,</if>
            <if test="hisNetIn != null ">his_net_in,</if>
            <if test="mipPayAmount != null ">mip_pay_amount,</if>
            <if test="mipRefundAmount != null ">mip_refund_amount,</if>
            <if test="mipNetIn != null ">mip_net_in,</if>
            <if test="totalNetIn != null ">total_net_in,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="wxPayAmount != null ">#{wxPayAmount},</if>
            <if test="wxMzPayAmount != null ">#{wxMzPayAmount},</if>
            <if test="wxZyPayAmount != null ">#{wxZyPayAmount},</if>
            <if test="hisPayAmount != null ">#{hisPayAmount},</if>
            <if test="diffPayAmount != null ">#{diffPayAmount},</if>
            <if test="payBalanced != null ">#{payBalanced},</if>
            <if test="wxRefundAmount != null ">#{wxRefundAmount},</if>
            <if test="wxMzRefundAmount != null ">#{wxMzRefundAmount},</if>
            <if test="wxZyRefundAmount != null ">#{wxZyRefundAmount},</if>
            <if test="hisRefundAmount != null ">#{hisRefundAmount},</if>
            <if test="diffRefundAmount != null ">#{diffRefundAmount},</if>
            <if test="refundBalanced != null ">#{refundBalanced},</if>
            <if test="wxNetIn != null ">#{wxNetIn},</if>
            <if test="hisNetIn != null ">#{hisNetIn},</if>
            <if test="mipPayAmount != null ">#{mipPayAmount},</if>
            <if test="mipRefundAmount != null ">#{mipRefundAmount},</if>
            <if test="mipNetIn != null ">#{mipNetIn},</if>
            <if test="totalNetIn != null ">#{totalNetIn},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateWxCheck" parameterType="WxCheck">
        update ph_wx_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="wxPayAmount != null ">wx_pay_amount = #{wxPayAmount},</if>
            <if test="wxMzPayAmount != null ">wx_mz_pay_amount = #{wxMzPayAmount},</if>
            <if test="wxZyPayAmount != null ">wx_zy_pay_amount = #{wxZyPayAmount},</if>
            <if test="hisPayAmount != null ">his_pay_amount = #{hisPayAmount},</if>
            <if test="diffPayAmount != null ">diff_pay_amount = #{diffPayAmount},</if>
            <if test="payBalanced != null ">pay_balanced = #{payBalanced},</if>
            <if test="wxRefundAmount != null ">wx_refund_amount = #{wxRefundAmount},</if>
            <if test="wxMzRefundAmount != null ">wx_mz_refund_amount = #{wxMzRefundAmount},</if>
            <if test="wxZyRefundAmount != null ">wx_zy_refund_amount = #{wxZyRefundAmount},</if>
            <if test="hisRefundAmount != null ">his_refund_amount = #{hisRefundAmount},</if>
            <if test="diffRefundAmount != null ">diff_refund_amount = #{diffRefundAmount},</if>
            <if test="refundBalanced != null ">refund_balanced = #{refundBalanced},</if>
            <if test="wxNetIn != null ">wx_net_in = #{wxNetIn},</if>
            <if test="hisNetIn != null ">his_net_in = #{hisNetIn},</if>
            <if test="mipPayAmount != null ">mip_pay_amount = #{mipPayAmount},</if>
            <if test="mipRefundAmount != null ">mip_refund_amount = #{mipRefundAmount},</if>
            <if test="mipNetIn != null ">mip_net_in = #{mipNetIn},</if>
            <if test="totalNetIn != null ">total_net_in = #{totalNetIn},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxCheckById" parameterType="Long">
        delete
        from ph_wx_check
        where id = #{id}
    </delete>

    <delete id="deleteWxCheckByIds" parameterType="String">
        delete from ph_wx_check where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>