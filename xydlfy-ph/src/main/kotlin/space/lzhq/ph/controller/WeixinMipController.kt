package space.lzhq.ph.controller

import com.github.binarywang.wxpay.bean.request.WxInsurancePayUnifiedOrderRequest
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult
import com.github.binarywang.wxpay.bean.result.WxInsurancePayUnifiedOrderResult
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.crypto.digest.MD5
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.AvailableRegistrationResponseV1
import org.mospital.dongruan.bean.PrescriptionDetailResponse
import org.mospital.dongruan.bean.PrescriptionResponse
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.*
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.controller.MipOrderCreateForm.Fee
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IMipWxOrderService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@RestController
@RequestMapping("/api/weixinMip")
class WeixinMipController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    private fun getEnvByTestMode(testMode: Boolean): String {
        return if (testMode) "WeixinTest" else "WeixinProd"
    }

    /**
     * 获取待结算费用清单
     */
    @GetMapping("pendingSettlements/v1")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlementsV1(
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val availableRegistrationResponse: AvailableRegistrationResponseV1 =
            DongruanService.queryAvailableRegistrationsV1(cardNo = currentPatient.cardNo, testMode = testMode)
        if (!availableRegistrationResponse.isOk()) {
            return AjaxResult.error(availableRegistrationResponse.msg)
        }

        val minRegisterTime = LocalDate.now().minusDays(2).atStartOfDay()
            .format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN))
        val availableRegistrations: List<AvailableRegistrationResponseV1.Registration> =
            availableRegistrationResponse.registrations.filter {
                it.registerTime >= minRegisterTime
            }
        availableRegistrations.forEach { registration ->
            val prescriptionResponse: PrescriptionResponse =
                DongruanService.queryPrescriptions(
                    clinicNo = registration.clinicNo,
                    testMode = testMode
                )
            if (prescriptionResponse.isOk()) {
                registration.prescriptions = prescriptionResponse.prescriptions
                    .filter {
                        // 返回未收费、非慢病、非急诊的处方
                        !it.isCharged() && !it.isChronic() && !it.isEmergency()
                    }
                    .onEach { it.registerTime = registration.registerTime }
                    .toMutableList()
            } else {
                logger.warn("Failed to query prescriptions for clinicNo=${registration.clinicNo}")
            }
        }
        return AjaxResult.success(availableRegistrations.filter { it.prescriptions.isNotEmpty() })
    }

    /**
     * 获取待结算费用明细
     */
    @GetMapping("/prescriptionDetail")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPrescriptionDetail(
        @RequestParam clinicNo: String,
        @RequestParam prescriptionNo: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val prescriptionDetailResponse: PrescriptionDetailResponse = DongruanService.queryPrescriptionDetails(
            clinicNo = clinicNo,
            prescriptionNo = prescriptionNo,
            testMode = testMode
        )
        return if (prescriptionDetailResponse.isOk()) {
            AjaxResult.success(prescriptionDetailResponse.prescriptionDetails)
        } else {
            AjaxResult.error(prescriptionDetailResponse.msg)
        }
    }

    /**
     * 获取授权地址
     * @param accountType 账户类型：self=本人医保，family=亲属医保
     */
    @GetMapping("authUrl")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getAuthUrl(
        @RequestParam accountType: String,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val authUrl = WecityMipService.buildAuthUrl(
            clientType = ClientType.MA,
            accountType = accountType,
            userName = activePatient.name,
            userCardNo = activePatient.idCardNo,
        )
        return AjaxResult.success(authUrl)
    }

    private fun doCreateOrder(form: MipOrderCreateForm): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val validateResult = form.validate()
        if (validateResult != null) {
            return AjaxResult.error(validateResult)
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val userQueryResult: UserQueryResult = WecityMipService.queryUser(
            qrcode = form.mipAuthCode,
            openid = currentPatient.openId
        )
        if (!userQueryResult.isOK()) {
            return AjaxResult.error(userQueryResult.message)
        }
        if (userQueryResult.cityId.isNullOrBlank()) {
            return AjaxResult.error("获取医保参保地失败")
        }
        if (!userQueryResult.cityId!!.startsWith("65")) {
            return AjaxResult.error("目前仅支持疆内异地结算")
        }
        if (userQueryResult.isSelfPay() && (userQueryResult.userCardNo != currentPatient.idCardNo || userQueryResult.userName != currentPatient.name)) {
            return AjaxResult.error("医保信息与当前就诊人信息不一致")
        }

        val payAuthNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.payAuthNo!!
        } else if (userQueryResult.isFamilyPay()) {
            userQueryResult.familyPayAuthNo!!
        } else {
            // Unreachable
            return AjaxResult.error("未知的医保支付方式")
        }

        val fees: List<CreateOrderForm.Fee> = form.feeList.stream().distinct().map {
            CreateOrderForm.Fee(
                prescriptionNo = it.prescriptionNo,
                amount = it.amount,
            )
        }.toList()

        val userName = if (userQueryResult.isSelfPay()) {
            userQueryResult.userName!!
        } else {
            currentPatient.name
        }
        val userCardNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.userCardNo!!
        } else {
            currentPatient.idCardNo
        }

        val createOrderResponse: Response<CreateOrderResult> = DongruanYibaoService.createOrder(
            form = CreateOrderForm(
                clinicNo = form.clinicNo,
                feeList = fees,
                totalFee = form.totalFee,
                payAuthNo = payAuthNo,
                ecToken = "",
                personName = userName,
                idCardNo = userCardNo,
                insuCode = userQueryResult.cityId!!,
                medType = "11",
                acctUsedFlag = form.acctUsedFlag,
                latlnt = userQueryResult.longitudeLatitude?.toString() ?: "0,0"
            ),
            thirdType = ThirdType.WEIXIN,
            extendParams = Request.ExtendParams(appId = WeixinMipSetting.ma.orgAppId),
            env = getEnvByTestMode(form.testMode)
        )
        return if (createOrderResponse.isOk()) {
            val preSettlement: CreateOrderResult.PreSettlement = createOrderResponse.data!!.preSettlement
            val mipWxOrder = MipWxOrder(userQueryResult, currentPatient, preSettlement)
            mipWxOrder.clinicNo = form.clinicNo
            mipWxOrder.feesJson = JacksonKit.writeValueAsString(fees)
            mipWxOrder.payToken = createOrderResponse.data!!.payToken
            mipWxOrder.medOrgOrd = createOrderResponse.data!!.medOrgOrd
            mipWxOrder.testMode = form.testMode
            mipWxOrderService.save(mipWxOrder)
            AjaxResult.success(createOrderResponse.data!!)
        } else {
            AjaxResult.error(createOrderResponse.message)
        }
    }

    /**
     * 创建订单
     */
    @PostMapping("createOrder")
    @RequireSession(clientType = space.lzhq.ph.common.ClientType.WEIXIN)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrder(
        @RequestParam clinicNo: String,
        @RequestParam prescriptionNo: String,
        @RequestParam amount: BigDecimal,
        @RequestParam mipAuthCode: String,
        @RequestParam(required = false, defaultValue = "1") acctUsedFlag: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val form = MipOrderCreateForm(
            clinicNo = clinicNo,
            feeList = listOf(Fee(prescriptionNo = prescriptionNo, amount = amount)),
            mipAuthCode = mipAuthCode,
            acctUsedFlag = acctUsedFlag,
            testMode = testMode
        )
        return doCreateOrder(form)
    }

    @PostMapping("createOrder/v1")
    @RequireSession(clientType = space.lzhq.ph.common.ClientType.WEIXIN)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrderV1(@RequestBody form: MipOrderCreateForm): AjaxResult {
        return doCreateOrder(form)
    }

    /**
     * 支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @PostMapping("payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun payOrder(
        @RequestParam payOrderId: String,
    ): AjaxResult {
        synchronized(payOrderId.intern()) {
            val currentPatient: Patient = request.getCurrentPatient()
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(payOrderId)
                ?: return AjaxResult.error("订单不存在")
            if (mipWxOrder.openid != currentPatient.openId) {
                return AjaxResult.error("订单异常")
            }
            if (mipWxOrder.hospitalOutTradeNo.isNotBlank() || mipWxOrder.medTransactionId.isNotBlank()) {
                return AjaxResult.error("订单已支付")
            }

            val request: WxInsurancePayUnifiedOrderRequest = WxInsurancePayUnifiedOrderRequest().apply {
                this.orderType = Constants.ORDER_TYPE_DiagPay
                this.openid = currentPatient.openId
                this.hospOutTradeNo = PaymentKit.newOrderId(ServiceType.YB)
                this.hospitalName = com.ruoyi.common.constant.Constants.MERCHANT_NAME
                // 总共需要支付现金金额，对应 feeSumamt
                this.totalFee = PaymentKit.yuanToFen(mipWxOrder.feeSumAmount).toInt()
                // 现金需要支付的金额，对应 ownPayAmt
                this.cashFee = PaymentKit.yuanToFen(mipWxOrder.ownPayAmount).toInt()
                this.payType = if (this.cashFee > 0) 3 else 2
                // 医保支付金额，医保基金支付+个人账户支出，对应 fundPay + psnAcctPay
                this.insuranceFee =
                    PaymentKit.yuanToFen(mipWxOrder.fundPayAmount + mipWxOrder.personalAccountPayAmount).toInt()
                this.spbillCreateIp = ServletUtil.getClientIP(request)
                this.notifyUrl = "https://ylyyyy.xjyqtl.cn:19000/open/wx/onWxInsurancePay"
                this.returnUrl = "pages_yibao/OrderResult/OrderResult?hospOutTradeNo=${this.hospOutTradeNo}"
                this.body = com.ruoyi.common.constant.Constants.MERCHANT_NAME + "-诊间缴费"
                this.userCardType = 1
                this.userCardNo = MD5.of().digestHex(mipWxOrder.userCardNo)
                this.userName = mipWxOrder.userName
                this.longitude = mipWxOrder.longitudeLatitude.split(",")[0].trim()
                this.latitude = mipWxOrder.longitudeLatitude.split(",")[1].trim()
                this.gmtOutCreate = DateUtil.format(Date(), DatePattern.PURE_DATETIME_PATTERN)
                this.serialNo = mipWxOrder.medOrgOrd
                this.payAuthNo = mipWxOrder.payAuthNo
                this.payOrderId = mipWxOrder.payOrderId
                this.cityId = WeixinMipSetting.ma.cityId
                this.orgNo = WeixinMipSetting.ma.orgCode
                this.channelNo = WeixinMipSetting.ma.channelNo

                if (!mipWxOrder.isSelfPay) {
                    this.extends = JacksonKit.writeValueAsString(
                        mapOf(
                            "rel_user_name_md5" to MD5.of().digestHex(mipWxOrder.patientName),
                            "rel_user_card_no_md5" to MD5.of().digestHex(mipWxOrder.patientIdCardNo)
                        )
                    )
                }
            }
            try {
                val wxInsurancePayUnifiedOrderResult: WxInsurancePayUnifiedOrderResult =
                    WeixinExt.getWxInsurancePayService().unifiedOrder(request)
                mipWxOrder.hospitalOutTradeNo = request.hospOutTradeNo
                mipWxOrder.medTransactionId = wxInsurancePayUnifiedOrderResult.medTransId
                mipWxOrder.cashOrderCreateTime = LocalDateTime.now()
                mipWxOrderService.updateById(mipWxOrder)
                return AjaxResult.success(
                    mapOf(
                        "medTransactionId" to wxInsurancePayUnifiedOrderResult.medTransId,
                        "payUrl" to wxInsurancePayUnifiedOrderResult.payUrl,
                        "payAppId" to wxInsurancePayUnifiedOrderResult.payAppId
                    )
                )
            } catch (e: Exception) {
                return AjaxResult.error(e.message)
            }
        }
    }

    @GetMapping("queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOrder(
        @RequestParam payOrderId: String
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipWxOrder.openid != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        // 如果已经有hisInvoiceNo，说明已经结算成功
        if (!mipWxOrder.hisInvoiceNo.isNullOrBlank()) {
            val wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult =
                WeixinExt.getWxInsurancePayService().queryOrder(
                    medTransId = mipWxOrder.medTransactionId,
                    hospOutTradeNo = ""
                )
            mipWxOrderService.updateTradeStatus(mipWxOrder.id, wxInsurancePayOrderQueryResult)
            return AjaxResult.success(wxInsurancePayOrderQueryResult)
        }

        try {
            val wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult =
                WeixinExt.getWxInsurancePayService().queryOrder(
                    medTransId = mipWxOrder.medTransactionId,
                    hospOutTradeNo = ""
                )
            mipWxOrderService.updateTradeStatus(mipWxOrder.id, wxInsurancePayOrderQueryResult)
            if (wxInsurancePayOrderQueryResult.medTradeState == "PAYING") {
                return AjaxResult.error("RETRY_REQUIRED")
            }
            if (wxInsurancePayOrderQueryResult.medTradeState != "SUCCESS") {
                return AjaxResult.success(wxInsurancePayOrderQueryResult)
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("RETRY_REQUIRED")
        }

        val yibaoOrderStatusResponse: Response<QueryOrderResult> = DongruanYibaoService.queryOrder(
            form = QueryOrderForm(
                clinicNo = mipWxOrder.clinicNo,
                payOrderId = mipWxOrder.payOrderId,
                personName = mipWxOrder.patientName,
                idCardNo = mipWxOrder.patientIdCardNo,
            ),
            thirdType = ThirdType.WEIXIN,
            extendParams = Request.ExtendParams(appId = WeixinMipSetting.ma.orgAppId),
            env = getEnvByTestMode(mipWxOrder.testMode)
        )
        return if (yibaoOrderStatusResponse.isOk()) {
            if (yibaoOrderStatusResponse.data?.orderStatus == "6") {
                try {
                    HisExt.settleWeixinMipOrder(mipWxOrder.hospitalOutTradeNo)
                    val wxInsurancePayOrderQueryResult: WxInsurancePayOrderQueryResult =
                        WeixinExt.getWxInsurancePayService().queryOrder(
                            medTransId = mipWxOrder.medTransactionId,
                            hospOutTradeNo = ""
                        )
                    mipWxOrderService.updateTradeStatus(mipWxOrder.id, wxInsurancePayOrderQueryResult)
                    AjaxResult.success(wxInsurancePayOrderQueryResult)
                } catch (e: Exception) {
                    logger.debug(e.message, e)
                    AjaxResult.error(e.message)
                }
            } else {
                AjaxResult.error("RETRY_REQUIRED")
            }
        } else {
            if (mipWxOrder.ownPayAmount > BigDecimal.ZERO) {
                CoroutineScope(Dispatchers.IO).launch {
                    WeixinExt.refundWeixin(mipWxOrder)
                }
                AjaxResult.error("HIS执行失败，将自动退还您的钱款")
            } else {
                AjaxResult.error("HIS执行失败，请稍后重试")
            }
        }
    }

    data class RefundOrderForm(
        val payOrderId: String,
    )

    @PostMapping("refundOrder")
    fun refundOrder(
        @RequestBody form: RefundOrderForm,
    ): AjaxResult {
        try {
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(form.payOrderId)
                ?: return AjaxResult.error("订单不存在")

            val refundResult: WxInsurancePayRefundResult = WeixinExt.refundWeixin(mipWxOrder)
            return AjaxResult.success(refundResult)
        } catch (e: Exception) {
            return AjaxResult.error(e.message)
        }
    }

}
