package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.NatSampleSummary;

import java.util.Date;
import java.util.List;

/**
 * 采样日报Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-13
 */
@Repository
public interface NatSampleSummaryMapper {
    /**
     * 查询采样日报
     *
     * @param id 采样日报ID
     * @return 采样日报
     */
    NatSampleSummary selectNatSampleSummaryById(Long id);

    /**
     * 查询采样日报列表
     *
     * @param natSampleSummary 采样日报
     * @return 采样日报集合
     */
    List<NatSampleSummary> selectNatSampleSummaryList(NatSampleSummary natSampleSummary);

    /**
     * 重新统计采样日报
     *
     * @param minTime 最小采样时间
     * @param maxTime 最大采样时间
     * @return 采样日报
     */
    NatSampleSummary redoSummarize(@Param("minTime") Date minTime, @Param("maxTime") Date maxTime);

    /**
     * 新增采样日报
     *
     * @param natSampleSummary 采样日报
     * @return 结果
     */
    int insertNatSampleSummary(NatSampleSummary natSampleSummary);

    /**
     * 修改采样日报
     *
     * @param natSampleSummary 采样日报
     * @return 结果
     */
    int updateNatSampleSummary(NatSampleSummary natSampleSummary);

    /**
     * 删除采样日报
     *
     * @param id 采样日报ID
     * @return 结果
     */
    int deleteNatSampleSummaryById(Long id);

    /**
     * 批量删除采样日报
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteNatSampleSummaryByIds(String[] ids);
}
