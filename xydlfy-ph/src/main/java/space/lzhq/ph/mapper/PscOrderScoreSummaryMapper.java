package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscOrderScoreSummary;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface PscOrderScoreSummaryMapper {

    /**
     * 汇总
     *
     * @param minCreateTime 最小创建时间
     * @param maxCreateTime 最大创建时间
     * @return 汇总结果
     */
    List<PscOrderScoreSummary> summary(
            @Param("minCreateTime") LocalDateTime minCreateTime,
            @Param("maxCreateTime") LocalDateTime maxCreateTime
    );

}
