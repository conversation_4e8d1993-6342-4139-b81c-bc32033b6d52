<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('微信订阅消息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>模板ID：</label>
                                <input type="text" name="templateId"/>
                            </li>
                            <li>
                                <label>openid：</label>
                                <input type="text" name="openid"/>
                            </li>
                            <li>
                                <label>消息类型：</label>
                                <select name="type">
                                    <option value="">所有</option>
                                    <option value="-1">代码生成请选择字典属性</option>
                                </select>
                            </li>
                            <li>
                                <label>关联ID：</label>
                                <input type="text" name="companionId"/>
                            </li>
                            <li class="select-time">
                                <label>发送时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginSendTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endSendTime]"/>
                            </li>
                            <li>
                                <label>发送状态：</label>
                                <select name="sendStatus" th:with="type=${@dict.getType('ok_fail')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:wxsubscribemessage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:wxsubscribemessage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ph:wxsubscribemessage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:wxsubscribemessage:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ph:wxsubscribemessage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ph:wxsubscribemessage:remove')}]];
        var sendStatusDatas = [[${@dict.getType('ok_fail')}]];
        var prefix = ctx + "ph/wxsubscribemessage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "微信订阅消息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id', 
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'templateId', 
                    title: '模板ID'
                },
                {
                    field: 'openid', 
                    title: 'openid'
                },
                {
                    field: 'data', 
                    title: '消息内容'
                },
                {
                    field: 'type', 
                    title: '消息类型'
                },
                {
                    field: 'companionId', 
                    title: '关联ID'
                },
                {
                    field: 'sendTime', 
                    title: '发送时间'
                },
                {
                    field: 'sendStatus', 
                    title: '发送状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(sendStatusDatas, value);
                    }
                },
                {
                    field: 'error', 
                    title: '错误信息'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>