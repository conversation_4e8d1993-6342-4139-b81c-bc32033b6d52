<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PatientMapper">

    <resultMap type="space.lzhq.ph.domain.Patient" id="PatientResult">
        <result property="id" column="id"/>
        <result property="clientType" column="client_type"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="accountNo" column="account_no"/>
        <result property="jzCardNo" column="jz_card_No"/>
        <result property="cardNo" column="card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="mobile" column="mobile"/>
        <result property="nation" column="nation"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="createTime" column="create_time"/>
        <result property="active" column="active"/>
        <result property="empi" column="empi"/>
        <result property="erhcCardNo" column="erhc_card_no"/>
        <result property="ruyuanTime" column="ruyuan_time"/>
        <result property="chuyuanTime" column="chuyuan_time"/>
    </resultMap>

    <resultMap id="PatientDailyIncrementResult" type="space.lzhq.ph.domain.PatientDailyIncrement">
        <result property="date" column="date"/>
        <result property="count" column="count"/>
    </resultMap>

    <sql id="selectPatientVo">
        select id,
        client_type,
        id_card_no,
        account_no,
        jz_card_No,
        card_no,
        zhuyuan_no,
        name,
        gender,
        mobile,
        nation,
        open_id,
        union_id,
        create_time,
        active,
        empi,
        erhc_card_no,
        ruyuan_time,
        chuyuan_time
        from ph_patient
    </sql>

    <select id="selectPatientList" parameterType="Patient" resultMap="PatientResult">
        <include refid="selectPatientVo"/>
        <where>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="accountNo != null  and accountNo != ''">and account_no = #{accountNo}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_No = #{jzCardNo}</if>
            <if test="cardNo != null  and cardNo != ''">and card_no = #{cardNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="erhcCardNo != null  and erhcCardNo != ''">and erhc_card_no = #{erhcCardNo}</if>
            <if test="clientType != null">and client_type = #{clientType}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="gender != null ">and gender = #{gender}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="unionId != null  and unionId != ''">and union_id = #{unionId}</if>
            <if test="active != null ">and active = #{active}</if>
            <if test="empi != null  and empi != ''">and empi = #{empi}</if>
        </where>
    </select>

    <select id="selectPatientById" parameterType="Long" resultMap="PatientResult">
        <include refid="selectPatientVo"/>
        where id = #{id}
    </select>

    <select id="selectActivePatientByOpenId" parameterType="String" resultMap="PatientResult">
        <include refid="selectPatientVo"/>
        where open_id = #{openId} and active = 1 limit 1
    </select>

    <select id="statisticsByDay" parameterType="space.lzhq.ph.domain.PatientDailyIncrement"
            resultMap="PatientDailyIncrementResult">
        select date(create_time) as `date`, count(*) as `count` from ph_patient
        <where>
            <if test="params.startDate != null and params.startDate != ''">
                and date(create_time) >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and date(create_time) &lt;= #{params.endDate}
            </if>
        </where>
        group by date(create_time)
        order by date(create_time) asc
    </select>

    <insert id="insertPatient" parameterType="Patient" useGeneratedKeys="true" keyProperty="id">
        insert into ph_patient
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientType != null">client_type,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="accountNo != null">account_no,</if>
            <if test="jzCardNo != null">jz_card_No,</if>
            <if test="cardNo != null">card_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="name != null">name,</if>
            <if test="gender != null ">gender,</if>
            <if test="mobile != null">mobile,</if>
            <if test="nation != null">nation,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="active != null ">active,</if>
            <if test="empi != null">empi,</if>
            <if test="erhcCardNo != null">erhc_card_no,</if>
            <if test="ruyuanTime != null">ruyuan_time,</if>
            <if test="chuyuanTime != null">chuyuan_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientType != null">#{clientType},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="accountNo != null">#{accountNo},</if>
            <if test="jzCardNo != null">#{jzCardNo},</if>
            <if test="cardNo != null">#{cardNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="name != null">#{name},</if>
            <if test="gender != null ">#{gender},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="nation != null">#{nation},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="active != null ">#{active},</if>
            <if test="empi != null">#{empi},</if>
            <if test="erhcCardNo != null">#{erhcCardNo},</if>
            <if test="ruyuanTime != null">#{ruyuanTime},</if>
            <if test="chuyuanTime != null">#{chuyuanTime},</if>
        </trim>
    </insert>

    <update id="updatePatient" parameterType="Patient">
        update ph_patient
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientType != null">client_type = #{clientType},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="accountNo != null">account_no = #{accountNo},</if>
            <if test="jzCardNo != null">jz_card_No = #{jzCardNo},</if>
            <if test="cardNo != null">card_no = #{cardNo},</if>
            <if test="zhuyuanNo != null">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null ">gender = #{gender},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="active != null ">active = #{active},</if>
            <if test="empi != null">empi = #{empi},</if>
            <if test="erhcCardNo != null">erhc_card_no = #{erhcCardNo},</if>
            <if test="ruyuanTime != null">ruyuan_time = #{ruyuanTime},</if>
            <if test="chuyuanTime != null">chuyuan_time = #{chuyuanTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="switchActivePatient">
        UPDATE ph_patient
        SET active=IF(jz_card_No=#{jzCardNo}, 1, 0)
        WHERE open_id=#{openId}
    </update>

    <delete id="deletePatientById" parameterType="Long">
        delete
        from ph_patient
        where id = #{id}
    </delete>

    <delete id="deletePatientByIds" parameterType="String">
        delete from ph_patient where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
