package space.lzhq.ph.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsGetResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsRequest;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.config.ServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.data.PhoneUtil;
import org.dromara.hutool.core.date.DatePattern;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.jetbrains.annotations.NotNull;
import org.mospital.common.StringKit;
import org.mospital.dongruan.qingtui.DongruanQingtuiService;
import org.mospital.dongruan.qingtui.ThirdPartyClearanceRequest;
import org.mospital.dongruan.qingtui.ThirdPartyClearanceResponse;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration;
import space.lzhq.ph.domain.SmsRecord;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.domain.WxTransferSearchForm;
import space.lzhq.ph.exception.CardRefundException;
import space.lzhq.ph.exception.WxTransferException;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.mapper.WxTransferMapper;
import space.lzhq.ph.service.ISmsRecordService;
import space.lzhq.ph.service.ISmsService;
import space.lzhq.ph.service.IWxTransferService;
import space.lzhq.ph.util.QueryWrapperUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Validated
public class WxTransferServiceImpl extends ServiceImpl<WxTransferMapper, WxTransfer> implements IWxTransferService {

    private static final int EXPIRE_TYPE_DAYS = 1;
    private static final int EXPIRE_INTERVAL_ONE_DAY = 1;
    private static final String SUCCESS = "SUCCESS";

    private final WxPayService wxPayService;
    private final ServerConfig serverConfig;
    private final WxSubscribeMessageConfiguration wxSubscribeMessageConfiguration;
    private final ISmsService smsService;
    private final ISmsRecordService smsRecordService;
    private final WxMaService wxMaService;

    public WxTransferServiceImpl(WxPayService wxPayService, ServerConfig serverConfig, WxSubscribeMessageConfiguration wxSubscribeMessageConfiguration, ISmsService smsService, ISmsRecordService smsRecordService, WxMaService wxMaService) {
        this.wxPayService = wxPayService;
        this.serverConfig = serverConfig;
        this.wxSubscribeMessageConfiguration = wxSubscribeMessageConfiguration;
        this.smsService = smsService;
        this.smsRecordService = smsRecordService;
        this.wxMaService = wxMaService;
    }

    @Override
    public List<WxTransfer> listBySearchForm(WxTransferSearchForm searchForm) {
        LambdaQueryWrapper<WxTransfer> queryWrapper = new LambdaQueryWrapper<>();
        if (searchForm != null) {

            QueryWrapperUtils.eqIfNotBlank(queryWrapper, WxTransfer::getCardNo, searchForm.getCardNo());

            QueryWrapperUtils.eqIfNotBlank(queryWrapper, WxTransfer::getIdCardNo, searchForm.getIdCardNo());

            QueryWrapperUtils.eqIfNotBlank(queryWrapper, WxTransfer::getOutBillNo, searchForm.getOutBillNo());

            QueryWrapperUtils.eqIfNotBlank(queryWrapper, WxTransfer::getTransferBillNo, searchForm.getTransferBillNo());

            if (SUCCESS.equalsIgnoreCase(searchForm.getTransferState())) {
                queryWrapper.eq(WxTransfer::getTransferState, SUCCESS);
            }
            if ("FAILED".equalsIgnoreCase(searchForm.getTransferState())) {
                queryWrapper.and(qw ->
                        qw.ne(WxTransfer::getTransferState, SUCCESS)
                                .or()
                                .ne(WxTransfer::getTransferFailReason, "")
                );
            }
        }

        return this.list(queryWrapper);
    }

    @Override
    public List<WxTransfer> listByApplicationIdAndCardNo(long applicationId, @NotNull String cardNo) {
        return lambdaQuery()
                .eq(WxTransfer::getApplicationId, applicationId)
                .eq(WxTransfer::getCardNo, cardNo)
                .orderByDesc(WxTransfer::getId)
                .list();
    }

    @Override
    public List<WxTransfer> listByMinCreateTimeAndTransferState(LocalDateTime minCreateTime, String transferState) {
        return lambdaQuery()
                .ge(WxTransfer::getCreateTime, minCreateTime)
                .eq(WxTransfer::getTransferState, transferState)
                .list();
    }

    public Optional<WxTransfer> getOneByOutBillNo(String outBillNo) {
        return lambdaQuery().eq(WxTransfer::getOutBillNo, outBillNo).oneOpt();
    }

    @Override
    public void requestTransfer(WxTransfer wxTransfer) throws CardRefundException {
        final String outBillNo = PaymentKit.INSTANCE.newOrderId(ServiceType.TF);
        int amountCents = (int) PaymentKit.INSTANCE.yuanToFen(wxTransfer.getAmount());
        String appId = wxPayService.getConfig().getAppId();

        TransferBillsRequest request = TransferBillsRequest.newBuilder()
                .appid(appId)
                .outBillNo(outBillNo)
                .transferSceneId("1000")
                .openid(wxTransfer.getOpenId())
                .notifyUrl(serverConfig.getOnWxTransferUrl())
                .userName("")
                .transferAmount(amountCents)
                .transferRemark("门诊预交金返还")
                .userRecvPerception("活动奖励")
                .transferSceneReportInfos(List.of(
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("活动名称")
                                .infoContent("门诊预交金余额清退")
                                .build(),
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("奖励说明")
                                .infoContent("门诊预交金返还")
                                .build()
                ))
                .build();
        wxTransfer.setOutBillNo(outBillNo);

        try {
            TransferBillsResult transferResult = wxPayService.getTransferService().transferBills(request);
            updateTransferInfo(wxTransfer, transferResult);
        } catch (WxPayException e) {
            log.error(e.getErrCodeDes(), e);
            wxTransfer.setCreateTime(LocalDateTime.now());
            wxTransfer.setTransferFailReason(e.getMessage());
        }

        updateById(wxTransfer);
    }

    @Override
    public void updateTransferState(WxTransfer wxTransfer) {
        if (wxTransfer == null || StringUtils.isBlank(wxTransfer.getOutBillNo())) {
            log.warn("更新转账状态失败：商户订单号为空");
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (wxTransfer.getCreateTime().plusDays(30).isBefore(LocalDateTime.now())) {
            log.warn("单号：{}，错误：单据超过30天无法通过API查询", wxTransfer.getOutBillNo());
            return;
        }

        try {
            TransferBillsGetResult result = wxPayService.getTransferService()
                    .getBillsByOutBillNo(wxTransfer.getOutBillNo());

            updateTransferStateInfo(wxTransfer, result);
            updateById(wxTransfer);

            if (wxTransfer.isSuccessTransfer()) {
                notifyHis(wxTransfer.getId());
            }

            log.info("转账状态更新成功，单号：{}，状态：{}",
                    wxTransfer.getOutBillNo(), result.getState());
        } catch (WxPayException e) {
            log.error("查询转账状态失败，单号：{}，错误：{}",
                    wxTransfer.getOutBillNo(), e.getMessage(), e);
            throw new WxTransferException("查询转账状态失败", e);
        }
    }

    @Override
    public void notifyHis(Long id) {
        WxTransfer wxTransfer = getById(id);
        if (wxTransfer == null) {
            return;
        }

        if (wxTransfer.isSuccessNotifyHis()) {
            return;
        }

        if (!wxTransfer.isSuccessTransfer()) {
            return;
        }

        ThirdPartyClearanceResponse response = DongruanQingtuiService.Companion.getMe().writeBackThirdPartyClearance(
                new ThirdPartyClearanceRequest(
                        wxTransfer.getCardNo(),
                        wxTransfer.getOutBillNo(),
                        wxTransfer.getUpdateTime(),
                        wxTransfer.getAmount(),
                        wxTransfer.getHisRegisterId(),
                        wxTransfer.getName() + "," + wxTransfer.getMobile() + "," + wxTransfer.getAmount()
                )
        );

        wxTransfer.setHisNotificationTime(LocalDateTime.now());
        if (response.isOk()) {
            wxTransfer.setHisNotificationState(1);
            wxTransfer.setHisNotificationResult("OK");
        } else {
            wxTransfer.setHisNotificationState(2);
            wxTransfer.setHisNotificationResult(response.getMsg());
        }
        updateById(wxTransfer);
    }

    @Override
    public void notifyReceiveTransfer(WxTransfer wxTransfer) {
        // 发送订阅消息
        sendSubscribeMessage(wxTransfer);

        // 发送短信通知
        sendSmsNotification(wxTransfer);
    }

    private void sendSubscribeMessage(WxTransfer wxTransfer) {
        try {
            WeixinExt.INSTANCE.sendSubscribeMessage(
                    wxSubscribeMessageConfiguration.receiveTransferId,
                    buildMessagePageParam(wxTransfer),
                    buildMessageData(wxTransfer),
                    wxTransfer.getOpenId(),
                    wxTransfer.getOutBillNo()
            );
        } catch (Exception e) {
            log.error("发送订阅消息失败: ", e);
        }
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(WxTransfer wxTransfer) {
        if (!PhoneUtil.isMobile(wxTransfer.getMobile())) {
            log.warn("转账申请人手机号不正确，无法发送短信通知: transferId={}, mobile={}",
                    wxTransfer.getId(), wxTransfer.getMobile());
            return;
        }

        List<SmsRecord> smsRecords = smsRecordService.getByBusinessTypeAndId(SmsRecord.BusinessType.WECHAT_TRANSFER_RECEIPT, wxTransfer.getId());
        LocalDate today = LocalDate.now();
        if (smsRecords.stream().anyMatch(r -> r.getCreateTime().toLocalDate().isEqual(today))) {
            log.warn("本日已发送过短信，跳过短信通知: transferId={}", wxTransfer.getId());
            return;
        }

        try {
            // 生成微信小程序链接
            String miniProgramLink = generateMiniProgramLink(wxTransfer);
            if (miniProgramLink == null) {
                log.warn("无法生成微信小程序链接，跳过短信通知: transferId={}", wxTransfer.getId());
                return;
            }

            // 构建短信内容
            String smsContent = buildSmsContent(wxTransfer, miniProgramLink);

            // 发送短信并保存记录
            if (smsService instanceof SmsServiceImpl smsServiceImpl) {
                // 使用带有业务记录功能的方法
                smsServiceImpl.sendSmsWithRecord(
                        wxTransfer.getMobile(),
                        smsContent,
                        "", // 扩展码
                        SmsRecord.SendMethod.NORMAL,  // 普通短信
                        SmsRecord.BusinessType.WECHAT_TRANSFER_RECEIPT,
                        wxTransfer.getId()
                );
            } else {
                smsService.sendSms(wxTransfer.getMobile(), smsContent);
            }
        } catch (Exception e) {
            log.error("发送转账确认短信时发生异常: transferId={}, mobile={}, error={}",
                    wxTransfer.getId(), wxTransfer.getMobile(), e.getMessage(), e);
        }
    }

    /**
     * 生成微信小程序链接
     */
    private String generateMiniProgramLink(WxTransfer wxTransfer) {
        try {
            GenerateUrlLinkRequest request = GenerateUrlLinkRequest.builder()
                    .path(wxSubscribeMessageConfiguration.receiveTransferPage)
                    .query("id=" + wxTransfer.getApplicationId() + "&cardNo=" + wxTransfer.getCardNo() + "&selectId=" + wxTransfer.getCardRefundId())
                    .isExpire(true)
                    .expireType(EXPIRE_TYPE_DAYS)
                    .expireInterval(EXPIRE_INTERVAL_ONE_DAY)
                    .build();

            return wxMaService.getLinkService().generateUrlLink(request);
        } catch (Exception e) {
            log.error("生成微信小程序链接失败: ", e);
            // 如果生成链接失败，返回一个默认的提示
            return "获取微信小程序链接失败，请手动打开微信小程序";
        }
    }

    /**
     * 构建短信内容
     */
    private String buildSmsContent(WxTransfer wxTransfer, String miniProgramLink) {
        String createTimeStr = wxTransfer.getCreateTime().format(DatePattern.NORM_DATETIME_MINUTE_FORMATTER);

        return String.format(
                "微信小程序于%s向您发起%.2f元的转账，款项来源是门诊余额清退，请点击以下链接进入微信小程序确认收款：%s 。如逾期未确认，转账将被撤销。",
                createTimeStr,
                wxTransfer.getAmount(),
                miniProgramLink
        );
    }

    public void renewOrderNo(WxTransfer wxTransfer) {
        wxTransfer.setOutBillNo(PaymentKit.INSTANCE.newOrderId(ServiceType.TF));
        wxTransfer.setTransferBillNo("");
        wxTransfer.setTransferState("");
        wxTransfer.setTransferPackageInfo("");
        wxTransfer.setTransferFailReason("");
        updateById(wxTransfer);
    }

    private void updateTransferInfo(WxTransfer wxTransfer, TransferBillsResult transferResult) {
        wxTransfer.setTransferBillNo(transferResult.getTransferBillNo());
        wxTransfer.setTransferState(transferResult.getState());
        wxTransfer.setTransferFailReason(CharSequenceUtil.emptyIfNull(transferResult.getFailReason()).toString());
        wxTransfer.setTransferPackageInfo(transferResult.getPackageInfo());
        wxTransfer.setCreateTime(parseDateTime(transferResult.getCreateTime()));
    }

    private void updateTransferStateInfo(WxTransfer wxTransfer, TransferBillsGetResult result) {
        wxTransfer.setTransferState(result.getState());
        wxTransfer.setTransferFailReason(result.getFailReason());

        if (StringUtils.isBlank(wxTransfer.getTransferBillNo())) {
            wxTransfer.setTransferBillNo(result.getTransferBillNo());
        }

        updateTransferTimes(wxTransfer, result);
    }

    private void updateTransferTimes(WxTransfer wxTransfer, TransferBillsGetResult result) {
        if (StringUtils.isNotBlank(result.getCreateTime())) {
            LocalDateTime parsedCreateTime = parseDateTime(result.getCreateTime());
            if (parsedCreateTime != null) {
                wxTransfer.setCreateTime(parsedCreateTime);
            }
        }

        if (StringUtils.isNotBlank(result.getUpdateTime())) {
            LocalDateTime parsedUpdateTime = parseDateTime(result.getUpdateTime());
            if (parsedUpdateTime != null) {
                wxTransfer.setUpdateTime(parsedUpdateTime);
            }
        }
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            log.warn("日期时间字符串为空");
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            log.error("解析时间失败: {}, 错误: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

    private String buildMessagePageParam(WxTransfer wxTransfer) {
        return wxSubscribeMessageConfiguration.receiveTransferPage + "?id=" + wxTransfer.getApplicationId() + "&cardNo=" + wxTransfer.getCardNo() + "&selectId=" + wxTransfer.getCardRefundId();
    }

    private List<WxMaSubscribeMessage.MsgData> buildMessageData(WxTransfer wxTransfer) {
        List<WxMaSubscribeMessage.MsgData> messageDataList = new ArrayList<>();
        // 姓名
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing1",
                StringKit.INSTANCE.sanitizeThing(wxTransfer.getName())
        ));
        // 就诊卡
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "character_string2",
                StringKit.INSTANCE.sanitizeCharacterString(wxTransfer.getCardNo())
        ));
        // 金额
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "amount3",
                wxTransfer.getAmount().toString()
        ));
        // 退款进度
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing4",
                StringKit.INSTANCE.sanitizeThing("待确认收款")
        ));
        // 温馨提醒
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing5",
                StringKit.INSTANCE.sanitizeThing("请尽快确认，逾期资金将被退回")
        ));

        return messageDataList;
    }

}
