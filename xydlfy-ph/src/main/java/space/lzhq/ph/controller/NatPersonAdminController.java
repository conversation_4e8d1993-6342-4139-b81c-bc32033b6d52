package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.dromara.hutool.core.date.DateUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.NatPerson;
import space.lzhq.ph.service.INatPersonService;
import space.lzhq.ph.service.INatSampleService;

import java.util.Date;
import java.util.List;

/**
 * 人员Controller
 *
 * <AUTHOR>
 * @date 2021-12-11
 */
@Controller
@RequestMapping("/ph/natPerson")
public class NatPersonAdminController extends BaseController {

    private final INatPersonService natPersonService;
    private final INatSampleService natSampleService;

    public NatPersonAdminController(INatPersonService natPersonService, INatSampleService natSampleService) {
        this.natPersonService = natPersonService;
        this.natSampleService = natSampleService;
    }

    @RequiresPermissions("ph:natPerson:view")
    @GetMapping()
    public String natPerson() {
        return "ph/natPerson/natPerson";
    }

    @RequiresPermissions("ph:natPerson:weijian")
    @GetMapping("/weijian")
    public String weijian(@RequestParam Date date, ModelMap modelMap) {
        modelMap.put("date", DateUtil.formatDate(date));
        return "ph/natPerson/weijian";
    }

    /**
     * 查询人员列表
     */
    @RequiresPermissions("ph:natPerson:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NatPerson natPerson) {
        startPage("sort_no asc, id desc");
        List<NatPerson> list = natPersonService.selectNatPersonList(natPerson);
        return getDataTable(list);
    }

    /**
     * 查询人员列表
     */
    @RequiresPermissions("ph:natPerson:weijian")
    @PostMapping("/listWeijian")
    @ResponseBody
    public TableDataInfo listWeijian(@RequestParam Date date, @RequestParam Integer department) {
        Date minTime = DateUtil.beginOfDay(date);
        Date maxTime = natPersonService.queryMaxCollectTime(minTime, DateUtil.endOfDay(date, false));
        startPage("sort_no asc, id desc");
        List<NatPerson> list = natPersonService.selectWeijianPersons(minTime, maxTime, department);
        return getDataTable(list);
    }

    /**
     * 导出人员列表
     */
    @RequiresPermissions("ph:natPerson:export")
    @Log(title = "人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(NatPerson natPerson) {
        List<NatPerson> list = natPersonService.selectNatPersonList(natPerson);
        ExcelUtil<NatPerson> util = new ExcelUtil<>(NatPerson.class);
        return util.exportExcel(list, "核酸采集-人员");
    }

    /**
     * 导出人员列表
     */
    @RequiresPermissions("ph:natPerson:weijian")
    @Log(title = "人员", businessType = BusinessType.EXPORT)
    @PostMapping("/exportWeijian")
    @ResponseBody
    public AjaxResult exportWeijian(@RequestParam Date date, @RequestParam Integer department) {
        Date minTime = DateUtil.beginOfDay(date);
        Date maxTime = natPersonService.queryMaxCollectTime(minTime, DateUtil.endOfDay(date, false));
        List<NatPerson> list = natPersonService.selectWeijianPersons(minTime, maxTime, department);
        ExcelUtil<NatPerson> util = new ExcelUtil<>(NatPerson.class);
        return util.exportExcel(list, DateUtil.formatDate(date) + "-应检未检人员");
    }

    /**
     * 新增人员
     */
    @GetMapping("/add")
    public String add() {
        return "ph/natPerson/add";
    }

    /**
     * 新增保存人员
     */
    @RequiresPermissions("ph:natPerson:add")
    @Log(title = "人员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(NatPerson natPerson) {
        if (!natPersonService.isIdCardNoUnique(natPerson.getIdCardNo(), natPerson.getId())) {
            return error("身份证号已存在");
        }
        return toAjax(natPersonService.insertNatPerson(natPerson));
    }

    /**
     * 修改人员
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        NatPerson natPerson = natPersonService.selectNatPersonById(id);
        mmap.put("natPerson", natPerson);
        return "ph/natPerson/edit";
    }

    /**
     * 修改保存人员
     */
    @RequiresPermissions("ph:natPerson:edit")
    @Log(title = "人员", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(NatPerson natPerson) {
        if (!natPersonService.isIdCardNoUnique(natPerson.getIdCardNo(), natPerson.getId())) {
            return error("身份证号已存在");
        }
        return toAjax(natPersonService.updateNatPerson(natPerson));
    }

    /**
     * 删除人员
     */
    @RequiresPermissions("ph:natPerson:remove")
    @Log(title = "人员", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(natPersonService.deleteNatPersonByIds(ids));
    }
}
