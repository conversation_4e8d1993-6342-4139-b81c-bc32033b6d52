package space.lzhq.ph.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ActiveProfiles;
import space.lzhq.ph.BaseIntegrationTest;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;


@Slf4j
@DisplayName("微信小程序链接服务测试")
@ActiveProfiles("prod")
class WxMaLinkServiceTest extends BaseIntegrationTest {

    @Autowired
    private WxMaService wxMaService;

    @Value("${wx.subscribe-message.receive-transfer-page: pages_refund/cardBalance/cardBalance}")
    private String receiveTransferPage;

    private static final int EXPIRE_TYPE_DAYS = 1;
    private static final int EXPIRE_INTERVAL_ONE_DAY = 1;

    @Test
    @DisplayName("获取加密 URL Link 测试")
    @Disabled("手动执行")
    void testGenerateUrlLink() {
        GenerateUrlLinkRequest request = GenerateUrlLinkRequest.builder()
                .path(receiveTransferPage)
                .isExpire(true)
                .expireType(EXPIRE_TYPE_DAYS)
                .expireInterval(EXPIRE_INTERVAL_ONE_DAY)
                .build();


        String urlLink = assertDoesNotThrow(() -> wxMaService.getLinkService().generateUrlLink(request));
        log.info("urlLink: {}", urlLink);
        assertTrue(urlLink.startsWith("https://wxaurl.cn/") || urlLink.startsWith("https://wxmpurl.cn/"));
    }

}
