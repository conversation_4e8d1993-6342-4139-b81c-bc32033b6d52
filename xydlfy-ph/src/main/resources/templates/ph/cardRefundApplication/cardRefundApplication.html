<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('就诊卡余额退款申请列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                病历号：<input type="text" name="cardNo"/>
                            </li>
                            <li>
                                姓名：<input type="text" name="name"/>
                            </li>
                            <li>
                                身份证号：<input type="text" name="idCardNo"/>
                            </li>
                            <li>
                                手机号：<input type="text" name="mobile"/>
                            </li>
                            <li>
                                审核状态：<select name="auditStatus">
                                    <option value="">所有</option>
                                    <option value="0">待审核</option>
                                    <option value="1">审核通过</option>
                                    <option value="2">已清退</option>
                                    <option value="3">驳回</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>申请时间： </label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginApplyTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endApplyTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:cardRefundApplication:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var detailFlag = [[${@permission.hasPermi('ph:cardRefundApplication:detail')}]];
        var auditStatusDatas = [
            { dictValue: "0", dictLabel: "待审核" },
            { dictValue: "1", dictLabel: "审核通过" },
            { dictValue: "2", dictLabel: "已清退" },
            { dictValue: "3", dictLabel: "驳回" }
        ];
        
        var prefix = ctx + "ph/cardRefundApplication";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                detailUrl: prefix + "/detail/{id}",
                modalName: "就诊卡余额退款申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'cardNo',
                    title: '病历号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'applyTime',
                    title: '申请时间'
                },
                {
                    field: 'auditStatus',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(auditStatusDatas, value);
                    }
                },
                {
                    field: 'auditTime',
                    title: '审核时间'
                },
                {
                    field: 'auditUser',
                    title: '审核人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.id + '\')"><i class="fa fa-search"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
