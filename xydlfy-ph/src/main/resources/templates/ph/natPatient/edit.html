<!DOCTYPE html>
<!--suppress HtmlFormInputWithoutLabel, HtmlRequiredTitleElement, JSCheckFunctionSignatures -->
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('修改人员')"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
  <form class="form-horizontal m" id="form-nat_patient-edit" th:object="${natPatient}">
    <input name="id" th:field="*{id}" type="hidden">
    <input name="type" th:field="*{type}" type="hidden">
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">姓名：</label>
      <div class="col-sm-8">
        <input name="name" th:field="*{name}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">身份证号：</label>
      <div class="col-sm-8">
        <input name="idCardNo" th:field="*{idCardNo}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">手机号：</label>
      <div class="col-sm-8">
        <input name="mobile" th:field="*{mobile}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">民族：</label>
      <div class="col-sm-8">
        <select name="nation" class="form-control m-b" th:with="type=${@dict.getType('minzu')}">
          <option value="">请选择民族</option>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                  th:field="*{nation}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label">几天一检：</label>
      <div class="col-sm-8">
        <select name="days" class="form-control m-b" th:with="type=${@dict.getType('nat_frequency')}">
          <option value="">请选择几天一检</option>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                  th:field="*{days}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label">现住址：</label>
      <div class="col-sm-8">
        <input name="address" th:field="*{address}" class="form-control" type="text">
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">排序号：</label>
      <div class="col-sm-8">
        <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text" required>
      </div>
    </div>
  </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
  const prefix = ctx + "ph/natPatient";
  $("#form-nat_patient-edit").validate({
    focusCleanup: true
  });

  function submitHandler() {
    if ($.validate.form()) {
      $.operate.save(prefix + "/edit", $('#form-nat_patient-edit').serialize());
    }
  }
</script>
</body>
</html>