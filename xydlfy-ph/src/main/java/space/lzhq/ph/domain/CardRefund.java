package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.util.MaskingUtil;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ph_card_refund")
public class CardRefund implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 申请记录ID
     */
    private Long applicationId;

    /**
     * 微信用户ID
     */
    private String openId;

    /**
     * 病历号
     */
    private String cardNo;

    /**
     * 姓名
     */
    @JsonIgnore
    private String name;

    /**
     * 获取脱敏后的姓名
     */
    @JsonGetter("name")
    public String getMaskedName() {
        return MaskingUtil.maskName(name);
    }

    /**
     * 身份证号
     */
    @JsonIgnore
    private String idCardNo;

    /**
     * 获取脱敏后的身份证号
     */
    @JsonGetter("idCardNo")
    public String getMaskedIdCardNo() {
        return MaskingUtil.maskIdCard(idCardNo);
    }

    /**
     * 手机号
     */
    @JsonIgnore
    private String mobile;

    /**
     * 获取脱敏后的手机号
     */
    @JsonGetter("mobile")
    public String getMaskedMobile() {
        return MaskingUtil.maskMobile(mobile);
    }

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String hisRegisterId;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 已退金额
     */
    private BigDecimal exrefund;

    /**
     * 未退金额
     */
    private BigDecimal unrefund;

    public CardRefund(CardRefundApplication application, String cardNo, BigDecimal amount) {
        this.applicationId = application.getId();
        this.openId = application.getOpenId();
        this.cardNo = cardNo;
        this.name = application.getName();
        this.idCardNo = application.getIdCardNo();
        this.mobile = application.getMobile();
        this.amount = amount;
        this.hisRegisterId = application.getHisRegisterId();
        this.createTime = LocalDateTime.now();
        this.exrefund = BigDecimal.ZERO;
        this.unrefund = amount;
    }

}
