package space.lzhq.ph.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.NatCollection;

import java.util.Date;
import java.util.List;

@Repository
@DataSource(value = DataSourceType.LIS)
public interface NatCollectionMapper {

    List<NatCollection> selectNatCollections(
            @Param("idCardNo") String idCardNo,
            @Param("minCollectTime") Date minCollectTime
    );

}
