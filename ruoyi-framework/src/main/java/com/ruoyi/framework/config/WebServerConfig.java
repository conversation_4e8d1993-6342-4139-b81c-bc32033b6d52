package com.ruoyi.framework.config;

import org.apache.catalina.connector.Connector;
import org.dromara.hutool.log.Log;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class WebServerConfig {

    private static final Map<Integer, Connector> connectors = new HashMap<>();

    private static final Log log = Log.get();

    @Bean
    @Profile("prod")
    TomcatServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        tomcat.addAdditionalTomcatConnectors(initiateHttpConnector());
        tomcat.addConnectorCustomizers(connector -> {
            log.info("WebServerConfig: Connector found -{}:{}", connector.getScheme(), connector.getPort());
            connectors.put(connector.getPort(), connector);
        });
        return tomcat;
    }

    @Value("${server.httpPort}")
    private int httpPort;

    private Connector initiateHttpConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setScheme("http");
        connector.setPort(httpPort);
        return connector;
    }

    public static Connector getConnector(int port) {
        return connectors.get(port);
    }
}
