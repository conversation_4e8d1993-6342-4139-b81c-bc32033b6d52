<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('字典数据列表')"/>
    <th:block th:include="include :: select2-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="data-form">
                <div class="select-list">
                    <ul>
                        <li>
                            字典名称：<select id="dictType" name="dictType" class="form-control">
                            <option th:each="dict : ${dictList}" th:text="${dict['dictName']}"
                                    th:value="${dict['dictType']}" th:field="*{dict.dictType}"></option>
                        </select>
                        </li>
                        <li>
                            字典标签：<input type="text" name="dictLabel"/>
                        </li>
                        <li>
                            数据状态：<select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                            <option value="">所有</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="add()" shiro:hasPermission="system:dict:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="system:dict:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="system:dict:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:dict:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-danger" onclick="closeItem()">
                <i class="fa fa-reply-all"></i> 关闭
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: select2-js}"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('system:dict:edit')}]];
    var removeFlag = [[${@permission.hasPermi('system:dict:remove')}]];
    var datas = [[${@dict.getType('sys_normal_disable')}]];
    var prefix = ctx + "system/dict/data";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add/{id}",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            queryParams: queryParams,
            sortName: "dictSort",
            sortOrder: "asc",
            modalName: "数据",
            columns: [{
                checkbox: true
            },
                {
                    field: 'dictCode',
                    title: '字典编码'
                },
                {
                    field: 'dictLabel',
                    title: '字典标签',
                    formatter: function (value, row, index) {
                        var listClass = $.common.equals("default", row.listClass) || $.common.isEmpty(row.listClass) ? "" : "badge badge-" + row.listClass;
                        return $.common.sprintf("<span class='%s'>%s</span>", listClass, value);
                    }
                },
                {
                    field: 'dictValue',
                    title: '字典键值'
                },
                {
                    field: 'dictSort',
                    title: '字典排序'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(datas, value);
                    }
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.dictCode + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dictCode + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.dictType = $("#dictType").val();
        return search;
    }

    /*字典数据-新增字典*/
    function add() {
        var dictType = $("#dictType option:selected").val();
        $.operate.add(dictType);
    }

    function resetPre() {
        $.form.reset();
        $("#dictType").val($("#dictType").val()).trigger("change");
    }
</script>
</body>
</html>
