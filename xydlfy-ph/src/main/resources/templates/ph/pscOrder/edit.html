<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改订单')"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-pscOrder-edit" th:object="${pscOrder}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">住院号：</label>
            <div class="col-sm-8">
                <input name="admissionNumber" th:field="*{admissionNumber}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者ID：</label>
            <div class="col-sm-8">
                <input name="patientId" th:field="*{patientId}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者姓名：</label>
            <div class="col-sm-8">
                <input name="patientName" th:field="*{patientName}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者性别：</label>
            <div class="col-sm-8">
                <select name="patientSex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{patientSex}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者民族：</label>
            <div class="col-sm-8">
                <select name="patientNation" class="form-control m-b" th:with="type=${@dict.getType('minzu')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{patientNation}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者出生日期：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="patientBirthday" th:value="${#dates.format(pscOrder.patientBirthday, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者手机号：</label>
            <div class="col-sm-8">
                <input name="patientMobile" th:field="*{patientMobile}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">服务类型：</label>
            <div class="col-sm-8">
                <select name="serviceType" class="form-control m-b" th:with="type=${@dict.getType('psc_service_type')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{serviceType}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">目标科室：</label>
            <div class="col-sm-8">
                <select name="destinationDepartment" class="form-control m-b"
                        th:with="type=${@dict.getType('psc_destination_department')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{destinationDepartment}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士工号：</label>
            <div class="col-sm-8">
                <input name="nurseEmployeeNo" th:field="*{nurseEmployeeNo}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士姓名：</label>
            <div class="col-sm-8">
                <input name="nurseName" th:field="*{nurseName}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">护士手机号：</label>
            <div class="col-sm-8">
                <input name="nurseMobile" th:field="*{nurseMobile}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检工号：</label>
            <div class="col-sm-8">
                <input name="carerEmployeeNo" th:field="*{carerEmployeeNo}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检姓名：</label>
            <div class="col-sm-8">
                <input name="carerName" th:field="*{carerName}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">陪检手机号：</label>
            <div class="col-sm-8">
                <input name="carerMobile" th:field="*{carerMobile}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">状态：0=待分配陪检，1=待陪检接单，2=服务已就绪，3=正在服务，4=待护士验收，99=服务结束：</label>
            <div class="col-sm-8">
                <select name="status" class="form-control m-b" th:with="type=${@dict.getType('psc_order_status')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{status}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">派发时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="assignTime" th:value="${#dates.format(pscOrder.assignTime, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">接单时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="takeTime" th:value="${#dates.format(pscOrder.takeTime, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">开始时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="startTime" th:value="${#dates.format(pscOrder.startTime, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">结束时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="endTime" th:value="${#dates.format(pscOrder.endTime, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">验收时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="confirmTime" th:value="${#dates.format(pscOrder.confirmTime, 'yyyy-MM-dd')}"
                           class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscOrder";
    $("#form-pscOrder-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-pscOrder-edit').serialize());
        }
    }

    $("input[name='patientBirthday']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='assignTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='takeTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='startTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='endTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='confirmTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });
</script>
</body>
</html>
