<!-- Generated by SpecStory -->

# 小程序码接口文档编写 (2025-04-30 02:05Z)

_**User**_

为 `/open/wx/qrCode` 编写接口文档，该接口返回小程序码的图片文件，接口访问地址是`@http://***********:18999/ `

---

_**Assistant**_

我将帮您编写接口文档。首先让我查看相关代码以确保文档的准确性。


---

<details>
            <summary>Searched codebase "wx qrCode controller endpoint" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L278-333 |
| `ruoyi-common/src/main/java/com/ruoyi/common/config/ServerConfig.java` | L1-49 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L1-41 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L41-101 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/NatPersonController.kt` | L1-61 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/NatPersonController.kt` | L61-77 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L101-129 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/NatPatientOpenController.kt` | L55-82 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt` | L1-47 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxPaymentAdminController.kt` | L1-29 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L165-209 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxCheckAdminController.kt` | L1-50 |
| `ruoyi-admin/src/main/resources/application.yml` | L151-202 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChuFangController.kt` | L214-257 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxPaymentAdminController.kt` | L72-115 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ErhcCardApiController.kt` | L140-193 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxTransferAdminController.kt` | L1-51 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhuyuanController.kt` | L202-243 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChuFangController.kt` | L162-214 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt` | L129-165 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/NatPatientOpenController.kt` | L1-55 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/MenzhenController.kt` | L1-55 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ChuFangController.kt` | L1-41 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt` | L220-266 |
| `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxRefundAdminController.kt` | L1-28 |

</details>

---

根据代码分析，我将为您编写接口文档：

# 微信小程序码生成接口文档

## 接口基本信息

- **接口地址**: `/open/wx/qrCode`
- **请求方式**: GET
- **接口描述**: 生成微信小程序码图片文件
- **访问地址**: `http://***********:18999/open/wx/qrCode`

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| markNo | String | 是 | 实体卡号 |
| scene | String | 是 | 场景值，当前支持的场景有：<br>- QRCODE_ZJJF：诊间缴费 |
| authCode | String | 是 | 认证码，用于认证请求，当前固定为 N6Qp8hx2QACDDJd4 |

## 响应说明

- **响应类型**: image/jpeg
- **响应头**:
  - Content-Type: image/jpeg
  - Cache-Control: no-store

## 响应示例

直接返回JPEG格式的图片文件。

## 错误说明

1. 认证码不正确
   - 错误信息: "认证码不正确"
   - 原因: authCode参数值不等于"N6Qp8hx2QACDDJd4"

2. 不支持的场景
   - 错误信息: "不支持的场景: {scene}"
   - 原因: scene参数值不在支持的场景列表中

## 注意事项

1. 接口需要正确的认证码才能访问
2. 目前仅支持诊间缴费(QRCODE_ZJJF)场景
3. 返回的图片文件为JPEG格式
4. 响应设置了no-store缓存控制，确保每次获取最新的二维码

## 调用示例

```http
GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4
```

---

English Version:

# WeChat Mini Program QR Code Generation API Documentation

## Basic Information

- **Endpoint**: `/open/wx/qrCode`
- **Method**: GET
- **Description**: Generates a WeChat Mini Program QR code image file
- **Base URL**: `http://***********:18999/open/wx/qrCode`

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| markNo | String | Yes | Physical card number |
| scene | String | Yes | Scene value, currently supported scenes:<br>- QRCODE_ZJJF: Clinic Payment |
| authCode | String | Yes | Authentication code, currently fixed as N6Qp8hx2QACDDJd4 |

## Response

- **Response Type**: image/jpeg
- **Response Headers**:
  - Content-Type: image/jpeg
  - Cache-Control: no-store

## Response Example

Returns a JPEG image file directly.

## Error Handling

1. Invalid Authentication Code
   - Error Message: "认证码不正确"
   - Cause: authCode parameter value is not equal to "N6Qp8hx2QACDDJd4"

2. Unsupported Scene
   - Error Message: "不支持的场景: {scene}"
   - Cause: scene parameter value is not in the supported scenes list

## Notes

1. The API requires a valid authentication code to access
2. Currently only supports clinic payment (QRCODE_ZJJF) scene
3. The returned image file is in JPEG format
4. Response has no-store cache control to ensure getting the latest QR code each time

## Example Request

```http
GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4
```

---

_**User**_

请将上述接口文档写入到markdown文件中

---

_**Assistant**_

我将帮您创建一个markdown文件来保存接口文档。



---

```diff
- 
+ # 微信小程序码生成接口文档
+ 
+ ## 接口基本信息
+ 
+ - **接口地址**: `/open/wx/qrCode`
+ - **请求方式**: GET
+ - **接口描述**: 生成微信小程序码图片文件
+ - **访问地址**: `http://***********:18999/open/wx/qrCode`
+ 
+ ## 请求参数
+ 
+ | 参数名 | 类型 | 必填 | 说明 |
+ |--------|------|------|------|
+ | markNo | String | 是 | 实体卡号 |
+ | scene | String | 是 | 场景值，当前支持的场景有：<br>- QRCODE_ZJJF：诊间缴费 |
+ | authCode | String | 是 | 认证码，用于认证请求，当前固定为 N6Qp8hx2QACDDJd4 |
+ 
+ ## 响应说明
+ 
+ - **响应类型**: image/jpeg
+ - **响应头**:
+   - Content-Type: image/jpeg
+   - Cache-Control: no-store
+ 
+ ## 响应示例
+ 
+ 直接返回JPEG格式的图片文件。
+ 
+ ## 错误说明
+ 
+ 1. 认证码不正确
+    - 错误信息: "认证码不正确"
+    - 原因: authCode参数值不等于"N6Qp8hx2QACDDJd4"
+ 
+ 2. 不支持的场景
+    - 错误信息: "不支持的场景: {scene}"
+    - 原因: scene参数值不在支持的场景列表中
+ 
+ ## 注意事项
+ 
+ 1. 接口需要正确的认证码才能访问
+ 2. 目前仅支持诊间缴费(QRCODE_ZJJF)场景
+ 3. 返回的图片文件为JPEG格式
+ 4. 响应设置了no-store缓存控制，确保每次获取最新的二维码
+ 
+ ## 调用示例
+ 
+ ```http
+ GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4
+ ```
+ 
+ ---
+ 
+ # WeChat Mini Program QR Code Generation API Documentation
+ 
+ ## Basic Information
+ 
+ - **Endpoint**: `/open/wx/qrCode`
+ - **Method**: GET
+ - **Description**: Generates a WeChat Mini Program QR code image file
+ - **Base URL**: `http://***********:18999/open/wx/qrCode`
+ 
+ ## Request Parameters
+ 
+ | Parameter | Type | Required | Description |
+ |-----------|------|----------|-------------|
+ | markNo | String | Yes | Physical card number |
+ | scene | String | Yes | Scene value, currently supported scenes:<br>- QRCODE_ZJJF: Clinic Payment |
+ | authCode | String | Yes | Authentication code, currently fixed as N6Qp8hx2QACDDJd4 |
+ 
+ ## Response
+ 
+ - **Response Type**: image/jpeg
+ - **Response Headers**:
+   - Content-Type: image/jpeg
+   - Cache-Control: no-store
+ 
+ ## Response Example
+ 
+ Returns a JPEG image file directly.
+ 
+ ## Error Handling
+ 
+ 1. Invalid Authentication Code
+    - Error Message: "认证码不正确"
+    - Cause: authCode parameter value is not equal to "N6Qp8hx2QACDDJd4"
+ 
+ 2. Unsupported Scene
+    - Error Message: "不支持的场景: {scene}"
+    - Cause: scene parameter value is not in the supported scenes list
+ 
+ ## Notes
+ 
+ 1. The API requires a valid authentication code to access
+ 2. Currently only supports clinic payment (QRCODE_ZJJF) scene
+ 3. The returned image file is in JPEG format
+ 4. Response has no-store cache control to ensure getting the latest QR code each time
+ 
+ ## Example Request
+ 
+ ```http
+ GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4
+ ``` 
```

---

我已经创建了一个新的markdown文件 `docs/api/wx-qrcode-api.md`，其中包含了完整的接口文档，包括中文和英文两个版本。文档包含了以下主要部分：

1. 接口基本信息
2. 请求参数说明
3. 响应说明
4. 错误处理
5. 注意事项
6. 调用示例

文档采用了标准的markdown格式，包含了表格、代码块等格式化元素，使得文档易于阅读和维护。

您可以在 `docs/api/wx-qrcode-api.md` 中查看完整的文档内容。如果您需要对文档进行任何修改或补充，请随时告诉我。

---

_**User**_

把 envVersion 参数添加到接口文档中

---

_**Assistant**_

我将帮您更新接口文档，添加 envVersion 参数。


---

```diff
  | scene | String | 是 | 场景值，当前支持的场景有：<br>- QRCODE_ZJJF：诊间缴费 |
  | authCode | String | 是 | 认证码，用于认证请求，当前固定为 N6Qp8hx2QACDDJd4 |
+ | envVersion | String | 否 | 要打开的小程序版本，默认为 "release"<br>- release：正式版<br>- trial：体验版<br>- develop：开发版 |
  
  ## 响应说明
```

```diff
  3. 返回的图片文件为JPEG格式
  4. 响应设置了no-store缓存控制，确保每次获取最新的二维码
+ 5. envVersion参数用于指定打开的小程序版本，默认为正式版
  
  ## 调用示例
  
  ```http
- GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4
+ GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4&envVersion=release
  ```
  
```

---

我已经更新了接口文档，主要做了以下修改：

1. 在请求参数表格中添加了 `envVersion` 参数：
   - 类型：String
   - 必填：否
   - 说明：要打开的小程序版本，默认为 "release"
   - 可选值：release（正式版）、trial（体验版）、develop（开发版）

2. 在注意事项中添加了第5点：
   - envVersion参数用于指定打开的小程序版本，默认为正式版

3. 更新了调用示例，添加了 envVersion 参数：
   ```http
   GET http://***********:18999/open/wx/qrCode?markNo=123456&scene=QRCODE_ZJJF&authCode=N6Qp8hx2QACDDJd4&envVersion=release
   ```

文档现在完整地包含了 envVersion 参数的所有相关信息。如果您需要进一步的修改或补充，请告诉我。

---

