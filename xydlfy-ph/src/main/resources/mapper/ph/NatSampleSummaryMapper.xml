<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.NatSampleSummaryMapper">

    <resultMap type="NatSampleSummary" id="NatSampleSummaryResult">
        <result property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="yingCai" column="ying_cai"/>
        <result property="shiCai" column="shi_cai"/>
        <result property="weiCai" column="wei_cai"/>
        <result property="pushOk" column="push_ok"/>
        <result property="pushFail" column="push_fail"/>
        <result property="pushTodo" column="push_todo"/>
    </resultMap>

    <sql id="selectNatSampleSummaryVo">
        select id,
               day,
               ying_cai,
               shi_cai,
               wei_cai,
               push_ok,
               push_fail,
               push_todo
        from ph_nat_sample_summary
    </sql>

    <select id="selectNatSampleSummaryList" parameterType="NatSampleSummary" resultMap="NatSampleSummaryResult">
        <include refid="selectNatSampleSummaryVo"/>
        <where>
            <if test="params.beginDay != null and params.beginDay != '' and params.endDay != null and params.endDay != ''">
                and day between #{params.beginDay} and #{params.endDay}
            </if>
        </where>
    </select>

    <select id="selectNatSampleSummaryById" parameterType="Long" resultMap="NatSampleSummaryResult">
        <include refid="selectNatSampleSummaryVo"/>
        where id = #{id}
    </select>

    <select id="redoSummarize" resultMap="NatSampleSummaryResult">
        select count(*)                      as shi_cai,
               sum(if(push_state = 0, 1, 0)) as push_todo,
               sum(if(push_state = 1, 1, 0)) as push_ok,
               sum(if(push_state = 2, 1, 0)) as push_fail
        from ph_nat_sample
        where collect_time between #{minTime} and #{maxTime};
    </select>

    <insert id="insertNatSampleSummary" parameterType="NatSampleSummary" useGeneratedKeys="true" keyProperty="id">
        insert into ph_nat_sample_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="day != null">day,</if>
            <if test="yingCai != null">ying_cai,</if>
            <if test="shiCai != null">shi_cai,</if>
            <if test="weiCai != null">wei_cai,</if>
            <if test="pushOk != null">push_ok,</if>
            <if test="pushFail != null">push_fail,</if>
            <if test="pushTodo != null">push_todo,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="day != null">#{day},</if>
            <if test="yingCai != null">#{yingCai},</if>
            <if test="shiCai != null">#{shiCai},</if>
            <if test="weiCai != null">#{weiCai},</if>
            <if test="pushOk != null">#{pushOk},</if>
            <if test="pushFail != null">#{pushFail},</if>
            <if test="pushTodo != null">#{pushTodo},</if>
        </trim>
    </insert>

    <update id="updateNatSampleSummary" parameterType="NatSampleSummary">
        update ph_nat_sample_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="day != null">day = #{day},</if>
            <if test="yingCai != null">ying_cai = #{yingCai},</if>
            <if test="shiCai != null">shi_cai = #{shiCai},</if>
            <if test="weiCai != null">wei_cai = #{weiCai},</if>
            <if test="pushOk != null">push_ok = #{pushOk},</if>
            <if test="pushFail != null">push_fail = #{pushFail},</if>
            <if test="pushTodo != null">push_todo = #{pushTodo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNatSampleSummaryById" parameterType="Long">
        delete
        from ph_nat_sample_summary
        where id = #{id}
    </delete>

    <delete id="deleteNatSampleSummaryByIds" parameterType="String">
        delete from ph_nat_sample_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>