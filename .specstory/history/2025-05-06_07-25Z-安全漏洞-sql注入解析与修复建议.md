<!-- Generated by SpecStory -->

# 安全漏洞：SQL注入解析与修复建议 (2025-05-06 07:25Z)

_**User**_

解释截图中描述的安全漏洞

---

_**Assistant**_

截图描述的是 SQL 注入漏洞，这是一种高危安全漏洞。

根据截图内容，该漏洞发生是因为 Web 程序直接将用户提交的参数拼接到 SQL 语句中执行，没有进行过滤。攻击者可以利用 SQL 特殊字符破坏原有 SQL 语句结构，执行任意 SQL 命令，如查询数据、下载数据、写入 webshell、执行系统命令，甚至绕过登录限制。

从你的代码来看，`DrugApiController.kt` 中的 `queryByCode` 函数存在风险：

```kotlin
@GetMapping
fun queryByCode(
    @RequestParam keyword: String,
): AjaxResult {
    if (keyword.isBlank()) {
        return AjaxResult.error("请输入关键字")
    }

    val drugResponse = DongruanService.queryDrug(keyword.uppercase())
    // ...
}
```

这里直接将用户输入的 `keyword` 传递给 `DongruanService.queryDrug`，如果该服务没有正确处理输入参数，就可能导致 SQL 注入漏洞。

修复建议：
1. 对用户输入的 `keyword` 参数进行验证和过滤，移除或转义特殊字符
2. 使用参数化查询（PreparedStatement）而非直接拼接 SQL 语句
3. 使用 ORM 框架（如 MyBatis-Plus）的安全查询方法
4. 限制数据库用户权限，遵循最小权限原则

SQL 注入是最常见的 Web 应用安全漏洞之一，必须认真对待。

---

_**User**_

这个漏洞是不是应该由 DongruanService 提供方来修复？

---

_**Assistant**_

是的，这个 SQL 注入漏洞应该主要由 DongruanService 提供方来修复。

从你的代码分析：

```kotlin
val drugResponse = DongruanService.queryDrug(keyword.uppercase())
```

DrugApiController 只是将用户输入的 keyword 转为大写后传递给了 DongruanService.queryDrug 方法。由于 SQL 注入漏洞是在执行数据库查询时发生的，所以真正的问题应该出在 DongruanService 内部的实现上。

DongruanService 提供方需要做的修复包括：
1. 使用参数化查询（PreparedStatement）替代字符串拼接
2. 对传入的参数进行严格的验证和过滤
3. 采用 ORM 框架的安全特性

不过，作为 API 调用方，你也应该采取一些防护措施：
1. 在调用 DongruanService 前对 keyword 参数进行基本验证和清洗
2. 限制参数长度和格式
3. 向 DongruanService 提供方报告这个安全问题

安全责任是共同的，虽然主要修复工作应该由服务提供方完成，但调用方也应实施必要的防御措施，保障整个应用的安全性。

---

_**User**_

项目中有 alibaba druid 连接池的依赖，能否利用它对 keyword 参数进行清洗？ 

---

_**Assistant**_

是的，可以利用 Alibaba Druid 连接池的 SQL 过滤功能对 keyword 参数进行清洗，防止 SQL 注入攻击。Druid 提供了 SQL 防火墙（SQL Firewall）功能，可以帮助检测和过滤 SQL 注入。

以下是在你的 DrugApiController 中实现 Druid 过滤的方法：

```kotlin
@RestController
@RequestMapping("/open/drug")
class DrugApiController {

    @GetMapping
    fun queryByCode(
        @RequestParam keyword: String,
    ): AjaxResult {
        if (keyword.isBlank()) {
            return AjaxResult.error("请输入关键字")
        }

        // 使用 Druid 的 SQL 过滤功能清洗参数
        val cleanKeyword = com.alibaba.druid.wall.WallUtils.stripSqlInjection(keyword.uppercase())
        
        // 如果检测到 SQL 注入攻击，可以直接拒绝请求
        if (com.alibaba.druid.wall.WallUtils.isValidateEscapeSQL(keyword)) {
            val drugResponse = DongruanService.queryDrug(cleanKeyword)
            return if (drugResponse.isOk()) {
                AjaxResult.success(drugResponse.drugs)
            } else {
                AjaxResult.error(drugResponse.msg)
            }
        } else {
            return AjaxResult.error("检测到不安全的输入")
        }
    }
}
```

另外，你也可以通过配置 Druid 的全局 SQL 防火墙，在 Spring Boot 应用中添加以下配置：

```yaml
spring:
  datasource:
    druid:
      filters: wall
      wall:
        enabled: true
        config:
          delete-allow: false
          drop-table-allow: false
          # 更多安全配置...
```

虽然这些措施可以在 Controller 层面提供一定防护，但仍建议 DongruanService 内部也实施参数化查询等安全措施，实现多层防御策略。

---

_**User**_

WallUtils 中没有找到stripSqlInjection和isValidateEscapeSQL方法

---

_**Assistant**_

您说得对，我之前提供的方法名有误。查看您的代码，我注意到您已经尝试使用了 `WallUtils.sqlInject()` 方法，但似乎还没有利用它的返回结果。让我们修正这个问题。

Druid 的 `WallUtils` 类提供了 `sqlInject()` 方法，它用于检测 SQL 注入攻击，如果检测到 SQL 注入，则返回 true，否则返回 false。我们应该根据这个结果来决定是否继续处理请求。

以下是修正后的代码：

```kotlin
@GetMapping
fun queryByCode(
    @RequestParam keyword: String,
): AjaxResult {
    if (keyword.isBlank()) {
        return AjaxResult.error("请输入关键字")
    }

    // 检测是否存在 SQL 注入攻击
    if (WallUtils.sqlInject(keyword)) {
        return AjaxResult.error("检测到不安全的输入")
    }
    
    // 如果没有检测到 SQL 注入，继续处理
    val drugResponse = DongruanService.queryDrug(keyword.uppercase())
    return if (drugResponse.isOk()) {
        AjaxResult.success(drugResponse.drugs)
    } else {
        AjaxResult.error(drugResponse.msg)
    }
}
```

这样如果检测到 SQL 注入攻击，就会直接返回错误信息，不再调用 DongruanService。

如果想要更完善的防护，还可以考虑添加其他验证措施，如参数长度限制、字符类型检查等。

---

