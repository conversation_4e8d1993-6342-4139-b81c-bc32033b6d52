package space.lzhq.ph.job

import com.ruoyi.common.constant.Constants
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.mospital.alipay.HospitalOrderStatus
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.service.IReservationService

/**
 * 向支付宝推送挂号单
 * 表达式：0 0,30 6-20 * * ?
 */
@Component("pushAlipayHospitalOrderJob")
class PushAlipayHospitalOrderJob {

    @Autowired
    private lateinit var reservationService: IReservationService

    fun execute() {
        pushWaitCheckInOrders()
        pushPreorderSuccessOrders()
    }

    /**
     * 第一次次推送，就诊前3小时，预约成功
     */
    private fun pushPreorderSuccessOrders() {
        val jiuzhenTimeRange: Pair<DateTime, DateTime> =
            AlipayExt.calculatePreorderSuccessJiuzhenTimeRange()

        // 查询三小时后就诊的支付宝用户的预约记录
        val reservations: List<Reservation> = reservationService.lambdaQuery()
            .ge(Reservation::getJzTime, jiuzhenTimeRange.first.toString(DatePattern.NORM_DATETIME_FORMAT))
            .lt(Reservation::getJzTime, jiuzhenTimeRange.second.toString(DatePattern.NORM_DATETIME_FORMAT))
            .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
            .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
            .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
            .likeRight(Reservation::getOpenid, Constants.ALIPAY_USER_ID_PREFIX)
            .list()
        reservations.forEach {
            reservationService.pushAlipayHospitalOrder(
                it,
                HospitalOrderStatus.MERCHANT_PREORDER_SUCCESS,
                it.operationTime,
                it.operationTime,
                it.id
            )
        }
    }

    /**
     * 第二次推送，就诊前30分钟，待签到
     */
    private fun pushWaitCheckInOrders() {
        val jiuzhenTimeRange: Pair<DateTime, DateTime> =
            AlipayExt.calculateWaitCheckInJiuzhenTimeRange()

        val reservations: List<Reservation> = reservationService.lambdaQuery()
            .ge(Reservation::getJzTime, jiuzhenTimeRange.first.toString(DatePattern.NORM_DATETIME_FORMAT))
            .lt(Reservation::getJzTime, jiuzhenTimeRange.second.toString(DatePattern.NORM_DATETIME_FORMAT))
            .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
            .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
            .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
            .likeRight(Reservation::getOpenid, Constants.ALIPAY_USER_ID_PREFIX)
            .list()
        reservations.forEach {
            reservationService.pushAlipayHospitalOrder(
                it,
                HospitalOrderStatus.MERCHANT_WAIT_CHECK_IN,
                it.operationTime,
                DateTime.now(),
                it.id
            )
        }
    }
}
