package space.lzhq.ph.controller

import com.alipay.api.response.AlipayEcoCityserviceCityserviceEnergySendResponse
import com.github.pagehelper.PageHelper
import com.github.pagehelper.PageInfo
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import jakarta.servlet.http.HttpServletRequest
import org.mospital.alipay.AlipayService
import org.mospital.alipay.EnergyScene
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.domain.LisReport
import space.lzhq.ph.domain.LisReportItem
import space.lzhq.ph.domain.PacsReport
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.ILisReportItemService
import space.lzhq.ph.service.ILisReportService
import space.lzhq.ph.service.IPacsReportService

@RestController
@RequestMapping("/api/report")
class ReportApiController {

    @Autowired
    private lateinit var lisReportService: ILisReportService

    @Autowired
    private lateinit var lisReportItemService: ILisReportItemService

    @Autowired
    private lateinit var pacsReportService: IPacsReportService

    @GetMapping("mzlis")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mzlis(request: HttpServletRequest): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val reports: List<LisReport> = lisReportService.selectLisReports(currentPatient.jzCardNo, currentPatient.cardNo)
        return AjaxResult.success(reports.filter { it.isMenzhen })
    }

    @GetMapping("zylis")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun zylis(request: HttpServletRequest): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val reports: List<LisReport> =
            lisReportService.selectLisReports(currentPatient.zhuyuanNo, currentPatient.cardNo)
        return AjaxResult.success(reports.filter { it.isZhuyuan })
    }

    @GetMapping("lisDetail")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun lisDetail(
        @RequestParam inspectionId: String,
        @RequestParam aliAuthCode: String?,
        request: HttpServletRequest,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val reportItems: List<LisReportItem> = lisReportItemService.selectLisReportItemsByInspectionId(inspectionId)
        var alipayEnergy: Long = 0L
        if (reportItems.isNotEmpty() && currentPatient.clientTypeEnum == ClientType.ALIPAY && !aliAuthCode.isNullOrBlank()) {
            val energySendResponse: AlipayEcoCityserviceCityserviceEnergySendResponse =
                AlipayService.sendEnergy(
                    authCode = aliAuthCode,
                    scene = EnergyScene.HOINQUIRE,
                    outerNo = "LIS#" + inspectionId
                )
            if (energySendResponse.isSuccess) {
                alipayEnergy = energySendResponse.totalEnergy
            }
        }
        return AjaxResult.success().data(
            mapOf(
                "reportItems" to reportItems,
                "alipayEnergy" to alipayEnergy
            )
        )
    }

    private fun pacs(patientId: String, patientName: String, type: String, pageNo: Int, pageSize: Int): AjaxResult {
        PageHelper.startPage<PacsReport>(pageNo, pageSize, "CHECKDATE DESC")
        val reports: List<PacsReport> = pacsReportService.selectPacsReports(patientId, patientName, type)
        val data = TableDataInfo(reports, PageInfo(reports).total.toInt())
        return AjaxResult.success(data)
    }

    @GetMapping("mzpacs")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mzpacs(
        @RequestParam(required = false, defaultValue = "1") pageNo: Int,
        @RequestParam(required = false, defaultValue = "20") pageSize: Int,
        request: HttpServletRequest
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()
        return pacs(
            patientId = currentPatient.cardNo,
            patientName = currentPatient.name,
            type = "1",
            pageNo = pageNo,
            pageSize = pageSize
        )
    }

    @GetMapping("zypacs")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun zypacs(
        @RequestParam(required = false, defaultValue = "1") pageNo: Int,
        @RequestParam(required = false, defaultValue = "20") pageSize: Int,
        request: HttpServletRequest
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()
        return pacs(
            patientId = currentPatient.cardNo,
            patientName = currentPatient.name,
            type = "2",
            pageNo = pageNo,
            pageSize = pageSize
        )
    }

    @GetMapping("pacsDetail")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun pacsDetail(
        @RequestParam reportId: String,
        @RequestParam aliAuthCode: String?,
        request: HttpServletRequest
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        var alipayEnergy: Long = 0L
        if (activePatient.clientTypeEnum == ClientType.ALIPAY && !aliAuthCode.isNullOrBlank()) {
            val energySendResponse: AlipayEcoCityserviceCityserviceEnergySendResponse =
                AlipayService.sendEnergy(
                    authCode = aliAuthCode,
                    scene = EnergyScene.HOINQUIRE,
                    outerNo = "PACS#" + reportId
                )
            if (energySendResponse.isSuccess) {
                alipayEnergy = energySendResponse.totalEnergy
            }
        }
        return AjaxResult.success().data(
            mapOf(
                "alipayEnergy" to alipayEnergy
            )
        )
    }
}
