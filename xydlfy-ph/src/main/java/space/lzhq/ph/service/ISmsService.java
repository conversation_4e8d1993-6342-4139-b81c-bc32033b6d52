package space.lzhq.ph.service;

import space.lzhq.ph.domain.SmsRecord;
import space.lzhq.ph.dto.sms.SmsResponse;
import space.lzhq.ph.dto.sms.SmsStatusResponse;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 */
public interface ISmsService {

    /**
     * 发送短信
     *
     * @param mobile  手机号码
     * @param content 短信内容
     * @return 发送结果
     */
    SmsResponse sendSms(String mobile, String content);

    /**
     * 发送验证码短信
     *
     * @param mobile  手机号码
     * @param content 短信内容
     * @return 发送结果
     */
    SmsResponse sendVerificationCode(String mobile, String content);

    /**
     * 发送短信（完整参数）
     *
     * @param mobile     手机号码
     * @param content    短信内容
     * @param extcode    扩展码
     * @param sendmethod 发送方式(验证码填写9，其他一律为0)
     * @return 发送结果
     */
    SmsResponse sendSms(String mobile, String content, String extcode, SmsRecord.SendMethod sendmethod);

    /**
     * 重试发送短信（不创建新记录）
     * 此方法专门用于重试已存在的短信记录，不会创建新的短信记录
     *
     * @param sismsid    现有短信ID
     * @param mobile     手机号码
     * @param content    短信内容
     * @param extcode    扩展码
     * @param sendmethod 发送方式(验证码填写9，其他一律为0)
     * @return 发送结果
     */
    SmsResponse retrySendSms(String sismsid, String mobile, String content, String extcode, Integer sendmethod);

    /**
     * 查询短信状态
     *
     * @param sismsid 短信ID
     * @return 状态查询结果
     */
    SmsStatusResponse querySmsStatus(Long sismsid);

    /**
     * 查询短信状态
     *
     * @param sismsid 短信ID（字符串格式）
     * @return 状态查询结果
     */
    SmsStatusResponse querySmsStatus(String sismsid);

    /**
     * 生成短信ID
     *
     * @return 短信ID
     */
    Long generateSmsId();

} 