package space.lzhq.ph.service;

import space.lzhq.ph.domain.NatSample1;

import java.util.Date;
import java.util.List;

/**
 * 样品Service接口
 *
 * <AUTHOR>
 * @date 2021-12-12
 */
public interface INatSample1Service {
    /**
     * 查询样品
     *
     * @param id 样品ID
     * @return 样品
     */
    NatSample1 selectNatSampleById(Long id);

    /**
     * 查询样品列表
     *
     * @param NatSample1 样品
     * @return 样品集合
     */
    List<NatSample1> selectNatSampleList(NatSample1 NatSample1);

    /**
     * 统计试管数量
     *
     * @param NatSample1 样品
     * @return 试管数量
     */
    Long countCodes(NatSample1 NatSample1);

    /**
     * 根据混样编码查询样品信息
     *
     * @param code 混样编码
     * @return 样品集合
     */
    List<NatSample1> selectListByCode(String code);

    List<NatSample1> selectListByIdCardNo(String idCardNo);

    /**
     * 查询样品列表
     *
     * @param personId    样品人ID
     * @param collectDate 采样日期
     * @return 样品集合
     */
    List<NatSample1> selectListByPersonIdAndCollectDate(Long personId, Date collectDate);

    /**
     * 新增样品
     *
     * @param NatSample1 样品
     * @return 结果
     */
    int insertNatSample(NatSample1 NatSample1);

    /**
     * 修改样品
     *
     * @param NatSample1 样品
     * @return 结果
     */
    int updateNatSample(NatSample1 NatSample1);

    /**
     * 批量删除样品
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteNatSampleByIds(String ids);

    /**
     * 删除样品信息
     *
     * @param id 样品ID
     * @return 结果
     */
    int deleteNatSampleById(Long id);

    /**
     * 更新混样类型
     *
     * @param code 混样编码
     */
    void updateMixType(String code);
}
