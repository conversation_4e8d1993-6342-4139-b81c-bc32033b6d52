package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import org.dromara.hutool.log.Log
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/open/log")
class LogController {

    private val log: Log = Log.get()

    private val token: String = "lybeV5TjI2j6qXcwzOdZQpjR4DmkQ1Pv"

    @PostMapping
    fun log(
        @RequestParam file: String,
        @RequestParam line: Int,
        @RequestParam message: String,
        @RequestParam token: String,
    ): AjaxResult {
        if (token != this.token) {
            return AjaxResult.error("token error")
        }

        log.error("/open/log: $file#$line $message")
        return AjaxResult.success()
    }

}