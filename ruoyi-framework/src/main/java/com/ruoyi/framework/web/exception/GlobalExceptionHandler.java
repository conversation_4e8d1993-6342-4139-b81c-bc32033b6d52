package com.ruoyi.framework.web.exception;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.security.PermissionUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.shiro.authz.AuthorizationException;
import org.dromara.hutool.http.server.servlet.ServletUtil;
import org.mospital.jackson.JacksonKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private String getRequestDetails(HttpServletRequest request) {
        Map<String, Object> requestDetailsMap = new HashMap<>();
        requestDetailsMap.put("uri", request.getRequestURI());
        requestDetailsMap.put("headers", ServletUtil.getHeadersMap(request));
        requestDetailsMap.put("params", ServletUtil.getParams(request));
        return JacksonKit.INSTANCE.writeValueAsString(requestDetailsMap);
    }

    /**
     * 权限校验异常（ajax请求返回json，redirect请求跳转页面）
     */
    @ExceptionHandler(AuthorizationException.class)
    public Object handleAuthorizationException(AuthorizationException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("权限校验失败: {}, {}", getRequestDetails(request), e.getMessage(), e);
        }

        if (ServletUtils.isAjaxRequest(request)) {
            return AjaxResult.error(PermissionUtils.getMsg(e.getMessage()));
        } else {
            return new ModelAndView("error/unauth");
        }
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("不支持的请求方式: {}, 请求方法: {}", getRequestDetails(request), e.getMethod(), e);
        }
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("发生未知异常: {}", getRequestDetails(request), e);
        }
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("系统异常: {}", getRequestDetails(request), e);
        }
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public Object handleServiceException(ServiceException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("业务异常: {}, {}", getRequestDetails(request), e.getMessage(), e);
        }
        if (ServletUtils.isAjaxRequest(request)) {
            return AjaxResult.error(e.getMessage());
        } else {
            return new ModelAndView("error/service", "errorMessage", e.getMessage());
        }
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e) {
        log.error("验证异常: {}", e.getMessage(), e);
        String message = e.getAllErrors().getFirst().getDefaultMessage();
        return AjaxResult.error(message);
    }

}
