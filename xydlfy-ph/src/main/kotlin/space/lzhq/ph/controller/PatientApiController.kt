package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.DictUtils
import com.ruoyi.system.service.ISysConfigService
import jakarta.servlet.http.HttpServletRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.data.id.IdUtil
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.lang.Validator
import org.dromara.hutool.core.text.StrUtil
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.*
import org.mospital.erhc.ylz.ErhcService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.Values.MENZHEN_BALANCE_FAILED_AJAX_RESULT
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.UserInfo
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IAlipayUserInfoService
import space.lzhq.ph.service.IPatientService
import space.lzhq.ph.service.ITokenService
import java.time.LocalDate

@RestController
@RequestMapping("/api/patient")
class PatientApiController : BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var alipayUserInfoService: IAlipayUserInfoService

    @Autowired
    private lateinit var tokenService: ITokenService

    /**
     * 添加就诊卡
     * @param jzCardNo 就诊卡号
     * @param name 姓名
     * @param mobile 手机号
     */
    @PostMapping("addJzCard")
    @RequireSession
    fun addJzCard(
        @RequestParam jzCardNo: String,
        @RequestParam name: String,
        @RequestParam mobile: String,
        @RequestParam(required = false) nationCode: String?,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo = alipayUserInfoService.fillUserInfo(session, UserInfo("", name, mobile))

        if (!Validator.isMobile(userInfo.mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        synchronized(jzCardNo.intern()) {
            val patientsSameOpenid: List<Patient> = patientService.selectPatientListByOpenId(session.openId)
            if (patientsSameOpenid.any { it.jzCardNo == jzCardNo }) {
                return AjaxResult.error("您已添加过此就诊卡")
            }

            val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
            if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
                return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
            }

            val hospitalCardResponse: HospitalCardResponse =
                DongruanService.queryHospitalCard(markNo = jzCardNo, testMode = testMode)
            if (!hospitalCardResponse.isOk()) {
                return AjaxResult.error(hospitalCardResponse.msg)
            }

            if (!HisExt.matchesChinese(hospitalCardResponse.name, userInfo.name)) {
                return AjaxResult.error("姓名不正确")
            }
            if (userInfo.mobile != hospitalCardResponse.mobile) {
                return AjaxResult.error("手机号不正确")
            }

            if (hospitalCardResponse.idCardNo.isBlank()) {
                return AjaxResult.error("证件号为空，请到窗口更新您的证件信息")
            }

            if (!IdcardUtil.isValidCard(hospitalCardResponse.idCardNo)) {
                return AjaxResult.error("【${hospitalCardResponse.idCardNo}】不是有效的身份证号，请到窗口更新您的身份证信息")
            }

            val patient = Patient(session, hospitalCardResponse, userInfo.mobile)
            // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
            patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
            patientService.insertPatient(patient)

            if (!nationCode.isNullOrBlank()) {
                CoroutineScope(Dispatchers.IO).launch {
                    val fixedNationCode = StrUtil.padPre(nationCode, 2, "0")
                    ErhcService.getOrCreateHealthCard(
                        idCardNo = patient.idCardNo,
                        name = patient.name,
                        mobile = patient.mobile,
                        nationCode = fixedNationCode,
                    )
                    logger.debug("Create health card after add patient: ${patient.idCardNo}, ${patient.name}")
                }
            }

            return AjaxResult.success(patient)
        }
    }

    /**
     * 添加婴儿就诊卡
     * @param name 姓名
     * @param gender 性别：1=男,2=女,9=未知
     * @param birthday 出生日期
     * @param nationCode 民族代码，如 01 表示汉族，参考 /open/dict/queryByType?type=minzu
     * @param jhrName 监护人姓名
     * @param jhrIdCardNo 监护人身份证号
     * @param mobile 监护人手机号
     * @param address 地址
     */
    @PostMapping("addBabyJzCard")
    @RequireSession
    fun addBabyJzCard(
        @RequestParam name: String,
        @RequestParam gender: Int,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) birthday: LocalDate,
        @RequestParam(required = false) nationCode: String?,
        @RequestParam jhrName: String,
        @RequestParam jhrIdCardNo: String,
        @RequestParam mobile: String,
        @RequestParam address: String,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!

        if (gender !in arrayOf(1, 2, 9)) {
            return AjaxResult.error("性别不正确")
        }

        if (birthday.isAfter(LocalDate.now())) {
            return AjaxResult.error("出生日期不正确")
        }

        if (!IdcardUtil.isValidCard(jhrIdCardNo)) {
            return AjaxResult.error("监护人身份证号不正确")
        }

        if (!Validator.isMobile(mobile)) {
            return AjaxResult.error("监护人手机号不正确")
        }

        val fixedNationCode = StrUtil.padPre(nationCode, 2, "0")
        val nationDict = DictUtils.getDictCache("minzu").firstOrNull {
            StrUtil.padPre(it.dictValue, 2, "0") == fixedNationCode
        }
        if (nationDict == null) {
            return AjaxResult.error("民族不正确")
        }

        synchronized(jhrIdCardNo.intern()) {
            val babyJzCardRegisterResponse: HealthCardRegisterResponse = DongruanService.registerHealthCardV2(
                HealthCardRegisterRequestV2().apply {
                    this.name = name
                    this.phone = mobile
                    this.idCardNo = ""
                    this.nation = fixedNationCode
                    this.sex = gender.toString()
                    this.address = address
                    this.markNo = ""
                    this.birthday = birthday.format(DatePattern.NORM_DATE_FORMATTER)
                    this.babyFlag = 1
                    this.jhrName = jhrName
                    this.jhrIdNo = jhrIdCardNo
                    this.transModel =
                        HealthCardRegisterRequestV2.TransModel(bankTransNo = IdUtil.fastSimpleUUID().take(30))
                }
            )
            if (!babyJzCardRegisterResponse.isOk()) {
                return AjaxResult.error(babyJzCardRegisterResponse.msg)
            }

            val markNo = babyJzCardRegisterResponse.markNo

            val patientsSameOpenid: List<Patient> = patientService.selectPatientListByOpenId(session.openId)
            if (patientsSameOpenid.any { it.jzCardNo == markNo }) {
                return AjaxResult.error("您已添加过此就诊卡")
            }

            val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
            if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
                return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
            }

            val hospitalCardResponse: HospitalCardResponse = DongruanService.queryHospitalCard(markNo = markNo)
            if (!hospitalCardResponse.isOk()) {
                return AjaxResult.error(hospitalCardResponse.msg)
            }

            if (!HisExt.matchesChinese(hospitalCardResponse.name, name)) {
                return AjaxResult.error("姓名不正确")
            }
            if (mobile != hospitalCardResponse.mobile) {
                return AjaxResult.error("手机号不正确")
            }

            val patient = Patient(session, hospitalCardResponse, mobile)
            // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
            patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
            patientService.insertPatient(patient)

            return AjaxResult.success(patient)
        }
    }

    /**
     * 获取就诊人列表
     */
    @GetMapping("list")
    @RequireSession
    fun list(): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
        return AjaxResult.success(patientsSameOpenid)
    }

    /**
     * 获取默认就诊人
     */
    @GetMapping("active")
    @RequireActivePatient
    fun active(): AjaxResult {
        return AjaxResult.success(request.getCurrentPatient())
    }

    /**
     * 根据就诊卡号查询患者信息
     * @param jzCardNo 就诊卡号
     */
    @GetMapping("jzCard")
    @RequireSession
    fun jzCard(@RequestParam jzCardNo: String): AjaxResult {
        val session: Session = request.getClientSession()!!
        val patient: Patient = patientService.getOneByJzCardNoAndOpenId(jzCardNo, session.openId)
            ?: return AjaxResult.error("查不到指定的就诊人信息")
        return AjaxResult.success(patient)
    }

    /**
     * 获取门诊余额
     */
    @GetMapping("menzhenBalance/v1")
    @RequireSession
    fun getMenzhenBalanceV1(
        @RequestParam cardNo: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
    ): AjaxResult {
        val menzhenBalance = HisExt.getMenzhenBalance(
            cardNo = cardNo,
            testMode = testMode
        )
        return if (menzhenBalance != null) {
            AjaxResult.success("ok", menzhenBalance)
        } else {
            MENZHEN_BALANCE_FAILED_AJAX_RESULT
        }
    }

    /**
     * 切换到指定就诊卡
     * @param jzCardNo 就诊卡号
     */
    @PostMapping("switchToJzCard")
    @RequireSession
    fun switchToJzCard(@RequestParam jzCardNo: String): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid: List<Patient> = patientService.selectPatientListByOpenId(session.openId)

        var oldActive: Patient? = null
        var newActive: Patient? = null
        patientsSameOpenid.forEach {
            if (it.jzCardNo == jzCardNo) {
                it.active = 1
                newActive = it
            } else {
                if (it.active == 1) {
                    it.active = 0
                    oldActive = it
                }
            }
        }

        if (newActive == null) {
            return AjaxResult.error("查不到指定的就诊人信息")
        }

        if (oldActive != null) {
            patientService.updatePatient(oldActive)
        }

        patientService.updatePatient(newActive)
        return AjaxResult.success(newActive)
    }

    /**
     * 根据就诊卡号移除就诊人
     * @param jzCardNo 就诊卡号
     */
    @PostMapping("removeJzCard")
    @RequireSession
    fun removeJzCard(@RequestParam jzCardNo: String): AjaxResult {
        if (jzCardNo.isBlank()) {
            return AjaxResult.error("就诊卡号不能为空")
        }

        val session = request.getClientSession()!!
        val patientsSameOpenid: List<Patient> = patientService.selectPatientListByOpenId(session.openId)
        val target = patientsSameOpenid.firstOrNull { jzCardNo == it.jzCardNo }
            ?: return AjaxResult.error("查不到指定的就诊人信息")
        if (target.active == 1 && patientsSameOpenid.size > 1) {
            return AjaxResult.error("为避免影响使用，请先切换到其它就诊人，再来移除此就诊人")
        }
        patientService.deletePatientById(target.id)
        return AjaxResult.success()
    }

    /**
     * 查询住院历史
     * @param zhuyuanNo 住院号，可选，为空时返回指定患者所有住院记录，不为空时返回指定患者特定住院号的住院记录
     */
    @GetMapping("zhuyuanHistory")
    @RequireActivePatient
    fun zhuyuanHistory(
        @RequestParam(required = false) zhuyuanNo: String?,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        if (zhuyuanNo.isNullOrBlank() && currentPatient.idCardNo.isNullOrBlank()) {
            return AjaxResult.error("当前就诊人未提供身份证号，无法查询住院记录，建议重新绑定此就诊人")
        }

        val zhuyuanPatientsResponse: ZhuyuanPatientsResponse = DongruanService.queryZhuyuanPatients(
            ZhuyuanPatientsRequest(
                idNo = if (zhuyuanNo.isNullOrBlank()) currentPatient.idCardNo else zhuyuanNo,
                mode = if (zhuyuanNo.isNullOrBlank()) 1 else 2
            ),
            testMode = testMode
        )

        if (!zhuyuanPatientsResponse.isOk()) {
            return AjaxResult.error("查询住院信息失败")
        }

        return AjaxResult.success(zhuyuanPatientsResponse.patients)
    }

    @PostMapping("switchZhuyuanNo")
    @RequireActivePatient
    fun switchZhuyuanNo(
        @RequestParam zhuyuanNo: String,
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean,
        request: HttpServletRequest,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val zhuyuanPatientsResponse: ZhuyuanPatientsResponse = DongruanService.queryZhuyuanPatients(
            ZhuyuanPatientsRequest(
                idNo = zhuyuanNo,
                mode = 2
            ),
            testMode = testMode
        )

        val zhuyuanPatient = zhuyuanPatientsResponse.patients.firstOrNull { it.zhuyuanNo == zhuyuanNo }
            ?: return AjaxResult.error("查不到指定的住院信息")

        currentPatient.zhuyuanNo = zhuyuanPatient.zhuyuanNo
        currentPatient.ruyuanTime = try {
            DateUtil.parse(zhuyuanPatient.ruyuanTime as CharSequence)
        } catch (e: Exception) {
            null
        }
        patientService.updatePatient(currentPatient)
        return AjaxResult.success(currentPatient)
    }

    @PostMapping("syncZhuyuanNo")
    @RequireActivePatient
    fun syncZhuyuanNo(
        @RequestParam(required = false, defaultValue = "false") testMode: Boolean
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        if (currentPatient.idCardNo.isNullOrBlank()) {
            return AjaxResult.error("当前就诊人未提供身份证号，无法查询住院记录，建议重新绑定此就诊人")
        }

        val zhuyuanPatientsResponse: ZhuyuanPatientsResponse = DongruanService.queryZhuyuanPatients(
            ZhuyuanPatientsRequest(
                idNo = currentPatient.idCardNo,
                mode = 1
            ),
            testMode = testMode
        )
        if (zhuyuanPatientsResponse.isOk()) {
            val zhuyuanPatient = zhuyuanPatientsResponse.patients.maxByOrNull { it.ruyuanTime }
                ?: return AjaxResult.success()
            currentPatient.zhuyuanNo = zhuyuanPatient.zhuyuanNo
            currentPatient.ruyuanTime = try {
                DateUtil.parse(zhuyuanPatient.ruyuanTime as CharSequence)
            } catch (e: Exception) {
                null
            }
            patientService.updatePatient(currentPatient)
        }
        return AjaxResult.success(currentPatient)
    }

    /**
     * 处理就诊卡二维码并切换当前活跃就诊人。
     *
     * 首先检查二维码中包含的就诊卡号是否已存在于当前用户的就诊人列表中：
     * - 如果存在，则直接将该就诊人设置为活跃状态
     * - 如果不存在，则检查二维码是否过期：
     *   - 若已过期，返回错误
     *   - 若未过期，则通过外部服务获取患者信息，创建新的就诊人记录并设置为活跃状态
     *
     * @param token 包含就诊卡信息的二维码字符串
     * @return 操作结果，成功则返回就诊人信息，失败则返回错误信息
     */
    @PostMapping("checkAndSwitchJzCard")
    @RequireSession
    fun checkAndSwitchJzCard(@RequestParam("token") tokenId: String): AjaxResult {
        if (tokenId.isBlank()) {
            return AjaxResult.error("二维码无效")
        }

        val token = tokenService.getById(tokenId) ?: return AjaxResult.error("二维码无效")
        if (token.isExpired()) {
            return AjaxResult.error("二维码已过期")
        }

        val jzCardNo = token.content
        val session = request.getClientSession()!!
        val openId = session.openId

        synchronized("${jzCardNo}_${openId}".intern()) {
            val patients: List<Patient> = patientService.selectPatientListByOpenId(openId)
            val targetPatient = patients.firstOrNull { jzCardNo == it.jzCardNo }

            // 如果找到目标患者，直接执行切换逻辑
            if (targetPatient != null) {
                if (targetPatient.active == 0) {
                    targetPatient.active = 1
                    patientService.switchActivePatient(jzCardNo, openId)
                }
                return AjaxResult.success(targetPatient)
            }

            val hospitalCardResponse: HospitalCardResponse =
                DongruanService.queryHospitalCard(markNo = jzCardNo, testMode = false)
            if (!hospitalCardResponse.isOk()) {
                return AjaxResult.error(hospitalCardResponse.msg)
            }

            val patient = Patient(session, hospitalCardResponse, hospitalCardResponse.mobile)
            patient.active = 1
            patient.jzCardNo = jzCardNo
            patientService.insertPatient(patient)
            patientService.switchActivePatient(jzCardNo, openId)

            return AjaxResult.success(patient)
        }
    }

}
