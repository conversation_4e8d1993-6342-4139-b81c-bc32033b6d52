package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import space.lzhq.ph.domain.NurseCard;
import space.lzhq.ph.domain.NurseCardStatus;
import space.lzhq.ph.service.INurseCardService;

import java.util.Date;
import java.util.List;

/**
 * 电子陪护证Controller
 *
 * <AUTHOR>
 * @date 2022-05-11
 */
@Controller
@RequestMapping("/ph/nurseCard")
public class NurseCardAdminController extends BaseController {
    private String prefix = "ph/nurseCard";

    @Autowired
    private INurseCardService nurseCardService;

    @RequiresPermissions("ph:nurseCard:view")
    @GetMapping()
    public String nurseCard() {
        return prefix + "/nurseCard";
    }

    /**
     * 查询电子陪护证列表
     */
    @RequiresPermissions("ph:nurseCard:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NurseCard nurseCard) {
        startPage();
        List<NurseCard> list = nurseCardService.selectNurseCardList(nurseCard);
        return getDataTable(list);
    }

    /**
     * 导出电子陪护证列表
     */
    @RequiresPermissions("ph:nurseCard:export")
    @Log(title = "电子陪护证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(NurseCard nurseCard) {
        List<NurseCard> list = nurseCardService.selectNurseCardList(nurseCard);
        ExcelUtil<NurseCard> util = new ExcelUtil<NurseCard>(NurseCard.class);
        return util.exportExcel(list, "nurseCard");
    }

    /**
     * 删除电子陪护证
     */
    @RequiresPermissions("ph:nurseCard:remove")
    @Log(title = "电子陪护证", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(nurseCardService.deleteNurseCardByIds(ids));
    }

    /**
     * 审核不通过
     */
    @RequiresPermissions("ph:nurseCard:reject")
    @PostMapping("/reject")
    @ResponseBody
    public AjaxResult reject(Long id, String auditRemark) {
        NurseCard nurseCard = nurseCardService.selectNurseCardById(id);
        nurseCard.setAuditRemark(auditRemark);
        nurseCard.setStatus(NurseCardStatus.REJECTED);
        nurseCard.setAuditTime(new Date());
        return toAjax(nurseCardService.updateNurseCard(nurseCard));
    }

    /**
     * 审核通过
     */
    @RequiresPermissions("ph:nurseCard:accept")
    @PostMapping("/accept")
    @ResponseBody
    public AjaxResult accept(Long id) {
        NurseCard nurseCard = nurseCardService.selectNurseCardById(id);
        nurseCard.setAuditRemark("");
        nurseCard.setStatus(NurseCardStatus.NORMAL);
        nurseCard.setAuditTime(new Date());
        nurseCard.setEffectiveTime(new Date());
        return toAjax(nurseCardService.updateNurseCard(nurseCard));
    }
}
