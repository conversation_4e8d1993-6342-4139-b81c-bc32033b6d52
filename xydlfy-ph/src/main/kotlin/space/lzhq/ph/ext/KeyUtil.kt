package space.lzhq.ph.ext

import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo
import org.bouncycastle.asn1.pkcs.RSAPrivateKey
import org.bouncycastle.asn1.x509.AlgorithmIdentifier
import org.bouncycastle.jce.provider.BouncyCastleProvider
import java.io.ByteArrayInputStream
import java.io.File
import java.io.IOException
import java.security.KeyFactory
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Security
import java.security.cert.Certificate
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.security.spec.PKCS8EncodedKeySpec
import java.util.*

object KeyUtil {
    private const val PKCS8_HEADER = "-----BEGIN PRIVATE KEY-----"
    private const val PKCS8_FOOTER = "-----END PRIVATE KEY-----"
    private const val PKCS1_HEADER = "-----BEGIN RSA PRIVATE KEY-----"
    private const val PKCS1_FOOTER = "-----END RSA PRIVATE KEY-----"
    private const val CERT_HEADER = "-----BEGIN CERTIFICATE-----"
    private const val CERT_FOOTER = "-----END CERTIFICATE-----"

    // Let's Encrypt的根证书列表
    private val letsEncryptRootCerts = mapOf(
        "ISRG Root X1" to """
            -----BEGIN CERTIFICATE-----
            MIIFazCCA1OgAwIBAgIRAIIQz7DSQONZRGPgu2OCiwAwDQYJKoZIhvcNAQELBQAw
            TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
            cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMTUwNjA0MTEwNDM4
            WhcNMzUwNjA0MTEwNDM4WjBPMQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJu
            ZXQgU2VjdXJpdHkgUmVzZWFyY2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBY
            MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAK3oJHP0FDfzm54rVygc
            h77ct984kIxuPOZXoHj3dcKi/vVqbvYATyjb3miGbESTtrFj/RQSa78f0uoxmyF+
            0TM8ukj13Xnfs7j/EvEhmkvBioZxaUpmZmyPfjxwv60pIgbz5MDmgK7iS4+3mX6U
            A5/TR5d8mUgjU+g4rk8Kb4Mu0UlXjIB0ttov0DiNewNwIRt18jA8+o+u3dpjq+sW
            T8KOEUt+zwvo/7V3LvSye0rgTBIlDHCNAymg4VMk7BPZ7hm/ELNKjD+Jo2FR3qyH
            B5T0Y3HsLuJvW5iB4YlcNHlsdu87kGJ55tukmi8mxdAQ4Q7e2RCOFvu396j3x+UC
            B5iPNgiV5+I3lg02dZ77DnKxHZu8A/lJBdiB3QW0KtZB6awBdpUKD9jf1b0SHzUv
            KBds0pjBqAlkd25HN7rOrFleaJ1/ctaJxQZBKT5ZPt0m9STJEadao0xAH0ahmbWn
            OlFuhjuefXKnEgV4We0+UXgVCwOPjdAvBbI+e0ocS3MFEvzG6uBQE3xDk3SzynTn
            jh8BCNAw1FtxNrQHusEwMFxIt4I7mKZ9YIqioymCzLq9gwQbooMDQaHWBfEbwrbw
            qHyGO0aoSCqI3Haadr8faqU9GY/rOPNk3sgrDQoo//fb4hVC1CLQJ13hef4Y53CI
            rU7m2Ys6xt0nUW7/vGT1M0NPAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV
            HRMBAf8EBTADAQH/MB0GA1UdDgQWBBR5tFnme7bl5AFzgAiIyBpY9umbbjANBgkq
            hkiG9w0BAQsFAAOCAgEAVR9YqbyyqFDQDLHYGmkgJykIrGF1XIpu+ILlaS/V9lZL
            ubhzEFnTIZd+50xx+7LSYK05qAvqFyFWhfFQDlnrzuBZ6brJFe+GnY+EgPbk6ZGQ
            3BebYhtF8GaV0nxvwuo77x/Py9auJ/GpsMiu/X1+mvoiBOv/2X/qkSsisRcOj/KK
            NFtY2PwByVS5uCbMiogziUwthDyC3+6WVwW6LLv3xLfHTjuCvjHIInNzktHCgKQ5
            ORAzI4JMPJ+GslWYHb4phowim57iaztXOoJwTdwJx4nLCgdNbOhdjsnvzqvHu7Ur
            TkXWStAmzOVyyghqpZXjFaH3pO3JLF+l+/+sKAIuvtd7u+Nxe5AW0wdeRlN8NwdC
            jNPElpzVmbUq4JUagEiuTDkHzsxHpFKVK7q4+63SM1N95R1NbdWhscdCb+ZAJzVc
            oyi3B43njTOQ5yOf+1CceWxG1bQVs5ZufpsMljq4Ui0/1lvh+wjChP4kqKOJ2qxq
            4RgqsahDYVvTH9w7jXbyLeiNdd8XM2w9U/t7y0Ff/9yi0GE44Za4rF2LN9d11TPA
            mRGunUHBcnWEvgJBQl9nJEiU0Zsnvgc/ubhPgXRR4Xq37Z0j4r7g1SgEEzwxA57d
            emyPxgcYxn/eR44/KJ4EBs+lVDR3veyJm+kXQ99b21/+jh5Xos1AnX5iItreGCc=
            -----END CERTIFICATE-----
        """.trimIndent(),
        "ISRG Root X2" to """
            -----BEGIN CERTIFICATE-----
            MIICGzCCAaGgAwIBAgIQQdKd0XLq7qeAwSxs6S+HUjAKBggqhkjOPQQDAzBPMQsw
            CQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJuZXQgU2VjdXJpdHkgUmVzZWFyY2gg
            R3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBYMjAeFw0yMDA5MDQwMDAwMDBaFw00
            MDA5MTcxNjAwMDBaME8xCzAJBgNVBAYTAlVTMSkwJwYDVQQKEyBJbnRlcm5ldCBT
            ZWN1cml0eSBSZXNlYXJjaCBHcm91cDEVMBMGA1UEAxMMSVNSRyBSb290IFgyMHYw
            EAYHKoZIzj0CAQYFK4EEACIDYgAEzZvVn4CDCuwJSvMWSj5cz3es3mcFDR0HttwW
            +1qLFNvicWDEukWVEYmO6gbf9yoWHKS5xcUy4APgHoIYOIvXRdgKam7mAHf7AlF9
            ItgKbppbd9/w+kHsOdx1ymgHDB/qo0IwQDAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0T
            AQH/BAUwAwEB/zAdBgNVHQ4EFgQUfEKWrt5LSDv6kviejM9ti6lyN5UwCgYIKoZI
            zj0EAwMDaAAwZQIwe3lORlCEwkSHRhtFcP9Ymd70/aTSVaYgLXTWNLxBo1BfASdW
            tL4ndQavEi51mI38AjEAi/V3bNTIZargCyzuFJ0nN6T5U6VR5CmD1/iQMVtCnwr1
            /q4AaOeMSQ+2b1tbFfLn
            -----END CERTIFICATE-----
        """.trimIndent()
    )

    /**
     * 合成PKCS12文件
     * @param certificateString 证书内容（可能包含多个证书）
     * @param privateKeyString 私钥内容
     * @param keystorePassword keystore密码
     * @param keyAlias 私钥别名
     * @param keystoreFile 输出的keystore文件
     * @throws IllegalArgumentException 参数错误
     * @throws IOException 文件写入失败
     */
    @Throws(IllegalArgumentException::class, IOException::class)
    fun convertToPKCS12(
        certificateString: String,
        privateKeyString: String,
        keystorePassword: String,
        keyAlias: String = "privateKey",
        keystoreFile: File
    ) {
        require(certificateString.isNotBlank()) { "证书内容不能为空" }
        require(privateKeyString.isNotBlank()) { "私钥内容不能为空" }
        require(keystorePassword.isNotBlank()) { "keystore密码不能为空" }

        // 注册BouncyCastle Provider（幂等）
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }

        // 解析并补全证书链
        val certChain = ensureCompleteChain(certificateString)
        require(certChain.isNotEmpty()) { "无法解析证书内容" }

        // 解析私钥
        val privateKey = parsePrivateKey(privateKeyString)

        // 创建PKCS12 keystore
        val keyStore = KeyStore.getInstance("PKCS12")
        keyStore.load(null, null)

        // 设置证书链和私钥
        keyStore.setKeyEntry(keyAlias, privateKey, keystorePassword.toCharArray(), certChain.toTypedArray())

        // 自动关闭流
        keystoreFile.outputStream().use { os ->
            keyStore.store(os, keystorePassword.toCharArray())
        }
    }

    /**
     * 解析证书链并确保包含根证书
     */
    private fun ensureCompleteChain(certificateString: String): List<Certificate> {
        val certs = parseCertificateChain(certificateString)
        if (certs.isEmpty()) return emptyList()

        // 如果已经包含根证书，直接返回排序后的证书链
        val x509Certs = certs.filterIsInstance<X509Certificate>()
        val lastCert = x509Certs.last()
        if (lastCert.subjectX500Principal.name.contains("ISRG Root")) {
            return sortCertificateChain(certs)
        }

        // 根据中间证书选择合适的根证书
        val rootCert = when {
            lastCert.issuerX500Principal.name.contains("ISRG Root X1") -> letsEncryptRootCerts["ISRG Root X1"]
            lastCert.issuerX500Principal.name.contains("ISRG Root X2") -> letsEncryptRootCerts["ISRG Root X2"]
            else -> letsEncryptRootCerts["ISRG Root X1"] // 默认使用X1
        }?.let { certStr ->
            CertificateFactory.getInstance("X.509")
                .generateCertificate(ByteArrayInputStream(certStr.toByteArray()))
        }

        // 添加根证书并返回排序后的证书链
        return sortCertificateChain(certs + listOfNotNull(rootCert))
    }

    /**
     * 解析证书链
     */
    private fun parseCertificateChain(certificateString: String): List<Certificate> {
        val certFactory = CertificateFactory.getInstance("X.509")
        val certificates = mutableListOf<Certificate>()

        // 分割多个证书
        val certStrings = certificateString.split(CERT_FOOTER)
            .filter { it.contains(CERT_HEADER) }
            .map { it.trim() + CERT_FOOTER }

        for (certStr in certStrings) {
            certFactory.generateCertificate(certStr.byteInputStream(Charsets.UTF_8))?.let {
                certificates.add(it)
            }
        }

        return certificates
    }

    /**
     * 对证书链进行排序
     * 确保服务器证书在前，根证书在后
     */
    private fun sortCertificateChain(certificates: List<Certificate>): List<Certificate> {
        if (certificates.size <= 1) return certificates

        val x509Certs = certificates.filterIsInstance<X509Certificate>()
        // 构建subject->证书映射
        val subjectMap = x509Certs.associateBy { it.subjectX500Principal }
        // 构建issuer集合
        val issuerSet = x509Certs.map { it.issuerX500Principal }.toSet()
        // 找到服务器证书（其subject不是任何issuer）
        val serverCert = x509Certs.find { it.subjectX500Principal !in issuerSet }
            ?: x509Certs.first() // 兜底

        val sortedChain = mutableListOf<X509Certificate>()
        var current = serverCert
        while (true) {
            sortedChain.add(current)
            // 如果是根证书（自签名），结束
            if (current.subjectX500Principal == current.issuerX500Principal) break
            // 查找上一级issuer
            val issuerCert = subjectMap[current.issuerX500Principal] ?: break
            if (issuerCert == current) break
            current = issuerCert
        }
        return sortedChain
    }

    /**
     * 支持PKCS#1和PKCS#8格式私钥
     */
    private fun parsePrivateKey(privateKeyString: String): PrivateKey {
        val keyPem = privateKeyString.trim()
        val keyFactory = KeyFactory.getInstance("RSA")
        val keyBytes: ByteArray = when {
            keyPem.contains(PKCS8_HEADER) -> {
                extractPemContent(keyPem, PKCS8_HEADER, PKCS8_FOOTER)
            }

            keyPem.contains(PKCS1_HEADER) -> {
                val pkcs1Bytes = extractPemContent(keyPem, PKCS1_HEADER, PKCS1_FOOTER)
                // PKCS#1转PKCS#8
                val rsaPrivateKey = RSAPrivateKey.getInstance(pkcs1Bytes)
                val privateKeyInfo = PrivateKeyInfo(
                    AlgorithmIdentifier(PKCSObjectIdentifiers.rsaEncryption),
                    rsaPrivateKey
                )
                privateKeyInfo.encoded
            }

            else -> throw kotlin.IllegalArgumentException("不支持的私钥格式")
        }
        return keyFactory.generatePrivate(PKCS8EncodedKeySpec(keyBytes))
    }

    /**
     * 提取PEM内容并Base64解码
     */
    private fun extractPemContent(pem: String, header: String, footer: String): ByteArray {
        val base64 = pem
            .replace(header, "")
            .replace(footer, "")
            .replace("\\s".toRegex(), "")
        return Base64.getDecoder().decode(base64)
    }
}