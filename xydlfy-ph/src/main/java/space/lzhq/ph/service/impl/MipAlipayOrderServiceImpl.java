package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.MipAlipayOrder;
import space.lzhq.ph.mapper.MipAlipayOrderMapper;
import space.lzhq.ph.service.IMipAlipayOrderService;

@Service
public class MipAlipayOrderServiceImpl extends ServiceImpl<MipAlipayOrderMapper, MipAlipayOrder> implements IMipAlipayOrderService {
    @Override
    public MipAlipayOrder getOneByAuthCode(String authCode) {
        return new LambdaQueryChainWrapper<MipAlipayOrder>(getBaseMapper())
                .eq(MipAlipayOrder::getAuthCode, authCode)
                .one();
    }

    @Override
    public MipAlipayOrder getOneByPayOrderId(String payOrderId) {
        return new LambdaQueryChainWrapper<MipAlipayOrder>(getBaseMapper())
                .eq(MipAlipayOrder::getPayOrderId, payOrderId)
                .one();
    }

    @Override
    public MipAlipayOrder getOneByOutTradeNo(String outTradeNo) {
        return new LambdaQueryChainWrapper<MipAlipayOrder>(getBaseMapper())
                .eq(MipAlipayOrder::getOutTradeNo, outTradeNo)
                .one();
    }

}
