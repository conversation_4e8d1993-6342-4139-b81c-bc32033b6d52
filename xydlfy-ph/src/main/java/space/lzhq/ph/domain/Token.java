package space.lzhq.ph.domain;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.dromara.hutool.core.util.RandomUtil;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ph_token")
public class Token {

    @TableField(exist = false)
    public static final String PREFIX_ZJJF = "ZJJF";
    
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    private String content;

    private Long expireTime;

    private static String generateId(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String random = RandomUtil.randomString(11);
        return prefix + timestamp + random;
    }

    public static String generateZJJFId() {
        return generateId(PREFIX_ZJJF);
    }

    @TableField(exist = false)
    private static final long EXPIRE_TIME_MS = 1000 * 60 * 60 * 12; // 12小时
    public static Token generateZJJFToken(String markNo) {
        return Token.builder()
            .id(generateZJJFId())
            .content(markNo)
            .expireTime(System.currentTimeMillis() + EXPIRE_TIME_MS)
            .build();
    }

    public boolean isExpired() {
        return System.currentTimeMillis() > expireTime;
    }
    
}
