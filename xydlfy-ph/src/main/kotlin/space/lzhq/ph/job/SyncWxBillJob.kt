package space.lzhq.ph.job

import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxCheckService
import java.time.LocalDate

@Component("syncWxBillJob")
class SyncWxBillJob {

    private val logger: Logger = LoggerFactory.getLogger(SyncWxBillJob::class.java)

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    fun sync() {
        val billDate = LocalDate.now().minusDays(1)

        try {
            val wxPayBillResult: WxPayBillResult? = WeixinExt.downloadAndSaveWxBill(billDate)
            wxCheckService.syncWxBill(billDate, wxPayBillResult)
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try {
            val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
            wxCheckService.syncWxMipBill(billDate, billFile)
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }
}
