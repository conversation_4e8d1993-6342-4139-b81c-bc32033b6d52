<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改字典数据')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-dict-edit" th:object="${dict}">
        <input name="dictCode" type="hidden" th:field="*{dictCode}"/>
        <input type="hidden" id="dictValue" name="dictValue" th:field="*{dictValue}">
        <input type="hidden" id="dictType" name="dictType" th:field="*{dictType}">
        <input type="hidden" id="cssClass" name="cssClass" th:field="*{cssClass}">
        <input type="hidden" id="listClass" name="listClass" th:field="*{listClass}">
        <input type="hidden" id="isDefault" name="isDefault" th:field="*{isDefault}">
        <input type="hidden" id="status" name="status" th:field="*{status}">
        <input type="hidden" id="remark" name="remark" th:field="*{remark}">

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="dictLabel" id="dictLabel" th:field="*{dictLabel}"
                       required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">序号：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="dictSort" th:field="*{dictSort}" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script type="text/javascript">
    const prefix = ctx + "system/dict/lite";

    $("#form-dict-edit").validate({
        rules: {
            dictSort: {
                digits: true
            },
        },
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-dict-edit').serialize());
        }
    }


</script>
</body>
</html>
