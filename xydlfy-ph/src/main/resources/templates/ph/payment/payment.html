<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('充值记录列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>下单时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <label>患者索引号：</label>
                            <input type="text" name="patientNo"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="jzCardNo"/>
                        </li>
                        <li>
                            <label>掌医订单号：</label>
                            <input type="text" name="zyPayNo"/>
                        </li>
                        <li>
                            <label>微信订单号：</label>
                            <input type="text" name="wxPayNo"/>
                        </li>
                        <li>
                            <label>HIS收据号：</label>
                            <input type="text" name="hisReceiptNo"/>
                        </li>
                        <li>
                            <label>人工充值状态：</label>
                            <select name="manualPayState" th:with="type=${@dict.getType('manual_state')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>只显示错单：</label>
                            <select name="params[filterErrorOrder]">
                                <option value="">所有</option>
                                <option value="true">是</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:payment:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var queryWXOrderFlag = [[${@permission.hasPermi('ph:payment:queryWXOrder')}]];
    var queryHisBillFlag = [[${@permission.hasPermi('ph:payment:queryHisBill')}]];
    var requestHisRechargeFlag = [[${@permission.hasPermi('ph:payment:requestHisRecharge')}]];
    var approveHisRechargeFlag = [[${@permission.hasPermi('ph:payment:approveHisRecharge')}]];
    var manualPayStateDatas = [[${@dict.getType('manual_state')}]];
    var prefix = ctx + "ph/payment";

    var strToChunks = (s, size) => {
        var numChunks = Math.ceil(s.length / size);
        var chunks = new Array(numChunks);

        for (var i = 0, o = 0; i < numChunks; ++i, o += size) {
            chunks[i] = s.substr(o, size);
        }

        return chunks;
    }

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "微信充值记录",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '下单时间'
                },
                {
                    field: 'patientNo',
                    title: '患者索引号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号',
                    formatter: function (value, row, index) {
                        if (!value) {
                            return value;
                        } else {
                            return strToChunks(value, 16).join('<br/>');
                        }
                    }
                },
                {
                    field: 'zyPayNo',
                    title: '掌医订单号'
                },
                {
                    field: 'amount',
                    title: '订单金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'exrefund',
                    title: '已退金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'unrefund',
                    title: '未退金额',
                    formatter: function (value, row, index) {
                        if (value) {
                            return (Number(value) / 100).toFixed(2);
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field: 'wxPayNo',
                    title: '微信订单号'
                },
                {
                    field: 'wxTradeStatus',
                    title: '微信交易状态'
                },
                {
                    field: 'hisTradeStatus',
                    title: 'HIS交易状态'
                },
                {
                    field: 'balance',
                    title: '余额'
                },
                {
                    field: 'manualPayState',
                    title: '人工充值状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(manualPayStateDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + queryWXOrderFlag + '" href="javascript:void(0)" onclick="queryWXOrder(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询微信订单状态</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + queryHisBillFlag + '" href="javascript:void(0)" onclick="queryHisBill(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询HIS账单</a> ');
                        if (row['wxTradeStatus'] === '支付成功' && row['hisTradeStatus'] !== '充值成功' && row['manualPayState'] == 0) {
                            actions.push('<a class="btn btn-success btn-xs ' + requestHisRechargeFlag + '" href="javascript:void(0)" onclick="requestHisRecharge(\'' + row.id + '\')"><i class="fa fa-paper-plane-o"></i>申请HIS充值</a> ');
                        }
                        if (row['wxTradeStatus'] === '支付成功' && row['hisTradeStatus'] !== '充值成功' && row['manualPayState'] == 1) {
                            actions.push('<a class="btn btn-success btn-xs ' + approveHisRechargeFlag + '" href="javascript:void(0)" onclick="approveHisRecharge(\'' + row.id + '\')"><i class="fa fa-check"></i>放行HIS充值</a> ');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryWXOrder(id) {
        $.operate.get(prefix + '/queryWXOrder/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询订单状态',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function queryHisBill(id) {
        $.operate.get(prefix + '/queryHisBill/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询HIS账单',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function requestHisRecharge(id) {
        $.operate.post(prefix + '/requestHisRecharge/' + id, {})
    }

    function approveHisRecharge(id) {
        $.operate.post(prefix + '/approveHisRecharge/' + id, {})
    }
</script>
</body>
</html>
