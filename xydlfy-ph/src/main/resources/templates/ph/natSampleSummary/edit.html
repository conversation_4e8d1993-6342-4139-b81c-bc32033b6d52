<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('修改采样日报')"/>
  <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
  <form class="form-horizontal m" id="form-natSampleSummary-edit" th:object="${natSampleSummary}">
    <input name="id" th:field="*{id}" type="hidden">
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">日期：</label>
      <div class="col-sm-8">
        <div class="input-group date">
          <input name="day" th:value="${#dates.format(natSampleSummary.day, 'yyyy-MM-dd')}" class="form-control"
                 placeholder="yyyy-MM-dd" type="text" required>
          <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">应采：</label>
      <div class="col-sm-8">
        <input name="yingCai" th:field="*{yingCai}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">实采：</label>
      <div class="col-sm-8">
        <input name="shiCai" th:field="*{shiCai}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">未采：</label>
      <div class="col-sm-8">
        <input name="weiCai" th:field="*{weiCai}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">推送成功：</label>
      <div class="col-sm-8">
        <input name="pushOk" th:field="*{pushOk}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">推送失败：</label>
      <div class="col-sm-8">
        <input name="pushFail" th:field="*{pushFail}" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">待推送：</label>
      <div class="col-sm-8">
        <input name="pushTodo" th:field="*{pushTodo}" class="form-control" type="text" required>
      </div>
    </div>
  </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
  var prefix = ctx + "ph/natSampleSummary";
  $("#form-natSampleSummary-edit").validate({
    focusCleanup: true
  });

  function submitHandler() {
    if ($.validate.form()) {
      $.operate.save(prefix + "/edit", $('#form-natSampleSummary-edit').serialize());
    }
  }

  $("input[name='day']").datetimepicker({
    format: "yyyy-mm-dd",
    minView: "month",
    autoclose: true
  });
</script>
</body>
</html>