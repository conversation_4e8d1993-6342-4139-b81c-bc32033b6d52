<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.SessionMapper">

    <resultMap type="space.lzhq.ph.domain.Session" id="SessionResult">
        <result property="id" column="id"/>
        <result property="clientType" column="client_type"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="sessionKey" column="session_key"/>
        <result property="ahoat" column="ahoat"/>
    </resultMap>

    <sql id="selectSessionVo">
        select id, client_type, open_id, union_id, session_key, ahoat
        from ph_wx_session
    </sql>

    <select id="selectSessionList" parameterType="Session" resultMap="SessionResult">
        <include refid="selectSessionVo"/>
        <where>
            <if test="clientType != null">and client_type = #{clientType}</if>
            <if test="openId != null">and open_id = #{openId}</if>
            <if test="unionId != null">and union_id = #{unionId}</if>
            <if test="sessionKey != null">and session_key = #{sessionKey}</if>
            <if test="ahoat != null">and ahoat = #{ahoat}</if>
        </where>
    </select>

    <select id="selectSessionById" parameterType="String" resultMap="SessionResult">
        <include refid="selectSessionVo"/>
        where id = #{id}
    </select>

    <insert id="insertSession" parameterType="Session">
        insert into ph_wx_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="clientType != null and clientType != ''">client_type,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="sessionKey != null">session_key,</if>
            <if test="ahoat != null">ahoat,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="clientType != null and clientType != ''">#{clientType},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="sessionKey != null">#{sessionKey},</if>
            <if test="ahoat != null">#{ahoat},</if>
        </trim>
    </insert>

    <update id="updateSession" parameterType="Session">
        update ph_wx_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientType != null">client_type = #{clientType},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="ahoat != null">ahoat = #{ahoat},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSessionById" parameterType="String">
        delete
        from ph_wx_session
        where id = #{id}
    </delete>

    <delete id="deleteSessionByOpenId" parameterType="String">
        delete
        from ph_wx_session
        where open_id = #{openId}
    </delete>

    <delete id="deleteSessionByIds" parameterType="String">
        delete from ph_wx_session where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
