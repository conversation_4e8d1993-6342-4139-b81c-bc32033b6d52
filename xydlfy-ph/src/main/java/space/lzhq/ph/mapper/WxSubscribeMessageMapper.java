package space.lzhq.ph.mapper;

import java.util.List;
import space.lzhq.ph.domain.WxSubscribeMessage;

/**
 * 微信订阅消息Mapper接口
 *
 * <AUTHOR>
 * @date 2020-06-16
 */
public interface WxSubscribeMessageMapper
{
    /**
     * 查询微信订阅消息
     *
     * @param id 微信订阅消息ID
     * @return 微信订阅消息
     */
    public WxSubscribeMessage selectWxSubscribeMessageById(Long id);

    /**
     * 查询微信订阅消息列表
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 微信订阅消息集合
     */
    public List<WxSubscribeMessage> selectWxSubscribeMessageList(WxSubscribeMessage wxSubscribeMessage);

    /**
     * 新增微信订阅消息
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 结果
     */
    public int insertWxSubscribeMessage(WxSubscribeMessage wxSubscribeMessage);

    /**
     * 修改微信订阅消息
     *
     * @param wxSubscribeMessage 微信订阅消息
     * @return 结果
     */
    public int updateWxSubscribeMessage(WxSubscribeMessage wxSubscribeMessage);

    /**
     * 删除微信订阅消息
     *
     * @param id 微信订阅消息ID
     * @return 结果
     */
    public int deleteWxSubscribeMessageById(Long id);

    /**
     * 批量删除微信订阅消息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteWxSubscribeMessageByIds(String[] ids);
}
