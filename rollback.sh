#!/bin/bash
set -euo pipefail
trap 'log_error "脚本执行失败，行号: $LINENO"' ERR

# Git Bash 版本的 jar包回退工具
# 适用于Windows平台的Git Bash环境

# 配置参数
ZYXCX_JAR_PATH="/d/zyxcx/zyxcx.jar"
BACKUP_DIR="/d/zyxcx/backup"
SERVICE_NAME="zyxcx"

# 全局变量存储备份文件数组
declare -a BACKUP_FILES

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示标题
show_header() {
    echo
    echo -e "${CYAN}========================================"
    echo -e "           🔄 jar包回退工具"
    echo -e "========================================${NC}"
    echo
}

# 检查备份目录
check_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        log_error "请确认是否进行过部署操作"
        read -p "按任意键退出..."
        exit 1
    fi
}

# 扫描备份文件
scan_backup_files() {
    BACKUP_FILES=()  # 清空全局数组
    local backup_count=0
    
    # 暂时禁用 set -e 以处理条件判断
    set +e
    
    # 使用数组存储备份文件，按时间倒序排列
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        
        # 验证文件名格式: zyxcx_YYYYMMDD_HHMMSS.jar
        # 使用POSIX兼容的字符类重复，避免扩展正则表达式兼容性问题
        if [[ $filename =~ ^zyxcx_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]_[0-9][0-9][0-9][0-9][0-9][0-9]\.jar$ ]]; then
            BACKUP_FILES+=("$file")
            ((backup_count++))
        else
            log_warn "跳过格式不正确的文件: $filename"
        fi
    done < <(find "$BACKUP_DIR" -name "zyxcx_*.jar" -type f -print0 | sort -z -t_ -k2,2r -k3,3r)
    
    # 重新启用 set -e
    set -e
    
    if [ $backup_count -eq 0 ]; then
        log_error "没有找到任何备份文件"
        log_error "备份目录: $BACKUP_DIR"
        read -p "按任意键退出..."
        exit 1
    fi
    
    log_info "扫描完成，找到 $backup_count 个备份文件"
}

# 格式化显示时间
format_timestamp() {
    local filename="$1"
    
    # 提取时间戳: zyxcx_20250619_181234.jar -> 20250619_181234
    local timestamp=$(echo "$filename" | sed 's/zyxcx_\([0-9]*_[0-9]*\)\.jar/\1/')
    
    # 分离日期和时间部分
    local date_part="${timestamp%_*}"      # 20250619
    local time_part="${timestamp#*_}"      # 181234
    
    # 格式化日期: 20250619 -> 2025-06-19
    local formatted_date="${date_part:0:4}-${date_part:4:2}-${date_part:6:2}"
    
    # 格式化时间: 181234 -> 18:12:34
    local formatted_time="${time_part:0:2}:${time_part:2:2}:${time_part:4:2}"
    
    echo "$formatted_date $formatted_time"
}

# 显示备份文件列表
show_backup_list() {
    local count=${#BACKUP_FILES[@]}
    
    log_info "在备份目录中找到 $count 个备份文件："
    log_info "备份目录: $BACKUP_DIR"
    echo
    
    # 显示备份文件列表
    for i in "${!BACKUP_FILES[@]}"; do
        local file="${BACKUP_FILES[$i]}"
        local filename=$(basename "$file")
        local formatted_time=$(format_timestamp "$filename")
        
        echo -e "${WHITE}[$(($i + 1))]${NC} $filename ${CYAN}($formatted_time)${NC}"
    done
    
    echo
    echo -e "${YELLOW}[0]${NC} 退出回退操作"
    echo
}

# 验证用户输入
validate_choice() {
    local choice="$1"
    local max_choice="$2"
    
    # 检查是否为数字
    if ! [[ "$choice" =~ ^[0-9]+$ ]]; then
        return 1
    fi
    
    # 检查范围
    if [ "$choice" -lt 0 ] || [ "$choice" -gt "$max_choice" ]; then
        return 1
    fi
    
    return 0
}

# 用户选择备份文件
select_backup() {
    local count=${#BACKUP_FILES[@]}
    local choice
    
    while true; do
        read -p "请选择要回退的备份版本 (0-$count): " choice
        
        if validate_choice "$choice" "$count"; then
            break
        else
            log_error "无效的选择，请输入 0 到 $count 之间的数字"
        fi
    done
    
    if [ "$choice" -eq 0 ]; then
        log_info "用户取消回退操作"
        return 1  # 返回错误码表示用户取消
    fi
    
    # 返回选中的文件路径 (数组索引从0开始，所以减1)
    echo "${BACKUP_FILES[$((choice - 1))]}"
    return 0  # 返回成功码
}

# 确认回退操作
confirm_rollback() {
    local selected_file="$1"
    local filename=$(basename "$selected_file")
    
    echo
    echo -e "${CYAN}========================================"
    log_info "您选择的备份文件："
    log_info "文件名: $filename"
    log_info "路径: $selected_file"
    echo -e "${CYAN}========================================${NC}"
    echo
    
    while true; do
        read -p "确认回退到此版本吗？这将覆盖当前的jar文件 (Y/N): " confirm
        
        case "$confirm" in
            [Yy]|[Yy][Ee][Ss])
                return 0
                ;;
            [Nn]|[Nn][Oo])
                log_info "回退操作已取消"
                exit 0
                ;;
            *)
                log_warn "请输入 Y 或 N"
                ;;
        esac
    done
}

# 停止Windows服务
stop_service() {
    local service_name="$1"
    
    log_info "检查服务状态: $service_name"
    
    # 首先检查服务是否存在并获取状态
    local service_status
    service_status=$(sc query "$service_name" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_warn "服务不存在或无法查询: $service_name"
        return 0  # 服务不存在时返回成功，允许继续回退
    fi
    
    # 检查服务是否正在运行
    if echo "$service_status" | grep -q "STOPPED"; then
        log_info "服务已处于停止状态，无需停止操作"
        return 0
    elif echo "$service_status" | grep -q "RUNNING"; then
        log_info "服务正在运行，开始停止操作"
        
        if sc stop "$service_name" >/dev/null 2>&1; then
            # 等待服务停止
            local count=0
            while [ $count -lt 30 ]; do
                if sc query "$service_name" 2>/dev/null | grep -q "STOPPED"; then
                    log_info "服务停止成功"
                    return 0
                fi
                sleep 2
                ((count += 2))
            done
            
            log_error "服务停止超时"
            return 1
        else
            log_error "服务停止失败"
            return 1
        fi
    else
        log_warn "服务状态未知，尝试直接停止"
        sc stop "$service_name" >/dev/null 2>&1
        log_info "已发送停止命令，继续回退流程"
        return 0
    fi
}

# 启动Windows服务
start_service() {
    local service_name="$1"
    
    log_info "检查服务状态: $service_name"
    
    # 首先检查服务是否存在并获取状态
    local service_status
    service_status=$(sc query "$service_name" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_error "服务不存在或无法查询: $service_name"
        return 1
    fi
    
    # 检查服务是否已经在运行
    if echo "$service_status" | grep -q "RUNNING"; then
        log_info "服务已处于运行状态，无需启动操作"
        return 0
    elif echo "$service_status" | grep -q "STOPPED"; then
        log_info "服务已停止，开始启动操作"
        
        if sc start "$service_name" >/dev/null 2>&1; then
            log_info "服务启动成功"
            return 0
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_warn "服务状态未知，尝试启动"
        if sc start "$service_name" >/dev/null 2>&1; then
            log_info "服务启动成功"
            return 0
        else
            log_error "服务启动失败"
            return 1
        fi
    fi
}

# 执行回退操作
execute_rollback() {
    local selected_file="$1"
    local filename=$(basename "$selected_file")
    
    echo
    log_info "开始执行回退操作..."
    echo
    
    # 检查当前jar文件是否存在
    if [ ! -f "$ZYXCX_JAR_PATH" ]; then
        log_warn "当前jar文件不存在: $ZYXCX_JAR_PATH"
        log_info "将直接复制备份文件"
    else
        log_info "当前jar文件: $ZYXCX_JAR_PATH"
    fi
    
    # 停止服务
    if ! stop_service "$SERVICE_NAME"; then
        show_error_exit "服务停止失败"
        return 1
    fi
    
    # 复制备份文件
    log_info "复制备份文件到目标位置..."
    if cp "$selected_file" "$ZYXCX_JAR_PATH"; then
        log_info "备份文件复制成功"
    else
        log_error "文件复制失败"
        log_error "源文件: $selected_file"
        log_error "目标文件: $ZYXCX_JAR_PATH"
        show_error_exit "文件复制失败"
        return 1
    fi
    
    # 启动服务
    if start_service "$SERVICE_NAME"; then
        show_success_exit "$filename"
    else
        log_error "服务启动失败"
        log_warn "jar文件已回退，但服务启动失败"
        log_warn "请手动检查服务状态和日志"
        show_error_exit "服务启动失败"
        return 1
    fi
}

# 显示成功退出信息
show_success_exit() {
    local filename="$1"
    
    echo
    echo -e "${GREEN}========================================"
    log_success "回退操作完成！"
    log_info "当前运行版本: $filename"
    log_info "服务已启动，请验证系统功能"
    echo -e "${GREEN}========================================${NC}"
    echo
    
    read -p "按任意键退出..."
}

# 显示错误退出信息
show_error_exit() {
    local error_msg="$1"
    
    echo
    echo -e "${RED}========================================"
    log_error "回退操作失败！"
    log_error "$error_msg"
    log_info "请检查错误信息并手动处理"
    echo -e "${RED}========================================${NC}"
    echo
    
    read -p "按任意键退出..."
    exit 1
}

# 显示帮助信息
show_help() {
    echo "jar包回退工具使用说明:"
    echo "  ./rollback.sh         # 启动交互式回退工具"
    echo "  ./rollback.sh -h      # 显示此帮助信息"
    echo ""
    echo "功能特性:"
    echo "  - 列出所有可用的备份文件"
    echo "  - 交互式选择要回退的版本"
    echo "  - 自动停止和启动Windows服务"
    echo "  - 彩色输出和友好的用户界面"
    echo "  - 完整的错误处理和确认机制"
    exit 0
}

# 主函数
main() {
    # 参数处理
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    # 显示标题
    show_header
    
    # 检查备份目录
    check_backup_dir
    
    # 扫描备份文件
    log_info "扫描备份文件..."
    scan_backup_files
    
    # 显示备份列表
    show_backup_list
    
    # 用户选择
    local selected_file
    if selected_file=$(select_backup); then
        # 确认操作
        confirm_rollback "$selected_file"
        
        # 执行回退
        execute_rollback "$selected_file"
    else
        # 用户取消操作，直接退出
        exit 0
    fi
}

# 检查是否在Windows环境下运行
if ! command -v sc >/dev/null 2>&1; then
    log_error "此脚本需要在Windows环境下的Git Bash中运行"
    log_error "未找到 sc 命令（Windows服务控制工具）"
    exit 1
fi

# 执行主函数
main "$@" 