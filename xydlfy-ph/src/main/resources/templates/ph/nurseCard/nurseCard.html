<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('电子陪护证列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>住院号：</label>
                            <input type="text" name="zhuyuanNo"/>
                        </li>
                        <li>
                            <label>患者姓名：</label>
                            <input type="text" name="patientName"/>
                        </li>
                        <li>
                            <label>患者ID：</label>
                            <input type="text" name="patientId"/>
                        </li>
                        <li>
                            <label>患者手机号：</label>
                            <input type="text" name="patientMobile"/>
                        </li>
                        <li>
                            <label>陪护人姓名：</label>
                            <input type="text" name="nurseName"/>
                        </li>
                        <li>
                            <label>陪护人身份证号：</label>
                            <input type="text" name="nurseIdCardNo"/>
                        </li>
                        <li>
                            <label>陪护人手机号：</label>
                            <input type="text" name="nurseMobile"/>
                        </li>
                        <li class="select-time">
                            <label>申请时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" th:with="type=${@dict.getType('nurse_card_status')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:nurseCard:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:nurseCard:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var removeFlag = [[${@permission.hasPermi('ph:nurseCard:remove')}]];
    var acceptFlag = [[${@permission.hasPermi('ph:nurseCard:accept')}]];
    var rejectFlag = [[${@permission.hasPermi('ph:nurseCard:reject')}]];
    var statusDatas = [[${@dict.getType('nurse_card_status')}]];
    var prefix = ctx + "ph/nurseCard";

    function reject(id) {
        layer.prompt({
            formType: 2,
            value: '',
            title: '请输入审核意见',
            btn: ['审核不通过'],
            area: ['400px', '150px']
        }, function (auditRemark, promptIndex) {
            layer.close(promptIndex);
            doReject(id, auditRemark);
        });
    }

    function doReject(id, auditRemark) {
        $.modal.loading("正在执行，请稍等...");
        $.post(prefix + "/reject", {
            id: id,
            auditRemark: auditRemark
        }, function (ret) {
            $.modal.closeLoading();
            if (ret.code === 0) {
                $.modal.alertSuccess("操作成功");
                $.table.refresh();
            } else {
                $.modal.alertError(ret.msg);
            }
        });
    }

    function accept(id) {
        layer.confirm('审核通过后，该电子陪护证将立即生效，确定继续吗？', {
            icon: 3, title: '电子陪护证'
        }, function (confirmIndex) {
            layer.close(confirmIndex);
            doAccept(id);
        });
    }

    function doAccept(id) {
        $.modal.loading("正在执行，请稍等...");
        $.post(prefix + "/accept", {
            id: id
        }, function (ret) {
            $.modal.closeLoading();
            if (ret.code === 0) {
                $.modal.alertSuccess("操作成功");
                $.table.refresh();
            } else {
                $.modal.alertError(ret.msg);
            }
        });
    }

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "电子陪护证",
            sortName: "id",
            sortOrder: "desc",
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'zhuyuanNo',
                    title: '住院号'
                },
                {
                    field: 'department',
                    title: '病区'
                },
                {
                    field: 'ruyuanTime',
                    title: '入院时间'
                },
                {
                    field: 'chuyuanTime',
                    title: '出院时间'
                },
                {
                    field: 'patientName',
                    title: '患者姓名'
                },
                {
                    field: 'patientId',
                    title: '患者ID'
                },
                {
                    field: 'patientMobile',
                    title: '患者手机号'
                },
                {
                    field: 'nurseName',
                    title: '陪护人姓名'
                },
                {
                    field: 'nurseIdCardNo',
                    title: '陪护人身份证号'
                },
                {
                    field: 'nurseMobile',
                    title: '陪护人手机号'
                },
                {
                    field: 'relationship.description',
                    title: '是被陪护人的'
                },
                {
                    field: 'nurseNatImage',
                    title: '陪护人核酸报告',
                    formatter: function (value, row, index) {
                        return '<a href="' + value + '" target="_blank">查看</a>';
                    }
                },
                {
                    field: 'nursePhoto',
                    title: '陪护人照片',
                    formatter: function (value, row, index) {
                        return '<a href="' + value + '" target="_blank">查看</a>';
                    }
                },
                {
                    field: 'createTime',
                    title: '申请时间'
                },
                {
                    field: 'cancelTime',
                    title: '取消时间',
                    visible: false
                },
                {
                    field: 'auditTime',
                    title: '审核时间'
                },
                {
                    field: 'effectiveTime',
                    title: '生效时间',
                    visible: false
                },
                {
                    field: 'invalidTime',
                    title: '作废时间',
                    visible: false
                },
                {
                    field: 'expiredTime',
                    title: '失效时间',
                    visible: false
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value.code);
                    }
                },
                {
                    field: 'auditRemark',
                    title: '审核意见'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        if (row.status.code === 0) {
                            actions.push('<a class="btn btn-primary btn-xs ' + acceptFlag + '" href="javascript:void(0)" onclick="accept(\'' + row.id + '\')"><i class="fa fa-check"></i>审核通过</a>');
                            actions.push('<a class="btn btn-warning btn-xs ' + rejectFlag + '" href="javascript:void(0)" onclick="reject(\'' + row.id + '\')"><i class="fa fa-times"></i>审核不通过</a>');
                        }
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>