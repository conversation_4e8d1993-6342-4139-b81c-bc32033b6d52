<!-- Generated by SpecStory -->

# 切换到暂存工作空间 (2025-05-30 01:30Z)

_**User**_

怎样切回暂存的工作空间

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户刚才执行了 `git stash` 来暂存工作目录和索引状态，然后成功拉取了代码。现在用户想要切回到暂存的工作空间。

在 Git 中，要恢复之前暂存的工作空间，可以使用 `git stash pop` 或 `git stash apply`：

- `git stash pop`: 恢复暂存的更改并从stash列表中删除该记录
- `git stash apply`: 恢复暂存的更改但保留stash记录

通常我们使用 `git stash pop` 来恢复最近的暂存状态。</details></think>

---

要切回到你刚才暂存的工作空间，你可以使用以下命令：

---

```bash
git stash pop
```

```
On branch master
Your branch is up to date with 'github/master'.

Changes to be committed:
  (use "git restore --staged <file>..." to unstage)
        new file:   ruoyi-common/src/main/java/com/ruoyi/common/annotation/Valid
DepartmentCode.java
        new file:   ruoyi-common/src/main/java/com/ruoyi/common/annotation/Valid
DoctorCode.java
        new file:   ruoyi-common/src/main/java/com/ruoyi/common/annotation/Valid
ScheduleId.java

Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
        modified:   xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/Reservati
onApiController.kt
        modified:   xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ScheduleA
piController.kt

Dropped refs/stash@{0} (f6cfd2682573feca0bf415dc0ce7fc2f710b817f)

```

---

完成！工作空间已经成功恢复了。你可以看到之前暂存的更改现在又回来了：

**已暂存的新文件（Changes to be committed）：**
- `ValidDepartmentCode.java`
- `ValidDoctorCode.java`
- `ValidScheduleId.java`

**已修改但未暂存的文件（Changes not staged for commit）：**
- `ReservationApiController.kt`
- `ScheduleApiController.kt`

`git stash pop` 命令已经成功恢复了你的工作空间状态，并且自动删除了这个stash记录（你可以看到输出中的 "Dropped refs/stash@{0}"）。

现在你可以继续之前的工作了。如果你想查看具体的更改，可以使用：
- `git diff` 查看未暂存的更改
- `git diff --cached` 查看已暂存的更改

---

