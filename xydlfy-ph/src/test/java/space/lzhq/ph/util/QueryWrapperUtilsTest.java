package space.lzhq.ph.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@SuppressWarnings("unchecked")
class QueryWrapperUtilsTest {

    // 使用一个简单的测试实体类
    @Data
    static class TestEntity {
        private String name;
        private Integer status;
        private LocalDateTime createTime;
    }

    @Test
    void testEqIfNotBlank() {
        // 使用Mockito模拟LambdaQueryWrapper
        LambdaQueryWrapper<TestEntity> wrapper = Mockito.mock(LambdaQueryWrapper.class);

        // 测试非空字符串
        QueryWrapperUtils.eqIfNotBlank(wrapper, TestEntity::getName, "张三");
        verify(wrapper).eq(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试空字符串
        QueryWrapperUtils.eqIfNotBlank(wrapper, TestEntity::getName, "");
        verify(wrapper, never()).eq(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试null
        QueryWrapperUtils.eqIfNotBlank(wrapper, TestEntity::getName, null);
        verify(wrapper, never()).eq(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试空白字符串
        QueryWrapperUtils.eqIfNotBlank(wrapper, TestEntity::getName, "   ");
        verify(wrapper, never()).eq(any(), any());
    }

    @Test
    void testLikeIfNotBlank() {
        // 使用Mockito模拟LambdaQueryWrapper
        @SuppressWarnings("unchecked")
        LambdaQueryWrapper<TestEntity> wrapper = Mockito.mock(LambdaQueryWrapper.class);

        // 测试非空字符串
        QueryWrapperUtils.likeIfNotBlank(wrapper, TestEntity::getName, "张三");
        verify(wrapper).like(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试空字符串
        QueryWrapperUtils.likeIfNotBlank(wrapper, TestEntity::getName, "");
        verify(wrapper, never()).like(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试null
        QueryWrapperUtils.likeIfNotBlank(wrapper, TestEntity::getName, null);
        verify(wrapper, never()).like(any(), any());
    }

    @Test
    void testEqIfNotNull() {
        // 使用Mockito模拟LambdaQueryWrapper
        @SuppressWarnings("unchecked")
        LambdaQueryWrapper<TestEntity> wrapper = Mockito.mock(LambdaQueryWrapper.class);

        // 测试非null值
        QueryWrapperUtils.eqIfNotNull(wrapper, TestEntity::getStatus, 1);
        verify(wrapper).eq(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试null值
        QueryWrapperUtils.eqIfNotNull(wrapper, TestEntity::getStatus, null);
        verify(wrapper, never()).eq(any(), any());
    }

    @Test
    void testGeIfNotNull() {
        // 使用Mockito模拟LambdaQueryWrapper
        @SuppressWarnings("unchecked")
        LambdaQueryWrapper<TestEntity> wrapper = Mockito.mock(LambdaQueryWrapper.class);

        // 测试非null值
        LocalDateTime now = LocalDateTime.now();
        QueryWrapperUtils.geIfNotNull(wrapper, TestEntity::getCreateTime, now);
        verify(wrapper).ge(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试null值
        QueryWrapperUtils.geIfNotNull(wrapper, TestEntity::getCreateTime, null);
        verify(wrapper, never()).ge(any(), any());
    }

    @Test
    void testLeIfNotNull() {
        // 使用Mockito模拟LambdaQueryWrapper
        @SuppressWarnings("unchecked")
        LambdaQueryWrapper<TestEntity> wrapper = Mockito.mock(LambdaQueryWrapper.class);

        // 测试非null值
        LocalDateTime now = LocalDateTime.now();
        QueryWrapperUtils.leIfNotNull(wrapper, TestEntity::getCreateTime, now);
        verify(wrapper).le(any(), any());

        // 重置mock
        Mockito.reset(wrapper);

        // 测试null值
        QueryWrapperUtils.leIfNotNull(wrapper, TestEntity::getCreateTime, null);
        verify(wrapper, never()).le(any(), any());
    }

    // 直接测试工具类中的逻辑
    @Test
    void testStringUtils() {
        assertTrue(StringUtils.isNotBlank("张三"));
        assertFalse(StringUtils.isNotBlank(""));
        assertFalse(StringUtils.isNotBlank(null));
        assertFalse(StringUtils.isNotBlank("   "));
    }
}
