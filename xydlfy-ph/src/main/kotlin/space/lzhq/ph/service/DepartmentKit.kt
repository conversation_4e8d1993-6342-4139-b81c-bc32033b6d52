package space.lzhq.ph.service

import com.ruoyi.common.utils.PinyinUtils
import com.ruoyi.common.utils.spring.SpringUtils
import com.ruoyi.system.service.ISysConfigService
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.DepartmentRequest
import org.mospital.dongruan.bean.DepartmentResponseV1
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.Department
import java.text.Collator
import java.util.*

object DepartmentKit {

    private val log: Logger = LoggerFactory.getLogger(DepartmentKit::class.java)

    fun getDepartmentsFromHisV1(testMode: Boolean = false): List<Department> {
        val departmentsResponse: DepartmentResponseV1 =
            DongruanService.getDepartmentsV1(DepartmentRequest(type = "ALL"), testMode = testMode)
        if (!departmentsResponse.isOk() || departmentsResponse.departments.isEmpty()) {
            return emptyList()
        }

        val departmentMap = mutableMapOf<String, Department>()
        departmentsResponse.departments.forEach {
            val department = Department().apply {
                this.id = it.code
                this.name = it.name
                this.type = it.type
                this.branch = it.branch
                this.parentId = ""
                this.parentName = ""
                this.intro = it.intro
                this.address = it.address
                this.logo = ""
                this.telephone = ""
                this.keyword = ""
                this.sortNo = 0
                this.simplePinyin = PinyinUtils.getSimplePinyin(it.name, "", true)
                this.fullPinyin = PinyinUtils.getFullPinyin(it.name, "", true)
            }
            departmentMap[department.id] = department
        }

        return departmentMap.values.toList()
    }

    fun sync() {
        val departmentService: IDepartmentService = SpringUtils.getBean(IDepartmentService::class.java)
        try {
            val departments = getDepartmentsFromHisV1()
            if (departments.isEmpty()) {
                return
            }

            departmentService.truncateTable()
            departmentService.insertDepartments(departments)
            Values.INDEXED_DEPARTMENTS = indexDepartments(departments)
        } catch (e: Exception) {
            log.debug("同步科室出错", e)
        }
    }

    fun indexDepartments(departments: List<Department>): List<Map<String, Any>> {
        val sysConfigService = SpringUtils.getBean(ISysConfigService::class.java)
        val excludedDepartments = sysConfigService.excludedDepartments
        return departments.filter {
            !excludedDepartments.contains(it.name)
        }.groupBy {
            it.simplePinyin.first().uppercase()
        }.map {
            mapOf(
                "index" to it.key,
                "departments" to it.value.sortedWith { d1: Department, d2: Department ->
                    Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(d1.name, d2.name)
                }
            )
        }
            .sortedWith { o1: Map<String, Any>, o2: Map<String, Any> -> (o1["index"] as String).compareTo(o2["index"] as String) }
    }

}
