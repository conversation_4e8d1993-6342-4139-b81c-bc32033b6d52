package space.lzhq.ph;

import com.ruoyi.RuoYiApplication;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 集成测试基类
 * <p>
 * 所有需要完整Spring Boot上下文的集成测试都应该继承此类
 * 使用相同的配置可以让Spring Test复用应用上下文，提高测试执行效率
 * <p>
 * 最佳实践：
 * 1. 仅在需要真实依赖交互的集成测试中使用
 * 2. 单元测试应使用 @ExtendWith(MockitoExtension.class) 和 Mock
 * 3. 数据访问测试可使用 @DataJpaTest 等切片测试
 * 4. Web层测试可使用 @WebMvcTest
 */
@SpringBootTest(classes = {RuoYiApplication.class})
public abstract class BaseIntegrationTest {

}