<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.MaterialMapper">

    <resultMap type="Material" id="MaterialResult">
        <result property="id" column="id"/>
        <result property="no" column="no"/>
        <result property="name" column="name"/>
        <result property="pinyinCode" column="pinyin_code"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="unit" column="unit"/>
        <result property="price" column="price"/>
    </resultMap>

    <sql id="selectMaterialVo">
        select id,
               no,
               name,
               pinyin_code,
               type_code,
               type_name,
               unit,
               price
        from ph_material
    </sql>

    <select id="selectMaterialList" parameterType="Material" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            <if test="no != null  and no != ''">and no = #{no}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="pinyinCode != null  and pinyinCode != ''">and pinyin_code like concat('%', #{pinyinCode}, '%')
            </if>
            <if test="typeCode != null  and typeCode != ''">and type_code = #{typeCode}</if>
            <if test="typeName != null  and typeName != ''">and type_name = #{typeName}</if>
        </where>
    </select>

    <select id="selectMaterialById" parameterType="String" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where id = #{id}
    </select>

    <insert id="insertMaterial" parameterType="Material">
        insert into ph_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="no != null and no != ''">no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="pinyinCode != null and pinyinCode != ''">pinyin_code,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="price != null">price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="no != null and no != ''">#{no},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="pinyinCode != null and pinyinCode != ''">#{pinyinCode},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="price != null">#{price},</if>
        </trim>
    </insert>

    <insert id="insertMaterials">
        insert into ph_material (no, name, pinyin_code, type_code, type_name, unit, price) values
        <foreach collection="materials" item="material" separator=",">
            (#{material.no}, #{material.name}, #{material.pinyinCode}, #{material.typeCode}, #{material.typeName},
            #{material.unit}, #{material.price})
        </foreach>
    </insert>

    <update id="updateMaterial" parameterType="Material">
        update ph_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="no != null and no != ''">no = #{no},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="pinyinCode != null and pinyinCode != ''">pinyin_code = #{pinyinCode},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="price != null">price = #{price},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialById" parameterType="String">
        delete
        from ph_material
        where id = #{id}
    </delete>

    <delete id="deleteMaterialByIds" parameterType="String">
        delete from ph_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="clearAll">
        truncate table ph_material
    </update>
</mapper>