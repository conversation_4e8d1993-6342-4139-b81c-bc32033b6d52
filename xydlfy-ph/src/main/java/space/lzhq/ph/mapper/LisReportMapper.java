package space.lzhq.ph.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.LisReport;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@DataSource(value = DataSourceType.LIS)
public interface LisReportMapper {

    List<LisReport> selectLisReports(@Param("inPatientId") String inPatientId, @Param("outPatientId") String outPatientId);

}
