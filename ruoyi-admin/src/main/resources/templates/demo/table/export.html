<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('导出')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:insert="~{include :: bootstrap-table-export-js}"/>
<script th:inline="javascript">
    var prefix = ctx + "demo/table";
    var datas = [[${@dict.getType('sys_normal_disable')}]];

    $(function() {
        var options = {
            url: prefix + "/list",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showExport: true,
            exportDataType: 'selected', // 导出选择数据
            exportTypes: ['json', 'xml', 'csv', 'txt', 'sql', 'excel'], // 导出的文件类型
            exportOptions: {
                fileName: '用户数据',    // 文件名称设置
                ignoreColumn: [0, 8]    // 忽略第一列和最后一列
            },
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'userId',
                    title: '用户ID'
                },
                {
                    field: 'userCode',
                    title: '用户编号'
            },
            {
                field : 'userName',
                title : '用户姓名'
            },
            {
                field : 'userPhone',
                title : '用户手机'
            },
            {
                field : 'userEmail',
                title : '用户邮箱'
            },
            {
                field : 'userBalance',
                title : '用户余额'
            },
            {
                field: 'status',
                title: '用户状态',
                align: 'center',
                formatter: function(value, row, index) {
                    return $.table.selectDictLabel(datas, value);
                }
            },
            {
                title: '操作',
                align: 'center',
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn btn-success btn-xs" href="#"><i class="fa fa-edit"></i>编辑</a> ');
                    actions.push('<a class="btn btn-danger btn-xs" href="#"><i class="fa fa-remove"></i>删除</a>');
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
