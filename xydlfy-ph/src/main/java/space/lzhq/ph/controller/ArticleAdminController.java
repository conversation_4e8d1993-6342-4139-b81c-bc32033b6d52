package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.Article;
import space.lzhq.ph.service.IArticleService;
import space.lzhq.ph.service.ICategoryService;

import java.util.List;

/**
 * 文章Controller
 *
 * <AUTHOR>
 * @date 2020-05-30
 */
@Controller
@RequestMapping("/ph/article")
public class ArticleAdminController extends BaseController {
    private String prefix = "ph/article";

    @Autowired
    private IArticleService articleService;

    @Autowired
    private ICategoryService categoryService;

    @RequiresPermissions("ph:article:view")
    @GetMapping()
    public String article(ModelMap attrs) {
        attrs.put("categories", categoryService.selectCategoryAll());
        return prefix + "/article";
    }

    /**
     * 查询文章列表
     */
    @RequiresPermissions("ph:article:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Article article) {
        startPage();
        List<Article> list = articleService.selectArticleList(article);
        return getDataTable(list);
    }

    /**
     * 导出文章列表
     */
    @RequiresPermissions("ph:article:export")
    @Log(title = "文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Article article) {
        List<Article> list = articleService.selectArticleList(article);
        ExcelUtil<Article> util = new ExcelUtil<Article>(Article.class);
        return util.exportExcel(list, "article");
    }

    /**
     * 新增文章
     */
    @GetMapping("/add")
    public String add(ModelMap attrs) {
        attrs.put("categories", categoryService.selectCategoryAll());
        return prefix + "/add";
    }

    /**
     * 新增保存文章
     */
    @RequiresPermissions("ph:article:add")
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Article article) {
        return toAjax(articleService.insertArticle(article));
    }

    /**
     * 修改文章
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap attrs) {
        Article article = articleService.selectArticleById(id);
        attrs.put("article", article);
        attrs.put("categories", categoryService.selectCategoryAll());
        return prefix + "/edit";
    }

    /**
     * 修改保存文章
     */
    @RequiresPermissions("ph:article:edit")
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Article article) {
        return toAjax(articleService.updateArticle(article));
    }

    /**
     * 删除文章
     */
    @RequiresPermissions("ph:article:remove")
    @Log(title = "文章", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(articleService.deleteArticleByIds(ids));
    }
}
