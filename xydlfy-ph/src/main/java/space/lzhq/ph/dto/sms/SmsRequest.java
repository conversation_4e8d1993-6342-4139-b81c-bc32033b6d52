package space.lzhq.ph.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 短信发送请求DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 请求类型(appId)
     */
    @NotBlank(message = "应用ID不能为空")
    private String applicationid;

    /**
     * 机构码
     */
    @NotBlank(message = "机构码不能为空")
    @JsonProperty("company_id_")
    private String companyId;

    /**
     * 手机号码(11位)
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String destaddr;

    /**
     * 扩展码(默认'')
     */
    private String extcode = "";

    /**
     * 短信内容
     */
    @NotBlank(message = "短信内容不能为空")
    private String messagecontent;

    /**
     * 短信格式(固定为0)
     */
    private Integer msgfmt = 0;

    /**
     * 状态报告(固定为0)
     */
    private Integer reqdeliveryreport = 0;

    /**
     * 请求时间
     */
    @NotBlank(message = "请求时间不能为空")
    private String requesttime;

    /**
     * 发送方式(验证码填写9，其他一律为0)
     */
    @NotNull(message = "发送方式不能为空")
    private Integer sendmethod = 0;

    /**
     * 短信ID
     */
    @NotNull(message = "短信ID不能为空")
    private Long sismsid;
} 