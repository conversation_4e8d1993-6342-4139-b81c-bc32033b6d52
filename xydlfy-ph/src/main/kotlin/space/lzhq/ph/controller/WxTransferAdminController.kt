package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.ModelAndView
import space.lzhq.ph.domain.WxTransfer
import space.lzhq.ph.domain.WxTransferSearchForm
import space.lzhq.ph.service.IWxTransferService

@RestController
@RequestMapping("/ph/wxTransfer")
class WxTransferAdminController : BaseController() {

    private val prefix = "ph/wxTransfer"

    @Autowired
    private lateinit var wxTransferService: IWxTransferService

    @RequiresPermissions("ph:wxTransfer:view")
    @GetMapping
    fun index(): ModelAndView {
        return ModelAndView("$prefix/index")
    }

    @RequiresPermissions("ph:wxTransfer:page")
    @PostMapping("/page")
    fun page(searchForm: WxTransferSearchForm): TableDataInfo {
        startPage("id desc")
        val list = wxTransferService.listBySearchForm(searchForm)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:wxTransfer:refreshWxTransfer")
    @PostMapping("/refreshWxTransfer/{id}")
    fun refreshWxTransfer(@PathVariable id: Long): AjaxResult {
        val wxTransfer = wxTransferService.getById(id)!!
        wxTransferService.updateTransferState(wxTransfer)
        return AjaxResult.success()
    }

    @RequiresPermissions("ph:wxTransfer:notifyHis")
    @PostMapping("/notifyHis/{id}")
    fun notifyHis(@PathVariable id: Long): AjaxResult {
        wxTransferService.notifyHis(id)
        return AjaxResult.success()
    }

    @RequiresPermissions("ph:wx_transfer:requestWxTransfer")
    @PostMapping("/requestWxTransfer/{id}")
    @ResponseBody
    fun requestWxTransfer(@PathVariable id: String): AjaxResult {
        val wxTransfer = wxTransferService.getById(id)!!
        if (wxTransfer.transferState in WxTransfer.FINAL_STATES) {
            return AjaxResult.error("该笔订单不支持重新发起转账")
        }
        if ("SUCCESS".equals(wxTransfer.transferState)) {
            return AjaxResult.error("该笔订单已成功发起转账")
        }

        wxTransferService.renewOrderNo(wxTransfer)
        wxTransferService.requestTransfer(wxTransfer)
        return AjaxResult.success()
    }
}