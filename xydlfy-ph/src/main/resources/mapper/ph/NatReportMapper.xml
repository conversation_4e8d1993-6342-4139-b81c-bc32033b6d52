<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.NatReportMapper">

    <resultMap id="NatReportResult" type="space.lzhq.ph.domain.NatReport">
        <result property="id" column="ID"/>
        <result property="mixCode" column="CCODE"/>
        <result property="orgName" column="CHECKORGNAME"/>
        <result property="name" column="UNAME"/>
        <result property="sex" column="SEX"/>
        <result property="idCardNo" column="SECONDNAME"/>
        <result property="mobile" column="PHONE"/>
        <result property="collectTime" column="SAMPLINGTIME"/>
        <result property="reportTime" column="RECORDTIME"/>
        <result property="collectorName" column="FDOCTORNAME"/>
        <result property="examinator" column="EXAMINERNAME"/>
        <result property="auditorName" column="AUDITORNAME"/>
        <result property="result" column="CHECKRESULT"/>
    </resultMap>

    <sql id="selectNatReportVo">
        SELECT ID,
               CCODE,        -- 混样编码
               CHECKORGNAME, -- 检测机构
               UNAME,        -- 姓名
               SEX,          -- 性别：1=男，2=女
               SECONDNAME,   -- 身份证号
               PHONE,        -- 手机号
               SAMPLINGTIME, -- 采样时间
               RECORDTIME,   -- 报告时间
               FDOCTORNAME,  -- 采样人
               EXAMINERNAME, -- 检验人
               AUDITORNAME,  -- 审核人
               CHECKTYPE,    -- 检验类型：
               CHECKRESULT   -- 检验结果：1=阴性，2=阳性，3=可疑
        FROM HIS.XGHS_LFY
    </sql>

    <select id="selectNatReports" resultMap="NatReportResult">
        <include refid="selectNatReportVo"/>
        where SECONDNAME = #{idCardNo} and SAMPLINGTIME >= #{minCollectTime}
    </select>
</mapper>
