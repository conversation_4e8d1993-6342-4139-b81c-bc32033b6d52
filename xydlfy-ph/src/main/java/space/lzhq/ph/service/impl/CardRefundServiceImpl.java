package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import space.lzhq.ph.domain.CardRefund;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.mapper.CardRefundMapper;
import space.lzhq.ph.service.ICardRefundService;
import space.lzhq.ph.service.IWxTransferService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
public class CardRefundServiceImpl extends ServiceImpl<CardRefundMapper, CardRefund> implements ICardRefundService {

    private static final BigDecimal DEFAULT_BATCH_SIZE = BigDecimal.valueOf(200);

    private final IWxTransferService wxTransferService;

    public CardRefundServiceImpl(IWxTransferService wxTransferService) {
        this.wxTransferService = wxTransferService;
    }

    @Override
    public List<CardRefund> getListByApplicationId(long applicationId) {
        return lambdaQuery().eq(CardRefund::getApplicationId, applicationId).list();
    }

    @Override
    public boolean existByApplicationIdAndCardNo(long applicationId, String cardNo) {
        return lambdaQuery().eq(CardRefund::getApplicationId, applicationId).eq(CardRefund::getCardNo, cardNo).exists();
    }

    @Override
    @Transactional
    public List<WxTransfer> createWxTransfer(CardRefund cardRefund) {
        BigDecimal unrefund = cardRefund.getAmount();
        List<WxTransfer> wxTransfers = new ArrayList<>();
        while (unrefund.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal refundAmount = DEFAULT_BATCH_SIZE.min(unrefund);
            unrefund = unrefund.subtract(refundAmount);

            WxTransfer wxTransfer = new WxTransfer(cardRefund, refundAmount);
            wxTransfers.add(wxTransfer);
        }
        wxTransferService.saveBatch(wxTransfers);
        cardRefund.setUnrefund(unrefund);
        cardRefund.setExrefund(cardRefund.getAmount().subtract(unrefund));
        updateById(cardRefund);
        return wxTransfers;
    }

}
