package space.lzhq.ph.controller

import com.ruoyi.common.annotation.Log
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.BillResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxPaymentService

/**
 * 充值记录Controller
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Controller
@RequestMapping("/ph/payment")
class WxPaymentAdminController : BaseController() {
    private val prefix = "ph/payment"

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @RequiresPermissions("ph:payment:view")
    @GetMapping
    fun payment(): String {
        return "$prefix/payment"
    }

    /**
     * 查询充值记录列表
     */
    @RequiresPermissions("ph:payment:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxPayment: WxPayment): TableDataInfo {
        startPage()
        val list = wxPaymentService.selectWxPaymentList(wxPayment)
        return getDataTable(list)
    }

    /**
     * 导出充值记录列表
     */
    @RequiresPermissions("ph:payment:export")
    @Log(title = "充值记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxPayment: WxPayment): AjaxResult {
        val list = wxPaymentService.selectWxPaymentList(wxPayment)
        val util = ExcelUtil(WxPayment::class.java)
        return util.exportExcel(list, "payment")
    }

    /**
     * 查询订单
     */
    @RequiresPermissions("ph:payment:queryWXOrder")
    @GetMapping("/queryWXOrder/{id}")
    @ResponseBody
    fun queryWXOrder(@PathVariable id: Long): AjaxResult {
        return try {
            val wxPayment = wxPaymentService.selectWxPaymentById(id)!!

            val wxPayService = when {
                wxPayment.isMenzhen -> WeixinExt.getWxPayServiceForMenZhen()
                wxPayment.isGuahao -> WeixinExt.getWxPayServiceForMenZhen()
                wxPayment.isZhenjian -> WeixinExt.getWxPayServiceForMenZhen()
                wxPayment.isZhuyuan -> WeixinExt.getWxPayServiceForZhuYuan()
                else -> throw IllegalArgumentException("不支持的支付方式")
            }
            val wxPayOrderQueryResult = wxPayService.queryOrder(null, wxPayment.zyPayNo)
            wxPaymentService.updateOnPay(wxPayment, wxPayOrderQueryResult)
            AjaxResult.success(wxPayOrderQueryResult)
        } catch (e: Exception) {
            logger.error(e.message, e)
            AjaxResult.error(e.message)
        }
    }

    /**
     * 查询账单
     */
    @RequiresPermissions("ph:payment:queryHisBill")
    @GetMapping("/queryHisBill/{id}")
    @ResponseBody
    fun queryHisBill(@PathVariable id: Long): AjaxResult {
        return try {
            val wxPayment: WxPayment = wxPaymentService.selectWxPaymentById(id)!!
            val billResponse: BillResponse = DongruanService.queryBills(cardNo = wxPayment.cardNo, testMode = false)
            AjaxResult.success(billResponse)
        } catch (e: Exception) {
            logger.error(e.message, e)
            AjaxResult.error(e.message)
        }
    }

    /**
     * 申请HIS充值
     */
    @RequiresPermissions("ph:payment:requestHisRecharge")
    @PostMapping("/requestHisRecharge/{id}")
    @ResponseBody
    fun requestHisRecharge(@PathVariable id: Long): AjaxResult {
        val wxPayment = wxPaymentService.selectWxPaymentById(id)!!
        if (Constants.PAY_OK != wxPayment.wxTradeStatus
            || Constants.RECHARGE_OK == wxPayment.hisTradeStatus
            || Constants.MANUAL_INIT != wxPayment.manualPayState
        ) {
            return AjaxResult.error("此订单不能申请HIS充值")
        }
        wxPayment.manualPayState = Constants.MANUAL_REQUESTED
        val ok = wxPaymentService.updateWxPayment(wxPayment) == 1
        return if (ok) AjaxResult.success() else AjaxResult.error()
    }

    /**
     * 放行HIS充值
     */
    @RequiresPermissions("ph:payment:approveHisRecharge")
    @PostMapping("/approveHisRecharge/{id}")
    @ResponseBody
    fun approveHisRecharge(@PathVariable id: Long): AjaxResult {
        val payment = wxPaymentService.selectWxPaymentById(id)!!
        if (Constants.PAY_OK != payment.wxTradeStatus
            || Constants.RECHARGE_OK == payment.hisTradeStatus
            || Constants.MANUAL_REQUESTED != payment.manualPayState
        ) {
            AjaxResult.error("此订单不能放行HIS充值")
        }

        HisExt.recharge(id)

        return AjaxResult.success()
    }
}
