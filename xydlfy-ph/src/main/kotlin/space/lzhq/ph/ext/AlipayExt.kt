package space.lzhq.ph.ext

import com.alipay.api.response.AlipayTradeRefundApplyResponse
import com.ruoyi.common.utils.spring.SpringUtils
import org.dromara.hutool.core.date.DateField
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.http.client.HttpDownloader
import org.mospital.alipay.AlipayService
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MipAlipayOrder
import space.lzhq.ph.service.IMipAlipayOrderService
import java.io.File
import java.time.LocalDate
import java.time.format.DateTimeFormatter

object AlipayExt {

    fun refundAlipay(mipAlipayOrder: MipAlipayOrder): AlipayTradeRefundApplyResponse {
        check(mipAlipayOrder.alipayRefundStatus.isNullOrBlank() || mipAlipayOrder.alipayRefundStatus == "REFUND_FAIL") { "订单已退款" }
        check(!mipAlipayOrder.tradeNo.isNullOrBlank()) { "订单未支付" }
        val refundResponse: AlipayTradeRefundApplyResponse = AlipayService.refundMip(
            refundAmount = mipAlipayOrder.feeSumAmount,
            tradeNo = mipAlipayOrder.tradeNo,
            outTradeNo = mipAlipayOrder.outTradeNo,
            outRequestNo = PaymentKit.newOrderId(ServiceType.YB),
            cancelSerialNo = mipAlipayOrder.outTradeNo,
            cancelBillNo = mipAlipayOrder.outTradeNo
        )
        if (refundResponse.isSuccess) {
            mipAlipayOrder.alipayOutRequestNo = refundResponse.outRequestNo
            mipAlipayOrder.alipayRefundStatus = refundResponse.refundStatus
        } else {
            mipAlipayOrder.alipayOutRequestNo = refundResponse.outRequestNo
            mipAlipayOrder.alipayRefundStatus = "FAIL"
        }

        val mipAlipayOrderService: IMipAlipayOrderService = SpringUtils.getBean(IMipAlipayOrderService::class.java)
        mipAlipayOrderService.updateById(mipAlipayOrder)

        return refundResponse
    }

    fun getOrDownloadBillFile(billDate: LocalDate): File {
        val path: String = AlipayService.buildBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        val billDownloadUrlResponse = AlipayService.queryBillDownloadUrl(billDate)
        return if (billDownloadUrlResponse.isSuccess) {
            HttpDownloader.downloadFile(billDownloadUrlResponse.billDownloadUrl, file)
        } else if (billDownloadUrlResponse.subCode == "isp.bill_not_exist") {
            throw RuntimeException("${billDate.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))}未发生交易")
        } else {
            throw RuntimeException("下载账单失败：${billDownloadUrlResponse.subCode}-${billDownloadUrlResponse.subMsg}")
        }
    }

    /**
     * 计算指定时间可发送第一次消息（预约成功）的就诊时间范围
     */
    fun calculatePreorderSuccessJiuzhenTimeRange(time: DateTime = DateTime.now()): Pair<DateTime, DateTime> {
        val minute = if (time.minute() >= 30) {
            30
        } else {
            0
        }
        val normalizedTime = time.setField(DateField.MINUTE, minute)
        val minJiuzhenTime = normalizedTime.offset(DateField.HOUR, 3)
        val maxJiuzhenTime = minJiuzhenTime.offset(DateField.MINUTE, 30)
        return minJiuzhenTime to maxJiuzhenTime
    }

    /**
     * 计算指定时间可发送第二次消息（待签到）的就诊时间范围
     */
    fun calculateWaitCheckInJiuzhenTimeRange(time: DateTime = DateTime.now()): Pair<DateTime, DateTime> {
        val minute = if (time.minute() >= 30) {
            30
        } else {
            0
        }
        val minJiuzhenTime = time.setField(DateField.MINUTE, minute)
        val maxJiuzhenTime = minJiuzhenTime.offset(DateField.MINUTE, 30)
        return minJiuzhenTime to maxJiuzhenTime
    }

}
