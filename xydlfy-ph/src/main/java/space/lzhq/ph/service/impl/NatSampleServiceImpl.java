package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.dromara.hutool.core.date.DatePattern;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.map.Dict;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.NatSample;
import space.lzhq.ph.domain.NatSampleSummary;
import space.lzhq.ph.mapper.NatSampleMapper;
import space.lzhq.ph.service.INatPersonService;
import space.lzhq.ph.service.INatSampleService;
import space.lzhq.ph.service.INatSampleSummaryService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 样品Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-12
 */
@Service
public class NatSampleServiceImpl implements INatSampleService {

    private final NatSampleMapper natSampleMapper;
    private final INatPersonService natPersonService;
    private final INatSampleSummaryService natSampleSummaryService;

    public NatSampleServiceImpl(
            NatSampleMapper natSampleMapper,
            INatPersonService natPersonService,
            INatSampleSummaryService natSampleSummaryService
    ) {
        this.natSampleMapper = natSampleMapper;
        this.natPersonService = natPersonService;
        this.natSampleSummaryService = natSampleSummaryService;
    }

    /**
     * 查询样品
     *
     * @param id 样品ID
     * @return 样品
     */
    @Override
    public NatSample selectNatSampleById(Long id) {
        return natSampleMapper.selectNatSampleById(id);
    }

    /**
     * 查询样品列表
     *
     * @param natSample 样品
     * @return 样品
     */
    @Override
    public List<NatSample> selectNatSampleList(NatSample natSample) {
        return natSampleMapper.selectNatSampleList(natSample);
    }

    @Override
    public Long countCodes(NatSample natSample) {
        return natSampleMapper.countCodes(natSample);
    }

    @Override
    public List<NatSample> selectListByCode(String code) {
        NatSample natSample = new NatSample();
        natSample.setCode(code);
        return selectNatSampleList(natSample);
    }

    @Override
    public List<NatSample> selectListByIdCardNo(String idCardNo) {
        NatSample natSample = new NatSample();
        natSample.setPersonIdCardNo(idCardNo);
        return selectNatSampleList(natSample);
    }

    @Override
    public List<NatSample> selectListByPersonIdAndCollectDate(Long personId, Date collectDate) {
        NatSample natSample = new NatSample();
        natSample.setPersonId(personId);
        Map<String, Object> params = Dict.of()
                .set("beginCollectTime", DateUtil.beginOfDay(collectDate).toString(DatePattern.NORM_DATETIME_FORMAT))
                .set("endCollectTime", DateUtil.endOfDay(collectDate, false).toString(DatePattern.NORM_DATETIME_FORMAT));
        natSample.setParams(params);
        return selectNatSampleList(natSample);
    }

    /**
     * 新增样品
     *
     * @param natSample 样品
     * @return 结果
     */
    @Override
    public int insertNatSample(NatSample natSample) {
        return natSampleMapper.insertNatSample(natSample);
    }

    /**
     * 修改样品
     *
     * @param natSample 样品
     * @return 结果
     */
    @Override
    public int updateNatSample(NatSample natSample) {
        return natSampleMapper.updateNatSample(natSample);
    }

    /**
     * 删除样品对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteNatSampleByIds(String ids) {
        return natSampleMapper.deleteNatSampleByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除样品信息
     *
     * @param id 样品ID
     * @return 结果
     */
    @Override
    public int deleteNatSampleById(Long id) {
        return natSampleMapper.deleteNatSampleById(id);
    }

    @Override
    public void updateSummary(Date collectDate) {
        NatSampleSummary summary = natSampleSummaryService.getOrCreate(collectDate);
        summary.setYingCai(natPersonService.countByMaxCreateTime(collectDate));

        NatSampleSummary tempSummary = natSampleSummaryService.redoSummarize(collectDate);
        summary.setShiCai(tempSummary.getShiCai());
        summary.setWeiCai(summary.getYingCai() - summary.getShiCai());
        summary.setPushOk(tempSummary.getPushOk());
        summary.setPushFail(tempSummary.getPushFail());
        summary.setPushTodo(tempSummary.getPushTodo());
        natSampleSummaryService.updateNatSampleSummary(summary);
    }

    @Override
    public void updateMixType(String code) {
        int count = natSampleMapper.countByCode(code);
        natSampleMapper.updateMixType(code, count);
    }
}
