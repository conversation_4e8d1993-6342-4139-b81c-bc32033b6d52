package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.page.TableDataInfo
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.ui.set
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import space.lzhq.ph.domain.PatientDailyIncrement
import space.lzhq.ph.domain.WxCheck
import space.lzhq.ph.service.IPatientService
import space.lzhq.ph.service.IWxCheckService

@Controller
@RequestMapping("/ph/report")
class ReportAdminController : BaseController() {

    private val prefix = "ph/report"

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    @Autowired
    private lateinit var patientService: IPatientService

    @RequiresPermissions("ph:report:wx_pay_refund")
    @GetMapping("/wx_pay_refund")
    fun payRefundPage(attrs: Model): String {
        val endDate = DateUtil.yesterday()
        val startDate = DateUtil.offsetMonth(endDate, -1)
        attrs["minId"] = startDate.toString(DatePattern.PURE_DATE_FORMAT)
        attrs["maxId"] = endDate.toString(DatePattern.PURE_DATE_FORMAT)
        return "$prefix/wx_pay_refund"
    }

    @RequiresPermissions("ph:report:wx_pay_refund")
    @PostMapping("/wx_pay_refund")
    @ResponseBody
    fun payRefundData(wxCheck: WxCheck): TableDataInfo {
        val list = wxCheckService.selectWxCheckList(wxCheck).sortedBy { it.id }
        return getDataTable(list)
    }

    @RequiresPermissions("ph:report:patient_daily_increment")
    @GetMapping("/patient_daily_increment")
    fun patientDailyIncrementPage(attrs: Model): String {
        val endDate = DateUtil.yesterday()
        val startDate = DateUtil.offsetMonth(endDate, -1)
        attrs["startDate"] = startDate.toString(DatePattern.NORM_DATE_FORMAT)
        attrs["endDate"] = endDate.toString(DatePattern.NORM_DATE_FORMAT)
        return "$prefix/patient_daily_increment"
    }

    @RequiresPermissions("ph:report:patient_daily_increment")
    @PostMapping("/patient_daily_increment")
    @ResponseBody
    fun patientDailyIncrementData(patientDailyIncrement: PatientDailyIncrement): TableDataInfo {
        val list = patientService.statisticsByDay(patientDailyIncrement)
        return getDataTable(list)
    }

}