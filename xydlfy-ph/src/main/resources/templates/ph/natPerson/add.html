<!DOCTYPE html>
<!--suppress HtmlFormInputWithoutLabel, HtmlRequiredTitleElement, JSCheckFunctionSignatures -->
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('新增人员')"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
  <form class="form-horizontal m" id="form-nat_person-add">
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">科室：</label>
      <div class="col-sm-8">
        <select name="department" class="form-control m-b" th:with="type=${@dict.getType('nat_department')}"
                required>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">姓名：</label>
      <div class="col-sm-8">
        <input name="name" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">身份证号：</label>
      <div class="col-sm-8">
        <input name="idCardNo" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">手机号：</label>
      <div class="col-sm-8">
        <input name="mobile" class="form-control" type="text" required>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">民族：</label>
      <div class="col-sm-8">
        <select name="nation" class="form-control m-b" th:with="type=${@dict.getType('minzu')}">
          <option value="">请选择民族</option>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label">类型：</label>
      <div class="col-sm-8">
        <select name="type" class="form-control m-b" th:with="type=${@dict.getType('nat_person_type')}">
          <option value="">请选择类型</option>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label">几天一检：</label>
      <div class="col-sm-8">
        <select name="days" class="form-control m-b" th:with="type=${@dict.getType('nat_frequency')}">
          <option value="">请选择几天一检</option>
          <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label">现住址：</label>
      <div class="col-sm-8">
        <input name="address" class="form-control" type="text">
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-3 control-label is-required">排序号：</label>
      <div class="col-sm-8">
        <input name="sortNo" class="form-control" type="text" required value="9999">
      </div>
    </div>
  </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
  const prefix = ctx + "ph/natPerson"
  $("#form-nat_person-add").validate({
    focusCleanup: true
  });

  function submitHandler() {
    if ($.validate.form()) {
      $.operate.save(prefix + "/add", $('#form-nat_person-add').serialize());
    }
  }
</script>
</body>
</html>