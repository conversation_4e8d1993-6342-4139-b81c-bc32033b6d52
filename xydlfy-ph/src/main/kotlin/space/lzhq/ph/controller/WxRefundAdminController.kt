package space.lzhq.ph.controller

import com.github.binarywang.wxpay.service.WxPayService
import com.ruoyi.common.annotation.Log
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.WxRefund
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxRefundService

/**
 * 退款记录Controller
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Suppress("DuplicatedCode", "SpringMVCViewInspection")
@Controller
@RequestMapping("/ph/refund")
class WxRefundAdminController : BaseController() {
    private val prefix = "ph/refund"

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var wxPayService: WxPayService

    @RequiresPermissions("ph:refund:view")
    @GetMapping
    fun refund(): String {
        return "$prefix/refund"
    }

    /**
     * 查询退款记录列表
     */
    @RequiresPermissions("ph:refund:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxRefund: WxRefund): TableDataInfo {
        startPage()
        val list = wxRefundService.selectWxRefundList(wxRefund)
        return getDataTable(list)
    }

    /**
     * 导出退款记录列表
     */
    @RequiresPermissions("ph:refund:export")
    @Log(title = "退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxRefund: WxRefund): AjaxResult {
        val list = wxRefundService.selectWxRefundList(wxRefund)
        val util = ExcelUtil(WxRefund::class.java)
        return util.exportExcel(list, "refund")
    }

    /**
     * 查询订单
     */
    @RequiresPermissions("ph:refund:queryWXOrder")
    @GetMapping("/queryWXOrder/{id}")
    @ResponseBody
    fun queryWXOrder(@PathVariable id: Long): AjaxResult {
        return try {
            val wxRefund = wxRefundService.selectWxRefundById(id)!!
            val wxPayRefundQueryResult = wxPayService.refundQuery(null, null, wxRefund.zyRefundNo, null)
            if (wxPayRefundQueryResult.refundCount > 0) {
                wxRefundService.updateOnWXRefund(wxRefund, wxPayRefundQueryResult.refundRecords[0])
            }
            AjaxResult.success(wxPayRefundQueryResult)
        } catch (e: Exception) {
            AjaxResult.success(e.message)
        }
    }

    /**
     * 申请微信退款
     */
    @RequiresPermissions("ph:refund:requestWXRefund")
    @PostMapping("/requestWXRefund/{id}")
    @ResponseBody
    fun requestWXRefund(@PathVariable id: Long): AjaxResult {
        val wxRefund = wxRefundService.selectWxRefundById(id)!!
        if (Constants.REFUND_OK != wxRefund.hisTradeStatus
            || Constants.REFUND_OK == wxRefund.wxTradeStatus
            || Constants.MANUAL_INIT != wxRefund.manualRefundState
        ) {
            return AjaxResult.error("此订单不能申请微信退款")
        }
        wxRefund.manualRefundState = Constants.MANUAL_REQUESTED
        val ok = wxRefundService.updateWxRefund(wxRefund) == 1
        return if (ok) AjaxResult.success() else AjaxResult.error()
    }

    /**
     * 放行微信退款
     */
    @RequiresPermissions("ph:refund:approveWXRefund")
    @PostMapping("/approveWXRefund/{id}")
    @ResponseBody
    fun approveWXRefund(@PathVariable id: Long): AjaxResult {
        val wxRefund = wxRefundService.selectWxRefundById(id)!!
        if (Constants.REFUND_OK != wxRefund.hisTradeStatus
            || Constants.REFUND_OK == wxRefund.wxTradeStatus
            || Constants.MANUAL_REQUESTED != wxRefund.manualRefundState
        ) {
            return AjaxResult.error("此订单不能放行微信退款")
        }

        wxRefund.manualRefundState = Constants.MANUAL_APPROVED
        wxRefundService.updateWxRefund(wxRefund)

        try {
            val refundResult = WeixinExt.refund(
                totalAmount = wxRefund.totalAmount.toInt(),
                amount = wxRefund.amount.toInt(),
                wxPayNo = wxRefund.wxPayNo,
                zyRefundNo = wxRefund.zyRefundNo
            )
            return AjaxResult.success(refundResult)
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error(e.message)
        }
    }
}
