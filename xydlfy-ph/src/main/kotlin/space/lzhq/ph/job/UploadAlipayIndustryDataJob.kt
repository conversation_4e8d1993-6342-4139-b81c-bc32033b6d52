package space.lzhq.ph.job

import com.alipay.api.domain.DepartmentData
import com.alipay.api.domain.HospitalData
import org.dromara.hutool.core.net.url.UrlEncoder
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.Department
import space.lzhq.ph.service.IDepartmentService

/**
 * 向支付宝上传医疗行业数据
 * 表达式：0 0 4 * * ?
 */
@Component("uploadAlipayIndustryDataJob")
class UploadAlipayIndustryDataJob {

    private val logger: Logger = LoggerFactory.getLogger(UploadAlipayIndustryDataJob::class.java)

    @Autowired
    private lateinit var departmentService: IDepartmentService

    fun execute() {
        try {
            uploadHospitalData()
        } catch (e: Exception) {
            logger.warn(e.message, e)
        }

        try {
            uploadDepartmentsData()
        } catch (e: Exception) {
            logger.warn(e.message, e)
        }
    }

    private fun uploadHospitalData() {
        val hosptialData: HospitalData = HospitalData().also {
            it.hospitalId = "1"
            it.hospitalName = AlipaySetting.maHospitalName
            it.hospitalAlias = "伊犁友谊医院"
            it.hospitalStandardCode = "H65400200247"
            it.hospitalProvince = "新疆维吾尔自治区"
            it.hospitalCity = "伊犁哈萨克自治州"
            it.hospitalDistrict = "伊宁市"
            it.hospitalAddr = "新疆维吾尔自治区伊犁哈萨克自治州伊宁市斯大林路"
            it.hospitalLat = "43.918918"
            it.hospitalLgt = "81.319155"
            it.hospitalType = "公立"
            it.hospitalGrade = "三级"
            it.hospitalWorkTime = ""
            it.hospitalTel = ""
            it.hospitalLogo = "https://appstoreisvpic.alipayobjects.com/prod/7579d30d-48ee-4749-9fc7-8ac8827068e9.png"
            it.countryKeyDepartment = ""
            it.provinceKeyDepartment = ""
            it.keyDepartment = ""
            it.hospitalOptag = ""
        }
        AlipayService.uploadHospitalMedicalIndustryData(listOf(hosptialData))
    }

    private fun uploadDepartmentsData() {
        val departments: List<Department> = departmentService.selectDepartmentAll()

        val hospitalName: String = AlipaySetting.maHospitalName
        val appId: String = AlipaySetting.maAppId
        val departmentDataList: List<DepartmentData> = departments.map { dept ->
            DepartmentData().also { departmentData ->
                departmentData.departmentId = dept.id
                departmentData.departmentName = dept.name
                departmentData.departmentType = "一级科室"
                departmentData.hospitalName = hospitalName

                val page = "pages_yuyue/PaibanYisheng/PaibanYisheng"
                val query: String =
                    UrlEncoder.encodeAll("departmentId=${dept.id}&departmentName=${dept.name}")
                departmentData.departmentUrl = "alipays://platformapi/startapp?appId=$appId&page=$page&query=$query"
            }
        }
        AlipayService.uploadDepartmentMedicalIndustryData(departmentDataList)
    }

}
