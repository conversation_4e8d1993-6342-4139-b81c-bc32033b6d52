---
description:
globs:
alwaysApply: true
---

# Cursor Rules

这是一个基于若依框架的医疗信息系统项目，包含公共卫生管理、支付对账、在线预约等功能模块。

## 项目总体架构

### 核心模块说明

- **ruoyi-admin**: 主应用程序模块，包含启动类和全局配置
- **xydlfy-ph**: 核心业务模块，包含公共卫生管理相关功能
- **ruoyi-framework**: 框架配置模块，包含Spring、Shiro、数据源等配置
- **ruoyi-system**: 系统基础模块，包含用户、权限、配置管理
- **ruoyi-common**: 公共工具模块，包含通用工具类和配置
- **ruoyi-generator**: 代码生成器模块
- **ruoyi-quartz**: 定时任务模块
- **ph-common**: 公共卫生业务公共模块
- **ph-pay-ccb**: 建行支付模块

## 重要文件和类的作用

### 主启动类

- `ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java`:
    - Spring Boot主启动类
    - 扫描包: `com.ruoyi`, `space.lzhq.ph`
    - MyBatis Mapper扫描: `com.ruoyi.*.mapper`, `space.lzhq.ph.mapper`

### 全局异常处理器

- `ruoyi-admin/src/main/java/com/ruoyi/web/exception/GlobalExceptionHandler.java`:
    - 全局异常处理器，使用@RestControllerAdvice注解
    - 处理权限校验、参数验证、业务异常等
    - 提供追踪ID功能，便于问题定位
    - 支持Ajax请求和页面请求的差异化处理

### 核心配置类

#### 数据源配置

- `ruoyi-framework/src/main/java/com/ruoyi/framework/config/DruidConfig.java`:
    - Druid数据源配置
    - 支持主从数据库配置
    - 支持LIS系统数据源

#### MyBatis-Plus配置

- `ruoyi-framework/src/main/java/com/ruoyi/framework/config/MybatisPlusConfig.java`:
    - MyBatis-Plus配置
    - 分页插件配置
    - 乐观锁插件配置
    - 防止全表删除更新插件

#### 安全配置

- `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java`:
    - Apache Shiro安全框架配置
    - 权限认证、会话管理、缓存配置
    - Cookie和RememberMe配置

#### 应用配置

- `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ApplicationConfig.java`:
    - 程序注解配置
    - AOP代理配置
    - Mapper扫描配置

#### 验证码配置

- `ruoyi-framework/src/main/java/com/ruoyi/framework/config/CaptchaConfig.java`:
    - Kaptcha验证码配置
    - 验证码样式和参数设置

#### 线程池配置

- `ruoyi-common/src/main/java/com/ruoyi/common/config/thread/ThreadPoolConfig.java`:
    - 线程池配置
    - 异步任务执行器配置

### 业务模块结构 (xydlfy-ph)

#### 控制器层

- `xydlfy-ph/src/main/java/space/lzhq/ph/controller/`:
    - REST API控制器
    - 处理HTTP请求和响应

#### 服务层

- `xydlfy-ph/src/main/java/space/lzhq/ph/service/`:
    - 业务逻辑服务类
    - 事务管理

#### 数据访问层

- `xydlfy-ph/src/main/java/space/lzhq/ph/mapper/`:
    - MyBatis Mapper接口
    - 数据库访问操作

#### 领域模型

- `xydlfy-ph/src/main/java/space/lzhq/ph/domain/`:
    - 实体类和领域对象

#### 数据传输对象

- `xydlfy-ph/src/main/java/space/lzhq/ph/dto/`:
    - 数据传输对象(DTO)
    - API请求响应对象

#### 异常处理

- `xydlfy-ph/src/main/java/space/lzhq/ph/exception/`:
    - 自定义业务异常类
    - 如AttachmentValidationException附件验证异常

#### 工具类

- `xydlfy-ph/src/main/java/space/lzhq/ph/util/`:
- `xydlfy-ph/src/main/java/space/lzhq/ph/utils/`:
    - 业务相关工具类

#### Web服务

- `xydlfy-ph/src/main/java/space/lzhq/ph/webservice/`:
    - SOAP Web服务实现
    - 与第三方系统集成

#### 对账模块

- `xydlfy-ph/src/main/java/space/lzhq/ph/reconciliation/`:
    - 支付对账相关功能
    - 包含匹配规则配置加载器

#### 定时任务

- `xydlfy-ph/src/main/java/space/lzhq/ph/job/`:
    - 定时任务实现

#### 邮件服务

- `xydlfy-ph/src/main/java/space/lzhq/ph/mail/`:
    - 邮件发送服务

### 配置文件

#### 主配置文件

- `ruoyi-admin/src/main/resources/application.yml`:
    - Spring Boot主配置文件
    - 包含数据源、日志、Shiro、文件上传等配置

#### MyBatis配置

- `ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml`:
    - MyBatis核心配置
    - 缓存、执行器、日志配置

#### 对账规则配置

- `xydlfy-ph/src/main/resources/`:
    - 包含各种支付对账匹配规则配置文件
    - 如微信、支付宝、建行POS等对账规则

## 技术栈

- **框架**: Spring Boot 3.4.5, Spring Framework
- **安全**: Apache Shiro 2.0.1
- **ORM**: MyBatis-Plus 3.5.8
- **数据库连接池**: Druid 1.2.23
- **模板引擎**: Thymeleaf
- **编程语言**: Java 21 + Kotlin 2.1.20
- **构建工具**: Maven
- **数据库**: MySQL (主要), Oracle (LIS系统)

## 开发约定

### 包命名规范

- 若依框架相关: `com.ruoyi.**`
- 业务功能相关: `space.lzhq.ph.**`

### 异常处理约定

- 全局异常由GlobalExceptionHandler统一处理
- 业务异常使用ServiceException
- 参数验证异常自动捕获处理
- 所有异常都包含追踪ID便于问题定位

### 数据访问约定

- 使用MyBatis-Plus作为ORM框架
- Repository层使用Mapper命名
- 支持多数据源(主库、从库、LIS库)

### 安全约定

- 使用Apache Shiro进行权限控制
- 支持会话管理和RememberMe功能
- 提供验证码防护

### 配置管理约定

- 外部化配置使用application.yml
- 敏感配置使用@ConfigurationProperties绑定
- 支持多环境配置(开发、生产等)

### Controller层集成测试约定

- **目录位置**: 所有Controller集成测试必须放在 `ruoyi-admin/src/test/java/space/lzhq/ph/controller/`
- **命名规范**:
    - 业务接口测试: `{业务模块}ApiControllerTest.java`
    - 专用测试控制器: `{功能}TestController.java`
    - 行为测试类: `{功能}BehaviorTest.java`
- **测试架构**: 采用专用测试端点模式，实现验证逻辑与业务逻辑测试分离
- **基类继承**: 所有Controller测试必须继承 `BaseIntegrationTest`
- **注解使用**: 使用 `@DisplayName` 提供中文测试描述

## 注意事项

1. 项目使用Java 21和Kotlin混合开发
2. 数据库采用多数据源架构
3. 对账功能通过YAML配置文件定义匹配规则
4. Web服务使用Apache CXF框架
5. 前端采用若依管理系统框架
6. 支持定时任务和异步处理
7. 集成微信小程序相关功能
8. **Controller集成测试统一管理**: 所有Controller层的集成测试都在`ruoyi-admin`模块中统一管理，不要在业务模块中创建Controller测试 