ruoyi:
  profile: D:/zyxcx/assets

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: ******************************************************************************************************************************************************************
        driverClassName: com.mysql.cj.jdbc.Driver
        username: root
        password: vgkQn17tb3X2wCEA
      lis:
        enabled: true
        url: *******************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: XHLIS
        password: XHLIS
      pacs:
        enabled: true
        url: *****************************************
        driverClassName: oracle.jdbc.OracleDriver
        username: xcxpacs
        password: xcxpacs
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 3000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      keep-alive: true
      pool-prepared-statements: true

server:
  httpPort: 9900
  port: 9999
