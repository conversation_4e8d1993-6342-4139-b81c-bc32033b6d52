package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.NatSampleSummary;
import space.lzhq.ph.service.INatSampleSummaryService;

import java.util.List;

/**
 * 采样日报Controller
 *
 * <AUTHOR>
 * @date 2021-12-13
 */
@Controller
@RequestMapping("/ph/natSampleSummary")
public class NatSampleSummaryAdminController extends BaseController {
    private final String prefix = "ph/natSampleSummary";

    private final INatSampleSummaryService natSampleSummaryService;

    public NatSampleSummaryAdminController(INatSampleSummaryService natSampleSummaryService) {
        this.natSampleSummaryService = natSampleSummaryService;
    }

    @RequiresPermissions("ph:natSampleSummary:view")
    @GetMapping()
    public String natSampleSummary() {
        return prefix + "/natSampleSummary";
    }

    /**
     * 查询采样日报列表
     */
    @RequiresPermissions("ph:natSampleSummary:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NatSampleSummary natSampleSummary) {
        startPage("id desc");
        List<NatSampleSummary> list = natSampleSummaryService.selectNatSampleSummaryList(natSampleSummary);
        return getDataTable(list);
    }

    /**
     * 导出采样日报列表
     */
    @RequiresPermissions("ph:natSampleSummary:export")
    @Log(title = "采样日报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(NatSampleSummary natSampleSummary) {
        List<NatSampleSummary> list = natSampleSummaryService.selectNatSampleSummaryList(natSampleSummary);
        ExcelUtil<NatSampleSummary> util = new ExcelUtil<NatSampleSummary>(NatSampleSummary.class);
        return util.exportExcel(list, "natSampleSummary");
    }

    /**
     * 新增采样日报
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存采样日报
     */
    @RequiresPermissions("ph:natSampleSummary:add")
    @Log(title = "采样日报", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(NatSampleSummary natSampleSummary) {
        return toAjax(natSampleSummaryService.insertNatSampleSummary(natSampleSummary));
    }

    /**
     * 修改采样日报
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        NatSampleSummary natSampleSummary = natSampleSummaryService.selectNatSampleSummaryById(id);
        mmap.put("natSampleSummary", natSampleSummary);
        return prefix + "/edit";
    }

    /**
     * 修改保存采样日报
     */
    @RequiresPermissions("ph:natSampleSummary:edit")
    @Log(title = "采样日报", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(NatSampleSummary natSampleSummary) {
        return toAjax(natSampleSummaryService.updateNatSampleSummary(natSampleSummary));
    }

    /**
     * 删除采样日报
     */
    @RequiresPermissions("ph:natSampleSummary:remove")
    @Log(title = "采样日报", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(natSampleSummaryService.deleteNatSampleSummaryByIds(ids));
    }
}
