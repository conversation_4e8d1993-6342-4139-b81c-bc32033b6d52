<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.JubaoCaseMapper">

    <resultMap type="JubaoCase" id="JubaoCaseResult">
        <result property="id" column="id"/>
        <result property="clientType" column="client_type"/>
        <result property="openId" column="open_id"/>
        <result property="reporterType" column="reporter_type"/>
        <result property="reporterRoleType" column="reporter_role_type"/>
        <result property="reporterName" column="reporter_name"/>
        <result property="reporterIdCardNo" column="reporter_id_card_no"/>
        <result property="reporterMobile" column="reporter_mobile"/>
        <result property="reporterAddress" column="reporter_address"/>
        <result property="reporterPoliticalStatus" column="reporter_political_status"/>
        <result property="reporterLevel" column="reporter_level"/>
        <result property="title" column="title"/>
        <result property="category1" column="category1"/>
        <result property="category2" column="category2"/>
        <result property="content" column="content"/>
        <result property="place" column="place"/>
        <result property="attachments" column="attachments"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="acceptTime" column="accept_time"/>
        <result property="finishTime" column="finish_time"/>
    </resultMap>

    <sql id="selectJubaoCaseVo">
        select id, client_type, open_id, reporter_type, reporter_role_type, reporter_name, reporter_id_card_no,
        reporter_mobile,
        reporter_address, reporter_political_status, reporter_level,
        title, category1, category2, content, place, attachments, status, create_time,
        update_time, accept_time, finish_time from jb_case
    </sql>

    <select id="selectJubaoCaseList" parameterType="JubaoCase" resultMap="JubaoCaseResult">
        <include refid="selectJubaoCaseVo"/>
        <where>
            <if test="clientType != null ">and client_type = #{clientType}</if>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="reporterType != null ">and reporter_type = #{reporterType}</if>
            <if test="reporterRoleType != null ">and reporter_role_type = #{reporterRoleType}</if>
            <if test="reporterName != null  and reporterName != ''">and reporter_name like concat('%', #{reporterName},
                '%')
            </if>
            <if test="reporterIdCardNo != null  and reporterIdCardNo != ''">and reporter_id_card_no =
                #{reporterIdCardNo}
            </if>
            <if test="reporterMobile != null  and reporterMobile != ''">and reporter_mobile = #{reporterMobile}</if>
            <if test="reporterPoliticalStatus != null ">and reporter_political_status = #{reporterPoliticalStatus}</if>
            <if test="reporterLevel != null  and reporterLevel != ''">and reporter_level = #{reporterLevel}</if>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="category1 != null ">and category1 = #{category1}</if>
            <if test="category2 != null ">and category2 = #{category2}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectListByOpenId" parameterType="String" resultMap="JubaoCaseResult">
        <include refid="selectJubaoCaseVo"/>
        where open_id = #{openId}
    </select>

    <select id="selectJubaoCaseById" parameterType="Long" resultMap="JubaoCaseResult">
        <include refid="selectJubaoCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertJubaoCase" parameterType="JubaoCase" useGeneratedKeys="true" keyProperty="id">
        insert into jb_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientType != null">client_type,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="reporterType != null">reporter_type,</if>
            <if test="reporterRoleType != null">reporter_role_type,</if>
            <if test="reporterName != null">reporter_name,</if>
            <if test="reporterIdCardNo != null">reporter_id_card_no,</if>
            <if test="reporterMobile != null">reporter_mobile,</if>
            <if test="reporterAddress != null">reporter_address,</if>
            <if test="reporterPoliticalStatus != null">reporter_political_status,</if>
            <if test="reporterLevel != null">reporter_level,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="category1 != null">category1,</if>
            <if test="category2 != null">category2,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="place != null and place != ''">place,</if>
            <if test="attachments != null and attachments != ''">attachments,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="acceptTime != null">accept_time,</if>
            <if test="finishTime != null">finish_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientType != null">#{clientType},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="reporterType != null">#{reporterType},</if>
            <if test="reporterRoleType != null">#{reporterRoleType},</if>
            <if test="reporterName != null">#{reporterName},</if>
            <if test="reporterIdCardNo != null">#{reporterIdCardNo},</if>
            <if test="reporterMobile != null">#{reporterMobile},</if>
            <if test="reporterAddress != null">#{reporterAddress},</if>
            <if test="reporterPoliticalStatus != null">#{reporterPoliticalStatus},</if>
            <if test="reporterLevel != null">#{reporterLevel},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="category1 != null">#{category1},</if>
            <if test="category2 != null">#{category2},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="place != null and place != ''">#{place},</if>
            <if test="attachments != null and attachments != ''">#{attachments},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="acceptTime != null">#{acceptTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
        </trim>
    </insert>

    <update id="updateJubaoCase" parameterType="JubaoCase">
        update jb_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientType != null">client_type = #{clientType},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="reporterType != null">reporter_type = #{reporterType},</if>
            <if test="reporterRoleType != null">reporter_role_type = #{reporterRoleType},</if>
            <if test="reporterName != null">reporter_name = #{reporterName},</if>
            <if test="reporterIdCardNo != null">reporter_id_card_no = #{reporterIdCardNo},</if>
            <if test="reporterMobile != null">reporter_mobile = #{reporterMobile},</if>
            <if test="reporterAddress != null">reporter_address = #{reporterAddress},</if>
            <if test="reporterPoliticalStatus != null">reporter_political_status = #{reporterPoliticalStatus},</if>
            <if test="reporterLevel != null">reporter_level = #{reporterLevel},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="category1 != null">category1 = #{category1},</if>
            <if test="category2 != null">category2 = #{category2},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="place != null and place != ''">place = #{place},</if>
            <if test="attachments != null and attachments != ''">attachments = #{attachments},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="acceptTime != null">accept_time = #{acceptTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJubaoCaseById" parameterType="Long">
        delete from jb_case where id = #{id}
    </delete>

    <delete id="deleteJubaoCaseByIds" parameterType="String">
        delete from jb_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="acceptJubaoCaseByIds" parameterType="String">
        update jb_case set status = '1', accept_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="finishJubaoCaseByIds" parameterType="String">
        update jb_case set status = '2', finish_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>