package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import space.lzhq.ph.domain.Drug;
import space.lzhq.ph.mapper.DrugMapper;
import space.lzhq.ph.service.IDrugService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DrugServiceImpl extends ServiceImpl<DrugMapper, Drug> implements IDrugService {

    /**
     * 药品关键字的正则表达式
     * [\u4e00-\u9fa5] 匹配汉字
     * [a-zA-Z] 匹配英文字母
     * [0-9] 匹配数字
     * [ ] 匹配空格
     */
    private static final Pattern VALID_KEYWORD_PATTERN = Pattern.compile("^[\u4e00-\u9fa5a-zA-Z0-9 ]+$");

    /**
     * 获取当前代理对象，用于调用事务方法
     */
    private IDrugService getSelf() {
        return (IDrugService) AopContext.currentProxy();
    }

    @Override
    public List<Drug> listByKeyword(String keyword) {
        if (!validateDrugKeyword(keyword)) {
            throw new IllegalArgumentException("查询关键字只能包含汉字、英文字母、数字和空格");
        }

        LambdaQueryChainWrapper<Drug> queryWrapper = lambdaQuery();
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like(Drug::getKeyword, keyword);
        }
        return queryWrapper.list();
    }

    /**
     * 清空药品表并重新填充数据
     * 此操作会先清空表中所有数据，然后批量插入新的药品数据
     * 整个操作在事务中执行，确保数据一致性
     *
     * @param drugs 要填充的药品列表
     * @return 成功插入的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int replaceAllDrugs(List<Drug> drugs) {
        log.info("开始替换药品数据，输入记录数: {}", drugs != null ? drugs.size() : 0);

        // 清空药品表
        getBaseMapper().truncateTable();
        log.info("已清空药品表");

        // 如果药品列表为空，直接返回0
        if (drugs == null || drugs.isEmpty()) {
            log.info("药品列表为空，替换完成");
            return 0;
        }

        // 对药品列表进行去重处理
        List<Drug> uniqueDrugs = deduplicateDrugs(drugs);

        // 如果去重后没有有效数据，直接返回0
        if (uniqueDrugs.isEmpty()) {
            log.warn("去重后没有有效的药品数据");
            return 0;
        }

        // 批量插入新的药品数据
        boolean result = getSelf().saveBatch(uniqueDrugs);

        if (result) {
            log.info("药品数据替换成功，插入记录数: {}", uniqueDrugs.size());
            return uniqueDrugs.size();
        } else {
            log.error("药品数据插入失败");
            return 0;
        }
    }

    /**
     * 对药品列表进行去重处理
     * 去重规则：
     * 1. 过滤null对象
     * 2. 过滤ID为null的记录
     * 3. 根据ID去重，如存在重复ID则保留最后一个记录
     *
     * @param drugs 原始药品列表
     * @return 去重后的药品列表
     */
    private List<Drug> deduplicateDrugs(List<Drug> drugs) {
        if (drugs == null || drugs.isEmpty()) {
            return List.of();
        }

        int originalSize = drugs.size();
        log.debug("开始对药品列表进行去重处理，原始记录数: {}", originalSize);

        // 根据ID去重，保留最后一个（或可以根据业务需求调整去重策略）
        List<Drug> uniqueDrugs = new ArrayList<>(drugs.stream()
                .filter(Objects::nonNull) // 过滤null对象
                .filter(drug -> drug.getId() != null) // 过滤ID为null的记录
                .collect(Collectors.toMap(
                        Drug::getId,
                        Function.identity(),
                        (existing, replacement) -> {
                            log.debug("发现重复ID: {}, 保留最后一个记录", existing.getId());
                            return replacement; // 保留最后一个
                        }))
                .values());

        int uniqueSize = uniqueDrugs.size();
        int nullObjectCount = (int) drugs.stream().filter(Objects::isNull).count();
        int nullIdCount = (int) drugs.stream()
                .filter(Objects::nonNull)
                .filter(drug -> drug.getId() == null)
                .count();
        int duplicateCount = originalSize - uniqueSize - nullObjectCount - nullIdCount;

        // 记录去重统计信息
        if (originalSize != uniqueSize) {
            log.warn("去重处理完成 - 原始记录数: {}, 有效记录数: {}, null对象: {}, null ID: {}, 重复记录: {}",
                    originalSize, uniqueSize, nullObjectCount, nullIdCount, duplicateCount);
        } else {
            log.debug("去重处理完成 - 无需去重，有效记录数: {}", uniqueSize);
        }

        return uniqueDrugs;
    }

    /**
     * 校验药品的keyword字段
     * 校验规则：
     * 1. 允许为null
     * 2. 允许为空字符串
     * 3. 如果不是null或空字符串，必须只包含汉字、英文字母、数字和空格
     *
     * @param keyword 药品关键字
     * @return 校验是否通过
     */
    private boolean validateDrugKeyword(String keyword) {
        // 允许null或空字符串
        if (keyword == null || keyword.isEmpty()) {
            return true;
        }

        // 检查是否只包含汉字、英文字母、数字和空格
        return VALID_KEYWORD_PATTERN.matcher(keyword).matches();
    }

}
