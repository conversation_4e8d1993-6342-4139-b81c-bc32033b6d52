package space.lzhq.ph.util;

import com.ruoyi.common.utils.StringUtils;
import org.dromara.hutool.core.data.id.IdUtil;
import org.slf4j.MDC;

/**
 * TraceId工具类
 * 用于管理日志追踪ID，方便日志检索和问题排查
 *
 * <AUTHOR>
 */
public class TraceIdUtil {

    /**
     * TraceId在MDC中的键名
     */
    private static final String TRACE_ID_KEY = "traceId";

    /**
     * 获取当前线程的TraceId
     *
     * @return 当前TraceId，如果不存在则返回null
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    /**
     * 设置当前线程的TraceId
     *
     * @param traceId 要设置的TraceId
     */
    public static void setTraceId(String traceId) {
        if (StringUtils.isNotBlank(traceId)) {
            MDC.put(TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 生成并设置新的TraceId
     *
     * @return 生成的TraceId
     */
    public static String generateAndSetTraceId() {
        String traceId = IdUtil.fastSimpleUUID();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 确保当前线程有TraceId，如果没有则生成一个
     *
     * @return 如果是新生成的TraceId则返回该ID，否则返回null
     */
    public static String ensureTraceId() {
        String existingTraceId = getTraceId();
        if (StringUtils.isBlank(existingTraceId)) {
            return generateAndSetTraceId();
        }
        return null;
    }

    /**
     * 清理当前线程的TraceId
     */
    public static void clearTraceId() {
        MDC.remove(TRACE_ID_KEY);
    }

    /**
     * 如果TraceId是新创建的，则清理它
     *
     * @param createdTraceId 如果不为null，表示是新创建的TraceId，需要清理
     */
    public static void clearTraceIdIfCreated(String createdTraceId) {
        if (createdTraceId != null) {
            clearTraceId();
        }
    }

    /**
     * 执行带TraceId的操作
     * 如果当前没有TraceId，会自动生成一个，操作完成后自动清理
     *
     * @param operation 要执行的操作
     */
    public static void executeWithTraceId(Runnable operation) {
        String createdTraceId = ensureTraceId();
        try {
            operation.run();
        } finally {
            clearTraceIdIfCreated(createdTraceId);
        }
    }

    /**
     * 执行带TraceId的操作并返回结果
     * 如果当前没有TraceId，会自动生成一个，操作完成后自动清理
     *
     * @param operation 要执行的操作
     * @param <T>       返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithTraceId(java.util.function.Supplier<T> operation) {
        String createdTraceId = ensureTraceId();
        try {
            return operation.get();
        } finally {
            clearTraceIdIfCreated(createdTraceId);
        }
    }

    /**
     * 使用指定的TraceId执行操作
     *
     * @param traceId   指定的TraceId
     * @param operation 要执行的操作
     */
    public static void executeWithSpecificTraceId(String traceId, Runnable operation) {
        String originalTraceId = getTraceId();
        try {
            setTraceId(traceId);
            operation.run();
        } finally {
            if (originalTraceId != null) {
                setTraceId(originalTraceId);
            } else {
                clearTraceId();
            }
        }
    }

    /**
     * 使用指定的TraceId执行操作并返回结果
     *
     * @param traceId   指定的TraceId
     * @param operation 要执行的操作
     * @param <T>       返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithSpecificTraceId(String traceId, java.util.function.Supplier<T> operation) {
        String originalTraceId = getTraceId();
        try {
            setTraceId(traceId);
            return operation.get();
        } finally {
            if (originalTraceId != null) {
                setTraceId(originalTraceId);
            } else {
                clearTraceId();
            }
        }
    }
} 