<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('支付宝医保订单列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>用户ID：</label>
                            <input type="text" name="userId"/>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="userName"/>
                        </li>
                        <li>
                            <label>证件号码：</label>
                            <input type="text" name="certNo"/>
                        </li>
                        <li>
                            <label>Token：</label>
                            <input type="text" name="accessToken"/>
                        </li>
                        <li>
                            <label>支付授权码：</label>
                            <input type="text" name="payAuthNo"/>
                        </li>
                        <li>
                            <label>支付订单号：</label>
                            <input type="text" name="payOrderId"/>
                        </li>
                        <li>
                            <label>门诊流水号：</label>
                            <input type="text" name="clinicNo"/>
                        </li>
                        <li>
                            <label>商户订单号：</label>
                            <input type="text" name="outTradeNo"/>
                        </li>
                        <li>
                            <label>支付宝订单号：</label>
                            <input type="text" name="tradeNo"/>
                        </li>
                        <li>
                            <label>HIS发票号：</label>
                            <input type="text" name="hisInvoiceNo"/>
                        </li>
                        <li>
                            <label>医保结算号：</label>
                            <input type="text" name="siSettleId"/>
                        </li>
                        <li>
                            <label>支付宝退款单号：</label>
                            <input type="text" name="alipayOutRequestNo"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="queryTransaction()"
               shiro:hasPermission="ph:mipAlipayOrder:queryTransaction">
                <i class="fa fa-refresh"></i> 查询交易
            </a>
            <a class="btn btn-primary single disabled" onclick="refundAlipay()"
               shiro:hasPermission="ph:mipAlipayOrder:refundAlipay">
                <i class="fa fa-backward"></i> 支付宝退款
            </a>
            <a class="btn btn-primary single disabled" onclick="queryAlipayRefund()"
               shiro:hasPermission="ph:mipAlipayOrder:queryAlipayRefund">
                <i class="fa fa-refresh"></i> 查询支付宝退款
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/mipAlipayOrder";

    $(function () {
        var options = {
            url: prefix + "/list",
            modalName: "支付宝医保订单",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'userId',
                    title: '用户ID'
                },
                {
                    field: 'userName',
                    title: '姓名'
                },
                {
                    field: 'certNo',
                    title: '证件号码'
                },
                {
                    field: 'certType',
                    title: '证件类型'
                },
                {
                    field: 'cityId',
                    title: '参保地城市编码'
                },
                {
                    field: 'payAuthNo',
                    title: '支付授权码'
                },
                {
                    field: 'payOrderId',
                    title: '支付订单号'
                },
                {
                    field: 'clinicNo',
                    title: '门诊流水号'
                },
                {
                    field: 'feeSumAmount',
                    title: '费用总额'
                },
                {
                    field: 'personalAccountPayAmount',
                    title: '个人账户支出'
                },
                {
                    field: 'fundPayAmount',
                    title: '医保基金支出'
                },
                {
                    field: 'ownPayAmount',
                    title: '现金支付金额'
                },
                {
                    field: 'outTradeNo',
                    title: '商户订单号'
                },
                {
                    field: 'tradeNo',
                    title: '支付宝订单号'
                },
                {
                    field: 'tradeStatus',
                    title: '交易状态'
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                {
                    field: 'tradeTime',
                    title: '交易时间'
                },
                {
                    field: 'hisInvoiceNo',
                    title: 'HIS发票号'
                },
                {
                    field: 'hisSettleTime',
                    title: 'HIS结算时间'
                },
                {
                    field: 'siSettleId',
                    title: '医保结算号'
                },
                {
                    field: 'siSettleTime',
                    title: '医保结算时间'
                },
                {
                    field: 'alipayOutRequestNo',
                    title: '支付宝退款单号'
                },
                {
                    field: 'alipayRefundStatus',
                    title: '支付宝退款状态'
                }
            ]
        };
        $.table.init(options);
    });

    function queryTransaction() {
        table.set();
        var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
        $.operate.get(prefix + '/queryTransaction/' + row.id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询交易状态',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function refundAlipay() {
        table.set();
        var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
        $.operate.post(prefix + '/refundAlipay/' + row.id, function (resp) {
            if (resp.code === 0) {
                $.table.search();
                $.modal.alertSuccess('操作成功');
            } else {
                $.modal.alertWarning(resp.msg);
            }
        });
    }

    function queryAlipayRefund() {
        table.set();
        var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
        $.operate.get(prefix + '/queryAlipayRefund/' + row.id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询支付宝退款',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }
</script>
</body>
</html>
