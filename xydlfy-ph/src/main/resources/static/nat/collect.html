<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover">
    <title>伊犁哈萨克自治州友谊医院核酸检测</title>
    <link type="text/css" rel="stylesheet" href="/css/weui.min.css">
    <style>
        body, html {
            height: 100%;
            -webkit-tap-highlight-color: transparent
        }

        body {
            font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif
        }

        ul {
            list-style: none
        }

        .page, body {
            background-color: var(--weui-BG-0)
        }

        .page {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            box-sizing: border-box;
        }

        .required {
            color: var(--weui-RED)
        }

        .weui-cells:after {
            display: none;
        }

        .weui-form__desc {
            color: var(--weui-FG-1);
            font-size: 14px;
        }

        .weui-form__control-area {
            margin: 36px 0;
        }

        .weui-cells__group_form .weui-cell {
            padding: 16px var(--weui-cellPaddingLR) !important;
        }

        #form {
            margin-top: 10px;
            margin-bottom: 10px;
        }

        #form .weui-cell {
            padding: 10px 32px;
        }

        .weui-form-preview__hd .weui-form-preview__value {
            font-family: monospace;
            font-style: normal;
            font-size: 1.2em;
        }

        .weui-toptips_success {
            background-color: var(--weui-BRAND);
        }

        .weui-media-box__desc.log-body {
            display: block;
        }

        .log-body span {
            display: block;
        }

        #tips {
            flex: 1;
            text-align: center;
            margin-bottom: 10px;
        }

        .text-red {
            color: var(--weui-RED);
        }

        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div class="page" id="app" v-cloak>
    <div class="weui-form" id="sampleForm">
        <div class="weui-form__text-area">
            <h3 class="weui-form__title" style="font-size: 18px;">伊犁哈萨克自治州友谊医院核酸检测</h3>
        </div>
        <div class="weui-form__control-area">
            <div class="weui-cells__group weui-cells__group_form">
                <div class="weui-cells__title">样品人信息</div>
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">姓名</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="sample.personName" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">身份证号</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="sample.personIdCardNo" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">手机号</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="sample.personMobile" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">民族</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="personNationLabel" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">人员类型</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="personTypeLabel" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">几天一检</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="personDaysLabel" readonly/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_readonly">
                        <div class="weui-cell__hd">
                            <label class="weui-label">现住址</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" v-model="sample.personAddress" readonly/>
                        </div>
                    </div>
                </div>
                <div class="weui-cells__title" style="margin-top: 20px;">样品信息</div>
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell weui-cell_active">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>混样编码</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" placeholder="请填写混样编码"
                                   v-model="sample.code"
                                   type="tel" @input="onInputSampleCode"
                                   minlength="12" maxlength="12"
                                   required emptyTips="请填写混样编码"/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_access weui-cell_select">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>混样类型</label>
                        </div>
                        <div class="weui-cell__bd" @click="showMixTypePicker">{{ mixTypeLabel }}</div>
                        <input type="hidden" v-model="sample.params.mixType"
                               required emptyTips="请选择混样类型">
                    </div>
                    <div class="weui-cell weui-cell_active weui-cell_access weui-cell_select">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>采样区域</label>
                        </div>
                        <div class="weui-cell__bd" @click="showCollectPositionPicker">{{ collectPositionLabel }}</div>
                        <input type="hidden" v-model="sample.collectPosition"
                               required emptyTips="请选择采样区域">
                    </div>
                </div>
                <div class="weui-cells__title" style="margin-top: 20px;">采样人信息</div>
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell weui-cell_active" :class="{'weui-cell_readonly': isReadonly}">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>姓名</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" placeholder="请填写姓名"
                                   v-model="normalizedCollectorName"
                                   required emptyTips="请填写姓名"
                                   v-bind:readonly="isReadonly"/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active" :class="{'weui-cell_readonly': isReadonly}">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>手机号</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" type="tel" maxlength="11" placeholder="请填写手机号"
                                   required emptyTips="请填写手机号"
                                   pattern="REG_MOBILE" notMatchTips="手机号不正确"
                                   v-model="sample.collectorMobile"
                                   v-bind:readonly="isReadonly"/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active" v-if="!loggedIn">
                        <div class="weui-cell__hd">
                            <label class="weui-label"><span class="required">*</span>认证码</label>
                        </div>
                        <div class="weui-cell__bd">
                            <input class="weui-input" type="tel" maxlength="11" placeholder="请填写认证码"
                                   required emptyTips="请填写认证码"
                                   v-model="sample.params.authCode"/>
                        </div>
                    </div>
                    <div class="weui-cell weui-cell_active" style="flex-direction: column;"
                         v-show="!isReadonly && sample.code && count !== null">
                        <p id="tips" class="weui-form__tips">
                            本组 <span class="text-red">{{ sample.code }}</span> 已采集 <span class="text-red">{{ count }}</span>
                            个，
                            这是第 <span class="text-red">{{ count + 1 }}</span> 个
                        </p>
                        <a class="weui-btn weui-btn_primary" @click="saveSample">保存</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="weui-panel weui-panel_access" v-if="recentSamples.length > 0">
            <div class="weui-panel__hd">最近十次登记</div>
            <div class="weui-media-box weui-media-box_text" v-for="sample in recentSamples">
                <h4 class="weui-media-box__title">{{ sample.code }}</h4>
                <p class="weui-media-box__desc log-body">
                    <span>{{ sample.personName }}，{{ sample.collectTime }}</span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="/js/<EMAIL>"></script>
<script src="/js/qs.js"></script>
<script src="/js/zepto.min.js"></script>
<script src="/js/weui.min.js"></script>
<script>
    Vue.createApp({
        data() {
            return {
                sample: {params: {}},
                authCode: '',
                nations: [],
                personTypes: [],
                personDays: [],
                collectPositions: [],
                mixTypes: [],
                loggedIn: false,
                isReadonly: false,
                count: null,
                recentSamples: [],
            }
        },
        computed: {
            normalizedCollectorName: {
                get() {
                    return (this.sample.collectorName || '').trim()
                },
                set(value) {
                    this.sample.collectorName = value.replace(/[^\u4E00-\u9FFF]/g, '·');
                }
            },
            personNationLabel() {
                try {
                    return this.nations.find(it => it.value === this.sample.personNation.toString()).label;
                } catch (e) {
                    return '';
                }
            },
            personTypeLabel() {
                try {
                    return this.personTypes.find(it => it.value === this.sample.personType.toString()).label;
                } catch (e) {
                    return '';
                }
            },
            personDaysLabel() {
                try {
                    return this.personDays.find(it => it.value === this.sample.personDays.toString()).label;
                } catch (e) {
                    return '';
                }
            },
            mixTypeLabel() {
                try {
                    return this.mixTypes.find(it => it.value === this.sample.params.mixType.toString()).label || '请选择混样类型';
                } catch (e) {
                    console.warn(e)
                    return '请选择混样类型';
                }
            },
            collectPositionLabel() {
                try {
                    return this.collectPositions.find(it => it.value === this.sample.collectPosition.toString())?.label || '请选择采样区域';
                } catch (e) {
                    console.warn(e)
                    return '请选择采样区域';
                }
            },
            mixCode() {
                return this.sample.code || '';
            },
        },
        watch: {
            mixCode: function () {
                this.onMixCodeChange();
            }
        },
        created() {
            const that = this;

            const personId = Qs.parse((location.search || '?').substring(1)).personId;
            if (!personId) {
                weui.alert('二维码无效');
            }
            $.getJSON('/open/natPerson/queryById', {
                id: personId
            }, function (ret) {
                if (ret.code !== 0) {
                    weui.alert(ret.msg);
                    return;
                }

                const person = ret.data;
                that.sample = {
                    personId: person.id,
                    personDepartment: person.department,
                    personIdCardNo: person.idCardNo,
                    personName: person.name,
                    personMobile: person.mobile,
                    personNation: person.nation,
                    personType: person.type,
                    personDays: person.days,
                    personAddress: person.address,
                    collectorName: '',
                    collectorMobile: '',
                    collectAddress: '新医大六附院',
                    params: {
                        authCode: '',
                        mixType: '',
                    }
                };
                that.loadFromLocalStorage();
            });

            const dictMapper = function (it) {
                return {
                    "label": it.dictLabel,
                    "value": it.dictValue
                };
            }

            $.getJSON('/open/dict/queryByType', {
                "type": "minzu"
            }, function (ret) {
                that.nations = ret.map(dictMapper);
            });

            $.getJSON('/open/dict/queryByType', {
                "type": "nat_person_type"
            }, function (ret) {
                that.personTypes = ret.map(dictMapper);
            });

            $.getJSON('/open/dict/queryByType', {
                "type": "nat_frequency"
            }, function (ret) {
                that.personDays = ret.map(dictMapper);
            });

            $.getJSON('/open/dict/queryByType', {
                "type": "nat_mix_type"
            }, function (ret) {
                that.mixTypes = ret.map(dictMapper);
            });

            $.getJSON('/open/dict/queryByType', {
                "type": "nat_collect_position"
            }, function (ret) {
                that.collectPositions = ret.map(dictMapper);
            });
        },
        methods: {
            onInputSampleCode() {
                let sampleCode = this.sample.code === null || this.sample.code === undefined ? '' : this.sample.code.toString().replaceAll(/[^0-9]/g, '')
                if (sampleCode.length > 12) {
                    sampleCode = sampleCode.substring(0, 12)
                }
                this.sample.code = sampleCode
            },
            loadFromLocalStorage() {
                let loggedIn = localStorage.getItem('loggedIn') === 'true';
                if (!loggedIn) {
                    // 没有认证时间，或上次认证距今已超过4小时，需要重新认证
                    const loggedInTime = localStorage.getItem('loggedInTime');
                    if (!loggedInTime) {
                        loggedIn = false
                    } else if (new Date().getTime() - loggedInTime > 14400000) {
                        loggedIn = false
                    }
                }
                this.loggedIn = loggedIn;

                const lastSampleJson = localStorage.getItem('lastSample');
                if (lastSampleJson) {
                    const lastSample = JSON.parse(lastSampleJson);
                    this.sample.code = lastSample.code;
                    this.sample.collectorName = lastSample.collectorName;
                    this.sample.collectorMobile = lastSample.collectorMobile;
                    this.sample.collectAddress = lastSample.collectAddress;
                    this.sample.params.mixType = lastSample.params.mixType;
                    this.sample.collectPosition = lastSample.collectPosition || '';
                }

                this.recentSamples = JSON.parse(localStorage.getItem('recentSamples') || '[]');
            },
            saveSample() {
                const that = this;
                weui.form.validate('#sampleForm', function (hasError) {
                    if (!hasError) {
                        const loader = weui.loading('正在保存');
                        $.ajax({
                            url: '/open/natSample/create',
                            type: 'POST',
                            data: JSON.stringify(that.sample),
                            dataType: 'json',
                            contentType: 'application/json;charset=utf-8',
                            success: function (ret) {
                                loader.hide();
                                if (ret.code !== 0) {
                                    weui.alert(ret.msg);
                                } else {
                                    that.isReadonly = true;
                                    that.updateLocalStorage(ret.data);
                                    weui.alert('保存成功！请关闭本页面，使用微信扫码并登记下一个样品', function () {
                                        if (typeof WeixinJSBridge !== 'undefined') {
                                            WeixinJSBridge.call('closeWindow');
                                        } else {
                                            window.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
                }, {
                    regexp: {
                        MOBILE: /^1[3-9]\d{9}$/
                    }
                });
            },
            updateLocalStorage(sample) {
                localStorage.setItem('loggedIn', 'true');
                localStorage.setItem('loggedInTime', new Date().getTime().toString());
                this.loggedIn = true

                localStorage.setItem('lastSample', JSON.stringify(sample));
                if (this.recentSamples.unshift(sample) > 10) {
                    this.recentSamples.splice(10);
                }
                localStorage.setItem('recentSamples', JSON.stringify(this.recentSamples));
            },
            showMixTypePicker() {
                const that = this;
                weui.picker(this.mixTypes, {
                    title: '混样类型',
                    onConfirm: function (selections) {
                        if (selections && selections[0]) {
                            that.sample.params.mixType = selections[0].value;
                        }
                    }
                });
            },
            showCollectPositionPicker() {
                console.log('showCollectPositionPicker')
                const that = this;
                weui.picker(this.collectPositions, {
                    title: '采样区域',
                    onConfirm: function (selections) {
                        if (selections && selections[0]) {
                            that.sample.collectPosition = selections[0].value;
                        }
                    }
                });
            },
            onMixCodeChange() {
                const that = this;
                $.getJSON('/open/natSample/countByCode', {
                    "code": this.sample.code
                }, function (ret) {
                    if (ret.code === 0) {
                        that.count = ret.data;
                    }
                });
            }
        }
    }).mount('#app');
</script>
</body>
</html>
