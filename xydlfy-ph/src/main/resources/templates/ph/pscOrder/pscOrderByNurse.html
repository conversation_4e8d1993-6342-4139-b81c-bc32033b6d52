<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单列表')"/>
    <style>
        #bootstrap-table td:first-of-type a.btn {
            display: inline-block;
            margin: 5px !important;
        }

        .patient-name {
            font-weight: bold;
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>住院号：</label>
                            <input type="text" name="admissionNumber"/>
                        </li>
                        <li>
                            <label>患者姓名：</label>
                            <input type="text" name="patientName"/>
                        </li>
                        <li>
                            <label>陪检员工号：</label>
                            <input type="text" name="carerEmployeeNo"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" th:with="type=${@dict.getType('psc_order_status')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label>创建时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:pscOrder:exportByNurse">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:pscOrder:editByNurse')}]];
    var removeFlag = [[${@permission.hasPermi('ph:pscOrder:removeByNurse')}]];
    var confirmFlag = [[${@permission.hasPermi('ph:pscOrder:confirm')}]];
    var patientSexDatas = [[${@dict.getType('sys_user_sex')}]];
    var patientNationDatas = [[${@dict.getType('minzu')}]];
    var serviceTypeDatas = [[${@dict.getType('psc_service_type')}]];
    var examItemDatas = [[${@dict.getType('psc_exam_item')}]];
    var statusDatas = [[${@dict.getType('psc_order_status')}]];
    var scoreDatas = [[${@dict.getType('psc_order_score')}]];
    var prefix = ctx + "ph/pscOrder";


    $(function () {
        var options = {
            url: prefix + "/listByNurse",
            createUrl: prefix + "/addByNurse",
            updateUrl: prefix + "/editByNurse/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/exportByNurse",
            modalName: "订单",
            pageSize: 25,
            columns: [
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '100px',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editOrder(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        if (row.status === 0) {
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        if (row.status === 4) {
                            actions.push('<a class="btn btn-danger btn-xs ' + confirmFlag + '" href="javascript:void(0)" onclick="confirm(' + row.id + ')"><i class="fa fa-check"></i>验收</a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '下单时间',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return value.split(' ').join('<br>')
                    }
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return value ? value.split(' ').join('<br>') : ''
                    }
                },
                {
                    field: 'serviceType',
                    title: '服务类型',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(serviceTypeDatas, value);
                    }
                },
                {
                    field: 'examItems',
                    title: '检查项目',
                    width: '120px',
                    formatter: function (value, row, index) {
                        const examItemsByValue = {};
                        examItemDatas.forEach(it => {
                            examItemsByValue[it.dictValue] = it.dictLabel.split('-')[1]
                        });
                        const values = value.split(',');
                        return values.map(it => examItemsByValue[it]).join('、');
                    }
                },
                {
                    field: 'patientId',
                    title: '患者',
                    width: '210px',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html = [
                            `姓名：<span class="patient-name" onclick="onClickPatientName('${row.patientName}');">${row.patientName}</span>`,
                            `住院号：${row.admissionNumber}`
                        ]
                        return html.join('<br>')
                    }
                },
                {
                    field: 'patientDepartment',
                    title: '科室（床号）',
                    width: '210px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return `<span style="color: red;">${row.patientDepartment}<br/>${row.patientBedNumber}</span>`
                    }
                },
                {
                    field: 'carerName',
                    title: '陪检员',
                    width: '210px',
                    align: 'center'
                },
                {
                    field: 'score',
                    title: '评价',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(scoreDatas, value);
                    }
                }
            ]
        };
        $.table.init(options);

        // 自动刷新列表
        var searching = false
        setInterval(function () {
            if (!searching) {
                searching = true
                $.table.search()
                searching = false
            }
        }, 5000);

        if ([[${@permission.isPermitted('ph:pscOrder:addByNurse')}]]) {
            addOrder();
        }
    });

    function addOrder() {
        $.operate.add('', 800, 600);
    }

    function editOrder(id) {
        $.operate.edit(id, 800, 600);
    }

    function onClickPatientName(patientName) {
        $('#patientName').val(patientName);
        $.table.search();
    }

    function confirm(id) {
        $.modal.open('验收订单', '/ph/pscOrder/confirm/' + id, 600, 600);
    }
</script>
</body>
</html>
