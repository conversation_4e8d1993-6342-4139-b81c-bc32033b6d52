<!-- Generated by SpecStory -->

# 列出控制器类及非公有方法 (2025-06-26 21:07Z)

_**User**_

列出 @/controller 目录下所有类及其非公有方法，整理为一份TODO List

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Analyzing Controller Methods**

I'm currently focused on extracting non-public methods within the controller classes.  I've parsed the directory structure, zeroing in on the relevant Kotlin files. My next step involves inspecting each file to identify and list those methods, excluding any with public access modifiers.


**Listing Non-Public Methods**

I've started processing the attached Kotlin files from the controller directory. My current focus is on extracting a TODO list. I'm reading each file, identifying the classes within, and pinpointing non-public methods.  I'll then format the output as requested.


**Creating a Method TODO List**

I've refined my approach to generate a method TODO list.  The files are loaded and parsed. I'm now actively searching within each Kotlin file for class definitions, followed by an identification of all non-public methods.  I'm ready to present a formatted TODO list.


**Refining File Identification**

I'm now identifying and listing the files within the specified controller directory. This involves validating the paths and making a directory listing. After this I'll proceed to the code parsing phase. I have confirmed that the user's `@/controller` refers to the target directory.


</details></think>

---

好的，我将为您整理一份 `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/` 目录下所有类及其非公有方法的 TODO List。

首先，我需要列出该目录下的所有文件，以确保覆盖所有控制器。

---

<details>
            <summary>Listed directory xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ • **40** results</summary>
        
| Name |
|-------|
| 📄 `ScheduleApiController.kt` |
| 📄 `ReservationApiController.kt` |
| 📄 `WxTransferAdminController.kt` |
| 📄 `MaterialApiController.kt` |
| 📄 `DrugApiController.kt` |
| 📄 `SSLController.kt` |
| 📄 `WxController.kt` |
| 📄 `PatientApiController.kt` |
| 📄 `ErhcCardApiController.kt` |
| 📄 `MenzhenController.kt` |
| 📄 `CardRefundApplicationApiController.kt` |
| 📄 `HisRefundController.kt` |
| 📄 `WxPaymentAdminController.kt` |
| 📄 `HealthCardAdminController.kt` |
| 📄 `ZhuyuanController.kt` |
| 📄 `ChuFangController.kt` |
| 📄 `EInvoiceController.kt` |
| 📄 `WxRefundAdminController.kt` |
| 📄 `WxCheckAdminController.kt` |
| 📄 `WeixinMipController.kt` |
| 📄 `ReportAdminController.kt` |
| 📄 `ReportApiController.kt` |
| 📄 `NatSampleController.kt` |
| 📄 `PatientAdminController.kt` |
| 📄 `NatSample1OpenController.kt` |
| 📄 `NatPatientOpenController.kt` |
| 📄 `NatPersonController.kt` |
| 📄 `LogController.kt` |
| 📄 `JubaoCaseApiController.kt` |
| 📄 `DoctorApiController.kt` |
| 📄 `ConfigController.kt` |
| 📄 `DepartmentApiController.kt` |
| 📄 `DictApiController.kt` |
| 📄 `AlipayMipController.kt` |
| 📄 `ArticleController.kt` |
| 📄 `AlipayController.kt` |
| 📄 `AlipayCheckAdminController.kt` |
| 📄 `MipOrderCreateForm.kt` |
| 📄 `FileApiController.kt` |
| 📄 `JubaoCaseCategoryApiController.kt` |

</details>

---

<think><details><summary>Thought Process</summary>**Examining the Codebase**

I've scoped out the file list from the specified directory. My current focus is dissecting each file, pinpointing the non-public methods. I'm building a concise TODO list as I progress, prioritizing those files already in the process. Each file will be processed individually to build the todo list.


**Building the initial list**

I've begun examining the files in the directory. Currently, I'm focusing on the attached files, identifying the non-public methods within each. I've already analyzed `AlipayCheckAdminController.kt` and `AlipayController.kt`, and there were no non-public methods. I'm now in the middle of analyzing `AlipayMipController.kt`, where I've identified two methods that need attention. I'll continue systematically through the remaining files.


**Completing the analysis**

I've made steady progress, completing the analysis of the attached files.  I've identified several private methods needing further attention, specifically within `AlipayMipController.kt` and `CardRefundApplicationApiController.kt`.  My next step is to continue with the remaining files in the directory. I'll maintain my focus on systematically identifying and logging all non-public methods.


**Reviewing Recent Findings**

I've continued to meticulously analyze the files, and identified several additional private methods that require attention. Specifically, I flagged methods within `ChuFangController.kt`, namely `checkHistoryWeixinPayment` and `checkHistoryAlipayPayment`. I'm also now looking at `DrugApiController.kt`, `EInvoiceController.kt`, and `ErhcCardApiController.kt`. The list is growing, and I'll keep the TODO list updated.


**Finalizing Method Identification**

My analysis continues with the remaining files in the directory. I've successfully completed the initial pass, identifying numerous private methods that necessitate further review within `AlipayMipController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, and `HisRefundController.kt`, and have added those methods to my TODO list. I'm now transitioning to the next set of files to maintain a thorough and exhaustive approach.


**Finalizing Method Identification**

I've meticulously analyzed the attached files, identifying and adding private methods to my TODO list. This now includes methods in `AlipayMipController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, and `HisRefundController.kt` that require further review. I'm now transitioning to a new set of files, to maintain the exhaustive scope of my assessment.


**Reviewing Initial Analysis**

I've methodically assessed the attached files, flagging several private methods that warrant attention. These include methods within `AlipayMipController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, and `HisRefundController.kt`. I'm now transitioning to the next batch of files to maintain a thorough and exhaustive approach.


**Analyzing Remaining Files**

I've completed the initial pass on the attached files, identifying private methods in `AlipayMipController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, `HisRefundController.kt`, and `ReportApiController.kt`. Now, I'm moving on to the remaining files to ensure a comprehensive analysis. I'll maintain my focus on systematically logging all non-public methods.


</details></think>

---

好的，我已经获取了目录下的所有文件名。现在我将逐一分析这些文件，找出其中的非公有方法，并整理成一份 TODO List。

为了高效处理，我将分批读取文件内容。首先读取第一批文件。

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ScheduleApiController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ReservationApiController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxTransferAdminController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/SSLController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxPaymentAdminController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxRefundAdminController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxCheckAdminController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhuyuanController.kt

---

<think><details><summary>Thought Process</summary>**Examining Non-Public Methods**

I've just reviewed the first batch of files, and identified non-public methods. I'm focusing on summarizing those findings before proceeding to the next set of documents. So far, I've identified the use of private functions to determine the correct environment in test mode within the AlipayMipController.kt file.


**Reviewing Identified Methods**

I've finished the initial summary, focusing on non-public methods. I've compiled a detailed list, and am now reading the remainder of the reservation API controller to complete my analysis of the batch. The first pass through the files identified a lot of internal logic for environment determination and payment processing. I'm focusing on the context of the methods to ensure I thoroughly understand their function.


**Cataloging Private Functions**

I've just finished compiling the findings from the first batch of files, cataloging all identified private functions. This includes environment determination, refund processing, payment methods, and specific administrative functions. Now, I'll proceed with reading the remaining files.


**Detailing Further Findings**

I've summarized the findings from the first batch, including environment determination, refund processing, and administrative functions. Now I'll proceed with the remaining large files, starting with lines 201 onwards in the remaining files. I'm focusing on those, to round out the details in my analysis.


</details></think>

---

好的，第一批文件已分析完毕。现在，我将继续读取剩余几个较大的文件，以确保覆盖所有类的非公有方法。

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ReservationApiController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WxCheckAdminController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt

---

<think><details><summary>Thought Process</summary>**Proceeding With Analysis**

I've moved on to the next phase. I've finished processing the second batch and am now deep into the large files, focusing on the remaining parts to finalize the identification of non-public methods. Specifically, I'm examining `ReservationApiController.kt` starting from line 401 and then moving on to `WeixinMipController.kt`, also from line 401. I'm aiming to complete this analysis promptly.


**Refining the Focus**

I'm now fully immersed in the large files. My primary aim is to identify the non-public methods. I'm focusing on  `ReservationApiController.kt` starting from line 401 and `WeixinMipController.kt`, also from line 401. I intend to generate the final TODO list with this information.


</details></think>

---

好的，第二批文件也已收到。我将继续分析这些较大文件的剩余部分，以完成非公有方法的排查。完成后，我将为您呈现最终的 TODO 清单。

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ReservationApiController.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/WeixinMipController.kt

---

<think><details><summary>Thought Process</summary>**Verifying non-public methods**

I've completed my review of all controller files, and now I'm finalizing the list of non-public methods. I have identified `private fun getEnvByTestMode(testMode: Boolean): String` and `private fun doCreate` in `AlipayMipController.kt` as methods that seem to have non-public accessibility.


**Confirming method visibility**

I've finished the comprehensive list of non-public methods after carefully reviewing all controller files. I am now confident in the methods I've flagged and am ready to move to the next stage of verification.


**Completing the Final List**

I've completed my thorough review of all controller files, and I've compiled an exhaustive list of non-public methods. I've meticulously checked for 'private', 'protected', and 'internal' keywords, with a focus on 'private' since Kotlin defaults to public visibility. I'm now certain this list is comprehensive and ready for the next step.


</details></think>

---

好的，我已经分析了 `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/` 目录下的所有控制器类。以下是整理出的所有非公有方法的 TODO List，您可以根据这个列表来审查和重构这些内部方法。

### TODO List: 非公有方法审查

-   [ ] **`AlipayMipController.kt`**
    -   [ ] `private fun getEnvByTestMode(testMode: Boolean): String`
    -   [ ] `private fun doCreateOrder(form: MipOrderCreateForm): AjaxResult`

-   [ ] **`CardRefundApplicationApiController.kt`**
    -   [ ] `private fun processApplication(form: CardRefundApplicationForm): AjaxResult`
    -   [ ] `private fun processRequestRefund(form: RequestRefundForm): AjaxResult`

-   [ ] **`ChuFangController.kt`**
    -   [ ] `private fun checkHistoryWeixinPayment(cardNo: String, clinicNo: String, prescriptionNo: String): AjaxResult`
    -   [ ] `private fun checkHistoryAlipayPayment(cardNo: String, clinicNo: String, prescriptionNo: String): AjaxResult`

-   [ ] **`HisRefundController.kt`**
    -   [ ] `private fun refundWeixinPay(form: Form, payment: WxPayment): AjaxResult`
    -   [ ] `private fun refundAlipay(form: Form, payment: AlipayPayment): AjaxResult`
    -   [ ] `private fun refundYibao(form: Form, mipWxOrder: MipWxOrder): AjaxResult`
    -   [ ] `private fun refundYibao(form: Form, mipAlipayOrder: MipAlipayOrder): AjaxResult`

-   [ ] **`ReportApiController.kt`**
    -   [ ] `private fun pacs(patientId: String, patientName: String, type: String, pageNo: Int, pageSize: Int): AjaxResult`

-   [ ] **`ReservationApiController.kt`**
    -   [ ] `private fun validateBeforeReserve(currentPatient: Patient, jiuzhenDate: LocalDate, schemaId: String, testMode: Boolean)`
    -   [ ] `private fun buildReservation(...)`
    -   [ ] `private fun reserveWithBalance(reservation: Reservation): AjaxResult`

-   [ ] **`WxController.kt`**
    -   [ ] `private fun ok(msg: String = "SUCCESS"): String`
    -   [ ] `private fun fail(msg: String = "FAIL"): String`

-   [ ] **`WeixinMipController.kt`**
    -   [ ] `private fun getEnvByTestMode(testMode: Boolean): String`
    -   [ ] `private fun doCreateOrder(form: MipOrderCreateForm): AjaxResult`

-   [ ] **`WxCheckAdminController.kt`**
    -   [ ] `companion object { private val log: org.dromara.hutool.log.Log }`

-   [ ] **`SSLController.kt`**
    -   [ ] `private val log: Log`

-   [ ] **`FileApiController.kt`**
    -   [ ] `companion object { private val log }`

其他所有控制器经过分析，其方法均为公有（public）方法。希望这份清单能帮助您进行后续的工作。

---

_**User**_

列出 @/controller 目录下所有类及其公有方法，整理为一份TODO List，并保存为 markdown 文件

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Generate Markdown List**

I've successfully listed all the files within the specified directory. My next step is to parse these files to identify and extract the public methods. Then, I plan to format these method names into a well-structured Markdown TODO list.


**Extracting Public Methods**

I've moved on to reading the Kotlin files. My initial plan is to read in batches, but I think I'll try to do them all at once. Once the file contents are read, I'll parse each file to identify and list the public methods. My next step will involve formatting this information into a Markdown TODO list and saving it to a new file.


**Refining Method Identification**

I'm now focusing on parsing the Kotlin files. I'm leveraging the list of files I generated earlier. Currently, I'm analyzing the content of `AlipayCheckAdminController.kt`, and I'm refining the process of identifying and extracting the public methods. This involves ignoring `private`, `protected`, and `internal` methods. The next step will be to format the identified methods into a Markdown TODO list.


**Extracting Methods from Files**

I'm now iterating through the already read files, parsing their contents to identify public methods. I'm focusing on excluding private, protected, and internal methods. I have identified the methods from `AlipayCheckAdminController.kt`, `AlipayController.kt` and `AlipayMipController.kt`. Next, I will format this information into a Markdown TODO list for easier viewing and further processing.


**Extracting Public Methods (Continued)**

I'm now iterating through the previously read files and extracting the public methods. I've successfully processed the content from `AlipayCheckAdminController.kt`, `AlipayController.kt`, `AlipayMipController.kt`, `ArticleController.kt`, and `CardRefundApplicationApiController.kt`. I'm excluding private, protected, and internal methods. Next, I plan to format all this extracted information into a Markdown TODO list for easy analysis and future processing.


**Compiling Method Data**

I'm now collating the public method data I extracted from the files. I'm focusing on consolidating the information into a single, cohesive structure that's easier to process. My aim is to have a comprehensive list of all the public methods across all classes, ready for formatting into a Markdown TODO list. I'm taking care to ensure that I have captured all the methods and that I'm correctly identifying the class each method belongs to.


**Consolidating & Formatting**

I've completed extracting public methods from the following files: `AlipayCheckAdminController.kt`, `AlipayController.kt`, `AlipayMipController.kt`, `ArticleController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, `ConfigController.kt`, `DepartmentApiController.kt`, `DictApiController.kt`, `DoctorApiController.kt`, `DrugApiController.kt`, `EInvoiceController.kt`, and `ErhcCardApiController.kt`. I'm now focusing on consolidating all this data into a single Markdown-formatted TODO list. This structured list will make it easier to analyze and process the extracted methods.


**Generating Comprehensive TODO**

I've been consolidating and formatting the public method data, derived from the Kotlin files. This list now includes the public methods of  `AlipayCheckAdminController.kt`, `AlipayController.kt`, `AlipayMipController.kt`, `ArticleController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, `ConfigController.kt`, `DepartmentApiController.kt`, `DictApiController.kt`, `DoctorApiController.kt`, `DrugApiController.kt`, `EInvoiceController.kt`, `ErhcCardApiController.kt`, `FileApiController.kt`, `HealthCardAdminController.kt`, `HisRefundController.kt`, `JubaoCaseApiController.kt`, `JubaoCaseCategoryApiController.kt`, `LogController.kt`, `MaterialApiController.kt`, and `MenzhenController.kt`. My next step is to finalize this Markdown TODO list for easy reference.


**Generating Detailed TODO List**

I'm now integrating the public methods extracted from the remaining files: `ScheduleApiController.kt`, `ReservationApiController.kt`, `WxTransferAdminController.kt`, `DrugApiController.kt`, `SSLController.kt`, `WxController.kt`, `PatientApiController.kt`, `WxPaymentAdminController.kt`, `ZhuyuanController.kt`, `WxRefundAdminController.kt`, `WxCheckAdminController.kt`, `WeixinMipController.kt`, `ReportAdminController.kt`, `ReportApiController.kt`, `NatSampleController.kt`, `PatientAdminController.kt`, `NatSample1OpenController.kt`, `NatPersonController.kt`, and `JubaoCaseCategoryApiController.kt`. This includes integrating `MipOrderCreateForm.kt`, a data class with no methods. The consolidated list is nearly complete, and I'm ready to produce the Markdown TODO file soon.


**Refining & Expanding List**

I've completed my initial pass, extracting public methods from the following files: `AlipayCheckAdminController.kt`, `AlipayController.kt`, `AlipayMipController.kt`, `ArticleController.kt`, `CardRefundApplicationApiController.kt`, `ChuFangController.kt`, `ConfigController.kt`, `DepartmentApiController.kt`, `DictApiController.kt`, `DoctorApiController.kt`, `DrugApiController.kt`, `EInvoiceController.kt`, `ErhcCardApiController.kt`, `FileApiController.kt`, `HealthCardAdminController.kt`, `HisRefundController.kt`, `JubaoCaseApiController.kt`, `JubaoCaseCategoryApiController.kt`, `LogController.kt`, `MaterialApiController.kt`, `MenzhenController.kt`, `NatPatientOpenController.kt`, `NatPersonController.kt`, `NatSample1OpenController.kt`, `NatSampleController.kt`, and `PatientAdminController.kt`. I'm now integrating the remaining methods, ensuring that the process is comprehensive. The next step will be to format the extracted information into a Markdown TODO list, enabling further processing and analysis.


**Refining & Expanding (Continued)**

I've been working on compiling the final list of public methods for the Markdown TODO list. I'm now integrating the public methods from `ScheduleApiController.kt` and `ReservationApiController.kt`. Currently, I'm focusing on cleaning and organizing this data to ensure the final list is accurate and well-structured.


**Extracting and Processing**

I'm currently extracting the public methods from the remaining files and compiling them into a single list. I've finished extracting methods from `ScheduleApiController.kt`, and `WxTransferAdminController.kt`, `ZhuyuanController.kt`, and `PatientApiController.kt`, so I'm finalizing the Markdown TODO list. I'm also ensuring the list's accuracy and readability. This comprehensive list will enable me to format the Markdown TODO file soon.


**Refining Method Extraction**

I'm now iterating through the file list and extracting public methods from the remaining files. I'm focusing on excluding private, protected, and internal methods. I've compiled the initial data, read the files and, for each file read, identified the classes and their public methods. I am now refining this process to ensure accuracy and completeness.  The next step will be to format the extracted information into a Markdown TODO list.


**Extracting and Processing (Continued)**

I'm now iterating through the file list and extracting public methods from the remaining files. I'm focusing on excluding private, protected, and internal methods. I've compiled the initial data, read the files and, for each file read, identified the classes and their public methods. I am now refining this process to ensure accuracy and completeness.  The next step will be to format the extracted information into a Markdown TODO list.


</details></think>

---

好的，我将为您整理一份 `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/` 目录下所有类及其所有公有方法的 TODO List，并将其保存为 Markdown 文件。

首先，我将分析所有控制器文件以提取类和公有方法。然后，我会将这些信息格式化为 Markdown TODO 列表。最后，我会将这个列表写入一个新文件 `controller_public_methods_todo.md` 中。

这是我将要创建的文件内容：

```markdown
# Controller Public Methods TODO List

This document lists all public methods in the controllers under `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/`.

## `AlipayCheckAdminController.kt`
- [ ] `alipaycheck(modelMap: ModelMap): String`
- [ ] `report(): String`
- [ ] `list(alipayCheck: AlipayCheck): TableDataInfo?`
- [ ] `reportList(alipayCheck: AlipayCheck): TableDataInfo`
- [ ] `export(alipayCheck: AlipayCheck): AjaxResult`
- [ ] `reportExport(alipayCheck: AlipayCheck): AjaxResult`
- [ ] `editSave(alipayCheck: AlipayCheck): AjaxResult`
- [ ] `syncAlipayBill(id: Long): AjaxResult`
- [ ] `downloadAlipayBill(id: Long): ResponseEntity<StreamingResponseBody>`
- [ ] `syncHisBill(id: Long): AjaxResult`
- [ ] `downloadHisBill(id: Long, response: HttpServletResponse)`
- [ ] `showDiffOrders(id: Long): ModelAndView`
- [ ] `diffOrders(id: Long): TableDataInfo`

## `AlipayController.kt`
- [ ] `login(code: String, scope: String, response: HttpServletResponse): AjaxResult`
- [ ] `onPay(outTradeNo: String): String`
- [ ] `onMipPay(outTradeNo: String): String`
- [ ] `authorizeHospitalOrder(authCode: String): AjaxResult`

## `AlipayMipController.kt`
- [ ] `getPendingSettlementsV1(testMode: Boolean): AjaxResult`
- [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
- [ ] `authAlipay(authCode: String): AjaxResult`
- [ ] `authAlipayMip(authCode: String, latitude: String, longitude: String, callbackPage: String?): AjaxResult`
- [ ] `createOrder(clinicNo: String, prescriptionNo: String, amount: BigDecimal, authCode: String, acctUsedFlag: String, testMode: Boolean): AjaxResult`
- [ ] `createOrderV1(form: MipOrderCreateForm): AjaxResult`
- [ ] `payOrder(payOrderId: String): AjaxResult`
- [ ] `queryOrder(payOrderId: String): AjaxResult`
- [ ] `refundOrder(form: RefundOrderForm): AjaxResult`

## `ArticleController.kt`
- [ ] `paginate(categoryId: Long, pageSize: Int, pageNumber: Int): TableDataInfo`
- [ ] `show(id: Long): AjaxResult`
- [ ] `preview(id: Long, attrs: ModelMap): String`

## `CardRefundApplicationApiController.kt`
- [ ] `getRefundableCards(idCardNo: String, name: String): AjaxResult`
- [ ] `applyJson(form: CardRefundApplicationForm): AjaxResult`
- [ ] `applyForm(form: CardRefundApplicationForm): AjaxResult`
- [ ] `myApplications(): AjaxResult`
- [ ] `detail(id: Long): AjaxResult`
- [ ] `requestRefundByJson(form: RequestRefundForm): AjaxResult`
- [ ] `requestRefundByForm(form: RequestRefundForm): AjaxResult`
- [ ] `getTransfers(applicationId: Long, cardNo: String): AjaxResult`
- [ ] `refreshTransferState(transferId: Long): AjaxResult`

## `ChuFangController.kt`
- [ ] `payByWeChat(clinicNo: String, prescriptionNo: String, amount: BigDecimal, testMode: Boolean, request: HttpServletRequest): AjaxResult`
- [ ] `refreshWechatOrder(zyPayNo: String): AjaxResult`
- [ ] `payByAlipay(clinicNo: String, prescriptionNo: String, amount: BigDecimal, testMode: Boolean, request: HttpServletRequest): AjaxResult`
- [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`

## `ConfigController.kt`
- [ ] `getConfigs(): AjaxResult`
- [ ] `getConfigValueAsString(configKey: String): AjaxResult`
- [ ] `getConfigValueAsBoolean(configKey: String): AjaxResult`

## `DepartmentApiController.kt`
- [ ] `paginate(pageNo: Int?, pageSize: Int?, kw: String?): TableDataInfo`
- [ ] `show(id: String): AjaxResult`

## `DictApiController.kt`
- [ ] `findByType(type: String): AjaxResult`

## `DoctorApiController.kt`
- [ ] `list(departmentId: String): AjaxResult`
- [ ] `show(id: String): AjaxResult`

## `DrugApiController.kt`
- [ ] `queryByCode(keyword: String): AjaxResult`

## `EInvoiceController.kt`
- [ ] `index(startDate: LocalDate, endDate: LocalDate, pageSize: Int, pageNumber: Int, request: HttpServletRequest): AjaxResult`
- [ ] `download(token: String, response: HttpServletResponse)`

## `ErhcCardApiController.kt`
- [ ] `getOrRegisterV1(idCardNo: String, name: String, mobile: String, nationCode: String, address: String, testMode: Boolean): AjaxResult`
- [ ] `getQrCode(jzCardNo: String): AjaxResult`

## `FileApiController.kt`
- [ ] `upload(file: MultipartFile): AjaxResult`

## `HealthCardAdminController.kt`
- [ ] `index(): String`
- [ ] `query(cardType: String, cardNo: String): AjaxResult`

## `HisRefundController.kt`
- [ ] `refund(form: Form): AjaxResult`

## `JubaoCaseApiController.kt`
- [ ] `create(jubao: JubaoCase, request: HttpServletRequest): AjaxResult`
- [ ] `query(request: HttpServletRequest): AjaxResult`

## `JubaoCaseCategoryApiController.kt`
- [ ] `getCaseCategories(): AjaxResult`

## `LogController.kt`
- [ ] `log(file: String, line: Int, message: String, token: String): AjaxResult`

## `MaterialApiController.kt`
- [ ] `queryByCode(keyword: String): AjaxResult`

## `MenzhenController.kt`
- [ ] `getRegistrations(testMode: Boolean): AjaxResult`
- [ ] `getPrescriptions(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
- [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
- [ ] `getReservationsForCheckIn(): AjaxResult`
- [ ] `checkIn(token: String): AjaxResult`
- [ ] `getQueue(): AjaxResult`
- [ ] `getMedicineOrder(beginDate: LocalDate, endDate: LocalDate): AjaxResult`
- [ ] `bill(testMode: Boolean): AjaxResult`
- [ ] `refreshWxpayOrder(zyPayNo: String): AjaxResult`
- [ ] `wxpay(amount: Long, testMode: Boolean, mode: String): AjaxResult`
- [ ] `wxrefund(zyPayNo: String, amount: Long): AjaxResult`
- [ ] `billForRefund(endDate: Date?, days: Int?, testMode: Boolean): AjaxResult`
- [ ] `alipay(amount: Long, testMode: Boolean, mode: String): AjaxResult`
- [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`
- [ ] `alipayRefund(outTradeNo: String, amount: Long): AjaxResult`

## `NatPatientOpenController.kt`
- [ ] `getOrCreate(natPatient: NatPatient): AjaxResult`
- [ ] `getQrCode(id: Long, response: HttpServletResponse)`
- [ ] `queryById(id: Long): AjaxResult`

## `NatPersonController.kt`
- [ ] `query(idCardNo: String): AjaxResult`
- [ ] `queryById(id: Long): AjaxResult`
- [ ] `update(natPerson: NatPerson): AjaxResult`
- [ ] `getQrCode(id: Long, response: HttpServletResponse)`

## `NatSample1OpenController.kt`
- [ ] `create(sample: NatSample1): AjaxResult`
- [ ] `countByCode(code: String): AjaxResult`
- [ ] `queryByIdCardNo(idCardNo: String): AjaxResult`

## `NatSampleController.kt`
- [ ] `create(sample: NatSample): AjaxResult`
- [ ] `countByCode(code: String): AjaxResult`
- [ ] `queryByIdCardNo(idCardNo: String): AjaxResult`

## `PatientAdminController.kt`
- [ ] `patient(): String`
- [ ] `list(patient: Patient): TableDataInfo`
- [ ] `export(patient: Patient): AjaxResult`
- [ ] `add(): String`
- [ ] `addSave(patient: Patient): AjaxResult`
- [ ] `edit(id: Long, mmap: ModelMap): String`
- [ ] `editSave(patient: Patient): AjaxResult`
- [ ] `remove(ids: String): AjaxResult`
- [ ] `showSid(id: Long): AjaxResult`
- [ ] `queryHisPatient(cardNo: String): AjaxResult`

## `PatientApiController.kt`
- [ ] `addJzCard(jzCardNo: String, name: String, mobile: String, nationCode: String?, testMode: Boolean): AjaxResult`
- [ ] `addBabyJzCard(name: String, gender: Int, birthday: LocalDate, nationCode: String?, jhrName: String, jhrIdCardNo: String, mobile: String, address: String): AjaxResult`
- [ ] `list(): AjaxResult`
- [ ] `active(): AjaxResult`
- [ ] `jzCard(jzCardNo: String): AjaxResult`
- [ ] `getMenzhenBalanceV1(cardNo: String, testMode: Boolean): AjaxResult`
- [ ] `switchToJzCard(jzCardNo: String): AjaxResult`
- [ ] `removeJzCard(jzCardNo: String): AjaxResult`
- [ ] `zhuyuanHistory(zhuyuanNo: String?, testMode: Boolean): AjaxResult`
- [ ] `switchZhuyuanNo(zhuyuanNo: String, testMode: Boolean, request: HttpServletRequest): AjaxResult`
- [ ] `syncZhuyuanNo(testMode: Boolean): AjaxResult`
- [ ] `checkAndSwitchJzCard(tokenId: String): AjaxResult`

## `ReportAdminController.kt`
- [ ] `payRefundPage(attrs: Model): String`
- [ ] `payRefundData(wxCheck: WxCheck): TableDataInfo`
- [ ] `patientDailyIncrementPage(attrs: Model): String`
- [ ] `patientDailyIncrementData(patientDailyIncrement: PatientDailyIncrement): TableDataInfo`

## `ReportApiController.kt`
- [ ] `mzlis(request: HttpServletRequest): AjaxResult`
- [ ] `zylis(request: HttpServletRequest): AjaxResult`
- [ ] `lisDetail(inspectionId: String, aliAuthCode: String?, request: HttpServletRequest): AjaxResult`
- [ ] `mzpacs(pageNo: Int, pageSize: Int, request: HttpServletRequest): AjaxResult`
- [ ] `zypacs(pageNo: Int, pageSize: Int, request: HttpServletRequest): AjaxResult`
- [ ] `pacsDetail(reportId: String, aliAuthCode: String?, request: HttpServletRequest): AjaxResult`

## `ReservationApiController.kt`
- [ ] `reserveByWeChatPayV1(...)`
- [ ] `reserveByAlipayV1(...)`
- [ ] `reserveByMip(...)`
- [ ] `payReservationByWechatMip(...)`
- [ ] `payReservationByAlipayMip(...)`
- [ ] `refreshReservationStatus(...)`
- [ ] `refreshReservationStatusByZyPayNo(...)`
- [ ] `cancel(...)`
- [ ] `list(...)`
- [ ] `last(...)`
- [ ] `trace(...)`

## `ScheduleApiController.kt`
- [ ] `indexedDepartments(forceReload: Boolean, testMode: Boolean): AjaxResult`
- [ ] `doctors(departmentCode: String, date: LocalDate, testMode: Boolean): AjaxResult`
- [ ] `ticketsV1(departmentCode: String, doctorCode: String, date: LocalDate, testMode: Boolean): AjaxResult`
- [ ] `ticketItems(schid: String, testMode: Boolean): AjaxResult`
- [ ] `doctor(doctorCode: String): AjaxResult`

## `SSLController.kt`
- [ ] `renewSSL(request: RenewSSLRequest): AjaxResult`

## `WeixinMipController.kt`
- [ ] `getPendingSettlementsV1(testMode: Boolean): AjaxResult`
- [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
- [ ] `getAuthUrl(accountType: String): AjaxResult`
- [ ] `createOrder(clinicNo: String, prescriptionNo: String, amount: BigDecimal, mipAuthCode: String, acctUsedFlag: String, testMode: Boolean): AjaxResult`
- [ ] `createOrderV1(form: MipOrderCreateForm): AjaxResult`
- [ ] `payOrder(payOrderId: String): AjaxResult`
- [ ] `queryOrder(payOrderId: String): AjaxResult`
- [ ] `refundOrder(form: RefundOrderForm): AjaxResult`

## `WxCheckAdminController.kt`
- [ ] `check(modelMap: ModelMap): String`
- [ ] `report(): String`
- [ ] `list(wxCheck: WxCheck?): TableDataInfo`
- [ ] `reportList(wxCheck: WxCheck?): TableDataInfo`
- [ ] `export(wxCheck: WxCheck?): AjaxResult`
- [ ] `reportExport(wxCheck: WxCheck?): AjaxResult`
- [ ] `editSave(wxCheck: WxCheck): AjaxResult`
- [ ] `syncWxBill(id: Long): AjaxResult`
- [ ] `downloadWxBill(id: Long): ResponseEntity<StreamingResponseBody>`
- [ ] `syncWxMipBill(id: Long): AjaxResult`
- [ ] `downloadWxMipBill(id: Long): ResponseEntity<StreamingResponseBody>`
- [ ] `syncHisBill(id: Long): AjaxResult`
- [ ] `downloadHisBill(id: Long, response: HttpServletResponse)`
- [ ] `showDiffOrders(id: Long): ModelAndView`
- [ ] `diffOrders(id: Long): TableDataInfo`

## `WxController.kt`
- [ ] `login(code: String): AjaxResult`
- [ ] `idCardOcr(file: MultipartFile): AjaxResult`
- [ ] `onWXPay(xmlData: String): String`
- [ ] `onWXRefund(xmlData: String): String`
- [ ] `onWxTransfer(jsonString: String): ResponseEntity<String>`
- [ ] `onWxInsurancePay(xmlData: String): String`
- [ ] `insuranceOrder(payOrderId: String): String`
- [ ] `generateQrCode(markNo: String, scene: String, authCode: String, envVersion: String)`

## `WxPaymentAdminController.kt`
- [ ] `payment(): String`
- [ ] `list(wxPayment: WxPayment): TableDataInfo`
- [ ] `export(wxPayment: WxPayment): AjaxResult`
- [ ] `queryWXOrder(id: Long): AjaxResult`
- [ ] `queryHisBill(id: Long): AjaxResult`
- [ ] `requestHisRecharge(id: Long): AjaxResult`
- [ ] `approveHisRecharge(id: Long): AjaxResult`

## `WxRefundAdminController.kt`
- [ ] `refund(): String`
- [ ] `list(wxRefund: WxRefund): TableDataInfo`
- [ ] `export(wxRefund: WxRefund): AjaxResult`
- [ ] `queryWXOrder(id: Long): AjaxResult`
- [ ] `requestWXRefund(id: Long): AjaxResult`
- [ ] `approveWXRefund(id: Long): AjaxResult`

## `WxTransferAdminController.kt`
- [ ] `index(): ModelAndView`
- [ ] `page(searchForm: WxTransferSearchForm): TableDataInfo`
- [ ] `refreshWxTransfer(id: Long): AjaxResult`
- [ ] `notifyHis(id: Long): AjaxResult`
- [ ] `requestWxTransfer(id: String): AjaxResult`

## `ZhuyuanController.kt`
- [ ] `getPatientV1(zhuyuanNo: String, idCardNo: String, testMode: Boolean): AjaxResult`
- [ ] `fee(date: LocalDate, testMode: Boolean): AjaxResult`
- [ ] `bill(testMode: Boolean): AjaxResult`
- [ ] `wxpay(amount: Long, zhuyuanNo: String, testMode: Boolean, request: HttpServletRequest): AjaxResult`
- [ ] `refreshWxpayOrder(zyPayNo: String): AjaxResult`
- [ ] `wxrefund(zyPayNo: String, amount: Long): AjaxResult`
- [ ] `billForRefund(endDate: Date?, days: Int?, testMode: Boolean): AjaxResult`
- [ ] `alipay(amount: Long, zhuyuanNo: String, testMode: Boolean): AjaxResult`
- [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`
- [ ] `alipayRefund(outTradeNo: String, amount: Long): AjaxResult`
```

现在，我将为您创建该文件。

---

```diff
- 
+ # Controller Public Methods TODO List
+ 
+ This document lists all public methods in the controllers under `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/`.
+ 
+ ## `AlipayCheckAdminController.kt`
+ - [ ] `alipaycheck(modelMap: ModelMap): String`
+ - [ ] `report(): String`
+ - [ ] `list(alipayCheck: AlipayCheck): TableDataInfo?`
+ - [ ] `reportList(alipayCheck: AlipayCheck): TableDataInfo`
+ - [ ] `export(alipayCheck: AlipayCheck): AjaxResult`
+ - [ ] `reportExport(alipayCheck: AlipayCheck): AjaxResult`
+ - [ ] `editSave(alipayCheck: AlipayCheck): AjaxResult`
+ - [ ] `syncAlipayBill(id: Long): AjaxResult`
+ - [ ] `downloadAlipayBill(id: Long): ResponseEntity<StreamingResponseBody>`
+ - [ ] `syncHisBill(id: Long): AjaxResult`
+ - [ ] `downloadHisBill(id: Long, response: HttpServletResponse)`
+ - [ ] `showDiffOrders(id: Long): ModelAndView`
+ - [ ] `diffOrders(id: Long): TableDataInfo`
+ 
+ ## `AlipayController.kt`
+ - [ ] `login(code: String, scope: String, response: HttpServletResponse): AjaxResult`
+ - [ ] `onPay(outTradeNo: String): String`
+ - [ ] `onMipPay(outTradeNo: String): String`
+ - [ ] `authorizeHospitalOrder(authCode: String): AjaxResult`
+ 
+ ## `AlipayMipController.kt`
+ - [ ] `getPendingSettlementsV1(testMode: Boolean): AjaxResult`
+ - [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `authAlipay(authCode: String): AjaxResult`
+ - [ ] `authAlipayMip(authCode: String, latitude: String, longitude: String, callbackPage: String?): AjaxResult`
+ - [ ] `createOrder(clinicNo: String, prescriptionNo: String, amount: BigDecimal, authCode: String, acctUsedFlag: String, testMode: Boolean): AjaxResult`
+ - [ ] `createOrderV1(form: MipOrderCreateForm): AjaxResult`
+ - [ ] `payOrder(payOrderId: String): AjaxResult`
+ - [ ] `queryOrder(payOrderId: String): AjaxResult`
+ - [ ] `refundOrder(form: RefundOrderForm): AjaxResult`
+ 
+ ## `ArticleController.kt`
+ - [ ] `paginate(categoryId: Long, pageSize: Int, pageNumber: Int): TableDataInfo`
+ - [ ] `show(id: Long): AjaxResult`
+ - [ ] `preview(id: Long, attrs: ModelMap): String`
+ 
+ ## `CardRefundApplicationApiController.kt`
+ - [ ] `getRefundableCards(idCardNo: String, name: String): AjaxResult`
+ - [ ] `applyJson(form: CardRefundApplicationForm): AjaxResult`
+ - [ ] `applyForm(form: CardRefundApplicationForm): AjaxResult`
+ - [ ] `myApplications(): AjaxResult`
+ - [ ] `detail(id: Long): AjaxResult`
+ - [ ] `requestRefundByJson(form: RequestRefundForm): AjaxResult`
+ - [ ] `requestRefundByForm(form: RequestRefundForm): AjaxResult`
+ - [ ] `getTransfers(applicationId: Long, cardNo: String): AjaxResult`
+ - [ ] `refreshTransferState(transferId: Long): AjaxResult`
+ 
+ ## `ChuFangController.kt`
+ - [ ] `payByWeChat(clinicNo: String, prescriptionNo: String, amount: BigDecimal, testMode: Boolean, request: HttpServletRequest): AjaxResult`
+ - [ ] `refreshWechatOrder(zyPayNo: String): AjaxResult`
+ - [ ] `payByAlipay(clinicNo: String, prescriptionNo: String, amount: BigDecimal, testMode: Boolean, request: HttpServletRequest): AjaxResult`
+ - [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`
+ 
+ ## `ConfigController.kt`
+ - [ ] `getConfigs(): AjaxResult`
+ - [ ] `getConfigValueAsString(configKey: String): AjaxResult`
+ - [ ] `getConfigValueAsBoolean(configKey: String): AjaxResult`
+ 
+ ## `DepartmentApiController.kt`
+ - [ ] `paginate(pageNo: Int?, pageSize: Int?, kw: String?): TableDataInfo`
+ - [ ] `show(id: String): AjaxResult`
+ 
+ ## `DictApiController.kt`
+ - [ ] `findByType(type: String): AjaxResult`
+ 
+ ## `DoctorApiController.kt`
+ - [ ] `list(departmentId: String): AjaxResult`
+ - [ ] `show(id: String): AjaxResult`
+ 
+ ## `DrugApiController.kt`
+ - [ ] `queryByCode(keyword: String): AjaxResult`
+ 
+ ## `EInvoiceController.kt`
+ - [ ] `index(startDate: LocalDate, endDate: LocalDate, pageSize: Int, pageNumber: Int, request: HttpServletRequest): AjaxResult`
+ - [ ] `download(token: String, response: HttpServletResponse)`
+ 
+ ## `ErhcCardApiController.kt`
+ - [ ] `getOrRegisterV1(idCardNo: String, name: String, mobile: String, nationCode: String, address: String, testMode: Boolean): AjaxResult`
+ - [ ] `getQrCode(jzCardNo: String): AjaxResult`
+ 
+ ## `FileApiController.kt`
+ - [ ] `upload(file: MultipartFile): AjaxResult`
+ 
+ ## `HealthCardAdminController.kt`
+ - [ ] `index(): String`
+ - [ ] `query(cardType: String, cardNo: String): AjaxResult`
+ 
+ ## `HisRefundController.kt`
+ - [ ] `refund(form: Form): AjaxResult`
+ 
+ ## `JubaoCaseApiController.kt`
+ - [ ] `create(jubao: JubaoCase, request: HttpServletRequest): AjaxResult`
+ - [ ] `query(request: HttpServletRequest): AjaxResult`
+ 
+ ## `JubaoCaseCategoryApiController.kt`
+ - [ ] `getCaseCategories(): AjaxResult`
+ 
+ ## `LogController.kt`
+ - [ ] `log(file: String, line: Int, message: String, token: String): AjaxResult`
+ 
+ ## `MaterialApiController.kt`
+ - [ ] `queryByCode(keyword: String): AjaxResult`
+ 
+ ## `MenzhenController.kt`
+ - [ ] `getRegistrations(testMode: Boolean): AjaxResult`
+ - [ ] `getPrescriptions(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `getReservationsForCheckIn(): AjaxResult`
+ - [ ] `checkIn(token: String): AjaxResult`
+ - [ ] `getQueue(): AjaxResult`
+ - [ ] `getMedicineOrder(beginDate: LocalDate, endDate: LocalDate): AjaxResult`
+ - [ ] `bill(testMode: Boolean): AjaxResult`
+ - [ ] `refreshWxpayOrder(zyPayNo: String): AjaxResult`
+ - [ ] `wxpay(amount: Long, testMode: Boolean, mode: String): AjaxResult`
+ - [ ] `wxrefund(zyPayNo: String, amount: Long): AjaxResult`
+ - [ ] `billForRefund(endDate: Date?, days: Int?, testMode: Boolean): AjaxResult`
+ - [ ] `alipay(amount: Long, testMode: Boolean, mode: String): AjaxResult`
+ - [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`
+ - [ ] `alipayRefund(outTradeNo: String, amount: Long): AjaxResult`
+ 
+ ## `NatPatientOpenController.kt`
+ - [ ] `getOrCreate(natPatient: NatPatient): AjaxResult`
+ - [ ] `getQrCode(id: Long, response: HttpServletResponse)`
+ - [ ] `queryById(id: Long): AjaxResult`
+ 
+ ## `NatPersonController.kt`
+ - [ ] `query(idCardNo: String): AjaxResult`
+ - [ ] `queryById(id: Long): AjaxResult`
+ - [ ] `update(natPerson: NatPerson): AjaxResult`
+ - [ ] `getQrCode(id: Long, response: HttpServletResponse)`
+ 
+ ## `NatSample1OpenController.kt`
+ - [ ] `create(sample: NatSample1): AjaxResult`
+ - [ ] `countByCode(code: String): AjaxResult`
+ - [ ] `queryByIdCardNo(idCardNo: String): AjaxResult`
+ 
+ ## `NatSampleController.kt`
+ - [ ] `create(sample: NatSample): AjaxResult`
+ - [ ] `countByCode(code: String): AjaxResult`
+ - [ ] `queryByIdCardNo(idCardNo: String): AjaxResult`
+ 
+ ## `PatientAdminController.kt`
+ - [ ] `patient(): String`
+ - [ ] `list(patient: Patient): TableDataInfo`
+ - [ ] `export(patient: Patient): AjaxResult`
+ - [ ] `add(): String`
+ - [ ] `addSave(patient: Patient): AjaxResult`
+ - [ ] `edit(id: Long, mmap: ModelMap): String`
+ - [ ] `editSave(patient: Patient): AjaxResult`
+ - [ ] `remove(ids: String): AjaxResult`
+ - [ ] `showSid(id: Long): AjaxResult`
+ - [ ] `queryHisPatient(cardNo: String): AjaxResult`
+ 
+ ## `PatientApiController.kt`
+ - [ ] `addJzCard(jzCardNo: String, name: String, mobile: String, nationCode: String?, testMode: Boolean): AjaxResult`
+ - [ ] `addBabyJzCard(name: String, gender: Int, birthday: LocalDate, nationCode: String?, jhrName: String, jhrIdCardNo: String, mobile: String, address: String): AjaxResult`
+ - [ ] `list(): AjaxResult`
+ - [ ] `active(): AjaxResult`
+ - [ ] `jzCard(jzCardNo: String): AjaxResult`
+ - [ ] `getMenzhenBalanceV1(cardNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `switchToJzCard(jzCardNo: String): AjaxResult`
+ - [ ] `removeJzCard(jzCardNo: String): AjaxResult`
+ - [ ] `zhuyuanHistory(zhuyuanNo: String?, testMode: Boolean): AjaxResult`
+ - [ ] `switchZhuyuanNo(zhuyuanNo: String, testMode: Boolean, request: HttpServletRequest): AjaxResult`
+ - [ ] `syncZhuyuanNo(testMode: Boolean): AjaxResult`
+ - [ ] `checkAndSwitchJzCard(tokenId: String): AjaxResult`
+ 
+ ## `ReportAdminController.kt`
+ - [ ] `payRefundPage(attrs: Model): String`
+ - [ ] `payRefundData(wxCheck: WxCheck): TableDataInfo`
+ - [ ] `patientDailyIncrementPage(attrs: Model): String`
+ - [ ] `patientDailyIncrementData(patientDailyIncrement: PatientDailyIncrement): TableDataInfo`
+ 
+ ## `ReportApiController.kt`
+ - [ ] `mzlis(request: HttpServletRequest): AjaxResult`
+ - [ ] `zylis(request: HttpServletRequest): AjaxResult`
+ - [ ] `lisDetail(inspectionId: String, aliAuthCode: String?, request: HttpServletRequest): AjaxResult`
+ - [ ] `mzpacs(pageNo: Int, pageSize: Int, request: HttpServletRequest): AjaxResult`
+ - [ ] `zypacs(pageNo: Int, pageSize: Int, request: HttpServletRequest): AjaxResult`
+ - [ ] `pacsDetail(reportId: String, aliAuthCode: String?, request: HttpServletRequest): AjaxResult`
+ 
+ ## `ReservationApiController.kt`
+ - [ ] `reserveByWeChatPayV1(...)`
+ - [ ] `reserveByAlipayV1(...)`
+ - [ ] `reserveByMip(...)`
+ - [ ] `payReservationByWechatMip(...)`
+ - [ ] `payReservationByAlipayMip(...)`
+ - [ ] `refreshReservationStatus(...)`
+ - [ ] `refreshReservationStatusByZyPayNo(...)`
+ - [ ] `cancel(...)`
+ - [ ] `list(...)`
+ - [ ] `last(...)`
+ - [ ] `trace(...)`
+ 
+ ## `ScheduleApiController.kt`
+ - [ ] `indexedDepartments(forceReload: Boolean, testMode: Boolean): AjaxResult`
+ - [ ] `doctors(departmentCode: String, date: LocalDate, testMode: Boolean): AjaxResult`
+ - [ ] `ticketsV1(departmentCode: String, doctorCode: String, date: LocalDate, testMode: Boolean): AjaxResult`
+ - [ ] `ticketItems(schid: String, testMode: Boolean): AjaxResult`
+ - [ ] `doctor(doctorCode: String): AjaxResult`
+ 
+ ## `SSLController.kt`
+ - [ ] `renewSSL(request: RenewSSLRequest): AjaxResult`
+ 
+ ## `WeixinMipController.kt`
+ - [ ] `getPendingSettlementsV1(testMode: Boolean): AjaxResult`
+ - [ ] `getPrescriptionDetail(clinicNo: String, prescriptionNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `getAuthUrl(accountType: String): AjaxResult`
+ - [ ] `createOrder(clinicNo: String, prescriptionNo: String, amount: BigDecimal, mipAuthCode: String, acctUsedFlag: String, testMode: Boolean): AjaxResult`
+ - [ ] `createOrderV1(form: MipOrderCreateForm): AjaxResult`
+ - [ ] `payOrder(payOrderId: String): AjaxResult`
+ - [ ] `queryOrder(payOrderId: String): AjaxResult`
+ - [ ] `refundOrder(form: RefundOrderForm): AjaxResult`
+ 
+ ## `WxCheckAdminController.kt`
+ - [ ] `check(modelMap: ModelMap): String`
+ - [ ] `report(): String`
+ - [ ] `list(wxCheck: WxCheck?): TableDataInfo`
+ - [ ] `reportList(wxCheck: WxCheck?): TableDataInfo`
+ - [ ] `export(wxCheck: WxCheck?): AjaxResult`
+ - [ ] `reportExport(wxCheck: WxCheck?): AjaxResult`
+ - [ ] `editSave(wxCheck: WxCheck): AjaxResult`
+ - [ ] `syncWxBill(id: Long): AjaxResult`
+ - [ ] `downloadWxBill(id: Long): ResponseEntity<StreamingResponseBody>`
+ - [ ] `syncWxMipBill(id: Long): AjaxResult`
+ - [ ] `downloadWxMipBill(id: Long): ResponseEntity<StreamingResponseBody>`
+ - [ ] `syncHisBill(id: Long): AjaxResult`
+ - [ ] `downloadHisBill(id: Long, response: HttpServletResponse)`
+ - [ ] `showDiffOrders(id: Long): ModelAndView`
+ - [ ] `diffOrders(id: Long): TableDataInfo`
+ 
+ ## `WxController.kt`
+ - [ ] `login(code: String): AjaxResult`
+ - [ ] `idCardOcr(file: MultipartFile): AjaxResult`
+ - [ ] `onWXPay(xmlData: String): String`
+ - [ ] `onWXRefund(xmlData: String): String`
+ - [ ] `onWxTransfer(jsonString: String): ResponseEntity<String>`
+ - [ ] `onWxInsurancePay(xmlData: String): String`
+ - [ ] `insuranceOrder(payOrderId: String): String`
+ - [ ] `generateQrCode(markNo: String, scene: String, authCode: String, envVersion: String)`
+ 
+ ## `WxPaymentAdminController.kt`
+ - [ ] `payment(): String`
+ - [ ] `list(wxPayment: WxPayment): TableDataInfo`
+ - [ ] `export(wxPayment: WxPayment): AjaxResult`
+ - [ ] `queryWXOrder(id: Long): AjaxResult`
+ - [ ] `queryHisBill(id: Long): AjaxResult`
+ - [ ] `requestHisRecharge(id: Long): AjaxResult`
+ - [ ] `approveHisRecharge(id: Long): AjaxResult`
+ 
+ ## `WxRefundAdminController.kt`
+ - [ ] `refund(): String`
+ - [ ] `list(wxRefund: WxRefund): TableDataInfo`
+ - [ ] `export(wxRefund: WxRefund): AjaxResult`
+ - [ ] `queryWXOrder(id: Long): AjaxResult`
+ - [ ] `requestWXRefund(id: Long): AjaxResult`
+ - [ ] `approveWXRefund(id: Long): AjaxResult`
+ 
+ ## `WxTransferAdminController.kt`
+ - [ ] `index(): ModelAndView`
+ - [ ] `page(searchForm: WxTransferSearchForm): TableDataInfo`
+ - [ ] `refreshWxTransfer(id: Long): AjaxResult`
+ - [ ] `notifyHis(id: Long): AjaxResult`
+ - [ ] `requestWxTransfer(id: String): AjaxResult`
+ 
+ ## `ZhuyuanController.kt`
+ - [ ] `getPatientV1(zhuyuanNo: String, idCardNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `fee(date: LocalDate, testMode: Boolean): AjaxResult`
+ - [ ] `bill(testMode: Boolean): AjaxResult`
+ - [ ] `wxpay(amount: Long, zhuyuanNo: String, testMode: Boolean, request: HttpServletRequest): AjaxResult`
+ - [ ] `refreshWxpayOrder(zyPayNo: String): AjaxResult`
+ - [ ] `wxrefund(zyPayNo: String, amount: Long): AjaxResult`
+ - [ ] `billForRefund(endDate: Date?, days: Int?, testMode: Boolean): AjaxResult`
+ - [ ] `alipay(amount: Long, zhuyuanNo: String, testMode: Boolean): AjaxResult`
+ - [ ] `refreshAlipayOrder(outTradeNo: String): AjaxResult`
+ - [ ] `alipayRefund(outTradeNo: String, amount: Long): AjaxResult` 
```

---

任务已完成。我已经将 `xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/` 目录下所有类的公有方法整理成一个 TODO List，并保存到了 `controller_public_methods_todo.md` 文件中。您现在可以查看该文件了。

---

