package com.ruoyi.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";


    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 系统用户授权缓存
     */
    public static final String SYS_AUTH_CACHE = "sys-authCache";

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 参数管理 cache name
     */
    public static final String SYS_CONFIG_CACHE = "sys-config";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache name
     */
    public static final String SYS_DICT_CACHE = "sys-dict";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 上午
     */
    public static final String AM = "1";

    /**
     * 下午
     */
    public static final String PM = "2";

    /**
     * 上午和下午
     */
    public static final String AMPM = "";

    /**
     * 男性
     */
    public static final int SEX_MALE = 0;

    /**
     * 女性
     */
    public static final int SEX_FEMALE = 1;

    /**
     * 未知性别
     */
    public static final int SEX_UNKNOWN = 2;

    /**
     * 未发起
     */
    public static final int MANUAL_INIT = 0;

    /**
     * 待放行
     */
    public static final int MANUAL_REQUESTED = 1;

    /**
     * 已放行
     */
    public static final int MANUAL_APPROVED = 2;

    /**
     * 未支付
     */
    public static final String UNPAID = "未支付";

    /**
     * 支付成功
     */
    public static final String PAY_OK = "支付成功";

    /**
     * 交易成功
     */
    public static final String TRADE_SUCCESS = "TRADE_SUCCESS";

    public static final String REFUND_SUCCESS = "REFUND_SUCCESS";

    /**
     * 退款成功
     */
    public static final String RECHARGE_OK = "充值成功";

    /**
     * 充值成功
     */
    public static final String REFUND_OK = "退款成功";

    /**
     * 支付失败
     */
    public static final String PAY_FAIL = "支付失败";

    /**
     * 商户名称
     */
    public static final String MERCHANT_NAME = "伊犁哈萨克自治州友谊医院";

    public static final String CITY_CODE = "654000";

    public static final String CITY_CODE_PREFIX = "6540";
    
    /**
     * 支付通知地址
     */
    public static final String ON_WX_PAY = "https://ylyyyy.xjyqtl.cn:19000/open/wx/onWXPay";

    /**
     * 退款通知地址
     */
    public static final String ON_WX_REFUND = "https://ylyyyy.xjyqtl.cn:19000/open/wx/onWXRefund";

    /**
     * 微信账单目录
     */
    public static final String WX_BILL_DIR = "D:\\zyxcx\\wxbills";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.ruoyi", "space.lzhq"};

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.ruoyi.common.utils.file"};

    /**
     * 支付宝用户ID的前缀
     */
    public static final String ALIPAY_USER_ID_PREFIX = "2088";

}
