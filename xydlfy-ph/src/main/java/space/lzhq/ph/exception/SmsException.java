package space.lzhq.ph.exception;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 短信服务异常
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SmsException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String code;

    public SmsException(String message) {
        super(message);
    }

    public SmsException(String code, String message) {
        super(message);
        this.code = code;
    }

    public SmsException(String message, Throwable cause) {
        super(message, cause);
    }

    public SmsException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
} 