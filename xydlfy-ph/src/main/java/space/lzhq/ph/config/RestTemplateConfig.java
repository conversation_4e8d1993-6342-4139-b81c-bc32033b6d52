package space.lzhq.ph.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {
    
    @Autowired
    private SmsConfig smsConfig;
    
    /**
     * 配置RestTemplate Bean
     * 只有在容器中没有RestTemplate Bean时才创建
     */
    @Bean
    @ConditionalOnMissingBean(RestTemplate.class)
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(smsConfig.getConnectTimeout());
        factory.setReadTimeout(smsConfig.getReadTimeout());
        
        return new RestTemplate(factory);
    }
} 