package com.ruoyi;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import java.io.PrintStream;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(
        scanBasePackages = {"com.ruoyi", "space.lzhq.ph"},
        exclude = {DataSourceAutoConfiguration.class}
)
@MapperScan(value = {"com.ruoyi.*.mapper", "space.lzhq.ph.mapper"})
public class RuoYiApplication {

    private static final Logger logger = LoggerFactory.getLogger(RuoYiApplication.class);

    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(RuoYiApplication.class, args);
        System.out.println("""
                (♥◠‿◠)ﾉﾞ  若依启动成功   ლ(´ڡ`ლ)ﾞ \s
                 .-------.       ____     __       \s
                 |  _ _   \\      \\   \\   /  /   \s
                 | ( ' )  |       \\  _. /  '      \s
                 |(_ o _) /        _( )_ .'        \s
                 | (_,_).' __  ___(_ o _)'         \s
                 |  |\\ \\  |  ||   |(_,_)'        \s
                 |  | \\ `'   /|   `-'  /          \s
                 |  |  \\    /  \\      /          \s
                 ''-'   `'-'    `-..-'              """);

        System.setOut(new PrintStream(System.out) {
            @Override
            public void println(String x) {
                logger.info(x);
            }
        });
    }
}
