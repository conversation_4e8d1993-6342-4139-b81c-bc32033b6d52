package space.lzhq.ph.controller

import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.github.binarywang.wxpay.service.WxPayService
import com.ruoyi.common.annotation.Excel
import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import com.ruoyi.common.utils.spring.SpringUtils
import jakarta.servlet.http.HttpServletResponse
import org.apache.shiro.authz.annotation.Logical
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.io.file.FileUtil
import org.mospital.common.IdUtil
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.CheckResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.ModelAndView
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.common.Values.BANK_PAY_TYPE_JHWX
import space.lzhq.ph.domain.WxCheck
import space.lzhq.ph.domain.WxCheckReport
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.isPay
import space.lzhq.ph.service.IWxCheckService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 对账Controller
 *
 * <AUTHOR>
 * @date 2020-06-09
 */
@Controller
@RequestMapping("/ph/check")
class WxCheckAdminController : BaseController() {

    companion object {
        private val log: org.dromara.hutool.log.Log = org.dromara.hutool.log.Log.get()
    }

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    @RequiresPermissions("ph:check:view")
    @GetMapping
    fun check(modelMap: ModelMap): String {
        modelMap.addAttribute("user", sysUser);
        return "ph/check/check"
    }

    @RequiresPermissions("ph:check:reportView")
    @GetMapping("/report")
    fun report(): String {
        return "ph/check/report"
    }

    /**
     * 查询对账列表
     */
    @RequiresPermissions("ph:check:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:check:reportList")
    @PostMapping("/reportList")
    @ResponseBody
    fun reportList(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list: List<WxCheck> = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list).apply { rows = list.map(::WxCheckReport) }
    }

    /**
     * 导出对账列表
     */
    @RequiresPermissions("ph:check:export")
    @Log(title = "对账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxCheck: WxCheck?): AjaxResult {
        startOrderBy()
        val list = wxCheckService.selectWxCheckList(wxCheck)
        val util = ExcelUtil(WxCheck::class.java)
        return util.exportEasyExcel(list, "微信支付对账单")
    }

    @RequiresPermissions("ph:check:reportExport")
    @Log(title = "对账", businessType = BusinessType.EXPORT)
    @PostMapping("/reportExport")
    @ResponseBody
    fun reportExport(wxCheck: WxCheck?): AjaxResult {
        startOrderBy()
        val list: List<WxCheckReport> = wxCheckService.selectWxCheckList(wxCheck).map(::WxCheckReport)
        val util = ExcelUtil(WxCheckReport::class.java)
        return util.exportEasyExcel(list, "微信支付日报表")
    }

    @RequiresPermissions("ph:check:list")
    @Log(title = "对账", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(wxCheck: WxCheck): AjaxResult {
        return toAjax(wxCheckService.updateWxCheck(wxCheck))
    }

    /**
     * 同步微信账单
     */
    @RequiresPermissions("ph:check:syncWxBill")
    @PostMapping("/syncWxBill/{id}")
    @ResponseBody
    fun syncWxBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val wxPayBillResult: WxPayBillResult? = WeixinExt.downloadAndSaveWxBill(billDate)
        val ok = wxCheckService.syncWxBill(billDate, wxPayBillResult)
        return toAjax(ok)
    }

    /**
     * 下载微信账单
     */
    @RequiresPermissions(
        value = ["ph:check:downloadWxBill", "ph:check:reportDownloadWxBill"],
        logical = Logical.OR
    )
    @GetMapping("/downloadWxBill/{id}")
    @ResponseBody
    fun downloadWxBill(@PathVariable id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadBillFile(billDate)
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("text/csv"))
            .contentLength(billFile.length())
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${billFile.name}")
            .body(StreamingResponseBody {
                try {
                    FileUtil.writeToStream(billFile, it)
                } catch (e: Exception) {
                    log.error(e.message, e)
                    it.write("Unknown error occurred".toByteArray())
                }
            })
    }

    /**
     * 同步微信医保账单
     */
    @RequiresPermissions("ph:check:syncWxMipBill")
    @PostMapping("/syncWxMipBill/{id}")
    @ResponseBody
    fun syncWxMipBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
        val ok = wxCheckService.syncWxMipBill(billDate, billFile)
        return toAjax(ok)
    }

    /**
     * 下载微信医保账单
     */
    @RequiresPermissions(
        value = ["ph:check:downloadWxMipBill", "ph:check:reportDownloadWxMipBill"],
        logical = Logical.OR
    )
    @GetMapping("/downloadWxMipBill/{id}")
    @ResponseBody
    fun downloadWxMipBill(@PathVariable id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("text/csv"))
            .contentLength(billFile.length())
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=weixin_yibao_bill_${billDate}.csv")
            .body(StreamingResponseBody {
                try {
                    FileUtil.writeToStream(billFile, it)
                } catch (e: Exception) {
                    log.error(e.message, e)
                    it.write("Unknown error occurred".toByteArray())
                }
            })
    }

    /**
     * 同步HIS账单
     */
    @RequiresPermissions("ph:check:syncHisBill")
    @PostMapping("/syncHisBill/{id}")
    @ResponseBody
    fun syncHisBill(@PathVariable id: Long): AjaxResult {
        val checkResponse: CheckResponse = DongruanService.check(
            date = IdUtil.idToLocalDate(id),
            payType = BANK_PAY_TYPE_JHWX
        )
        return wxCheckService.syncHisBill(IdUtil.idToLocalDate(id), checkResponse)
    }

    data class HisBill(
        @Excel(name = "交易类型")
        val paymentType: String,

        @Excel(name = "支付方式")
        val paymentMethod: String,

        @Excel(name = "病人ID")
        val patientId: String,

        @Excel(name = "交易时间", width = 24.0)
        val orderTime: String,

        @Excel(name = "HIS发票号", width = 32.0)
        val invoiceNo: String,

        @Excel(name = "商户订单号", width = 32.0)
        val wxTradeNo: String,

        @Excel(name = "交易金额")
        val amount: BigDecimal,

        ) {
        companion object {
            fun convertFrom(bill: CheckResponse.Item): HisBill = HisBill(
                paymentType = if (bill.isRecharge()) "充值" else "退款",
                paymentMethod = "微信支付",
                patientId = bill.cardNo,
                orderTime = bill.transTime,
                invoiceNo = bill.invoiceNo,
                wxTradeNo = bill.bankTransNo,
                amount = bill.amount
            )
        }

        fun isMipOrder(): Boolean = wxTradeNo.isBlank() && invoiceNo.startsWith("M")
    }

    /**
     * 下载HIS账单
     */
    @RequiresPermissions("ph:check:downloadHisBill")
    @GetMapping("/downloadHisBill/{id}")
    @ResponseBody
    fun downloadHisBill(@PathVariable id: Long, response: HttpServletResponse) {
        val checkResponse: CheckResponse = DongruanService.check(
            date = IdUtil.idToLocalDate(id),
            payType = BANK_PAY_TYPE_JHWX
        )
        return if (checkResponse.isOk()) {
            val bills: List<CheckResponse.Item> = checkResponse.items
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=hisbill_${id}.xlsx")
            response.contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ExcelUtil(HisBill::class.java).apply {
                this.init(
                    bills.filterNot { it.isMipOrder() }.map(HisBill.Companion::convertFrom),
                    "Sheet1",
                    "",
                    Excel.Type.EXPORT
                )
            }.exportExcel(response)
        } else {
            response.sendError(500, checkResponse.msg)
        }
    }

    data class DiffOrder(
        var orderType: String,
        var patientId: String,
        var orderNo: String,
        var wxOrderTime: String,
        var hisOrderTime: String,
        var wxAmount: BigDecimal,
        var hisAmount: BigDecimal,
        var diffAmount: BigDecimal,
    )

    /**
     * 显示错单
     */
    @RequiresPermissions("ph:check:showDiffOrders")
    @GetMapping("/showDiffOrders/{id}")
    fun showDiffOrders(@PathVariable id: Long): ModelAndView {
        val billDate = IdUtil.idToLocalDate(id)
        return ModelAndView(
            "ph/check/diffOrders",
            mapOf("id" to id, "billDate" to billDate.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)))
        )
    }

    /**
     * 显示错单
     */
    @RequiresPermissions("ph:check:showDiffOrders")
    @PostMapping("/diffOrders/{id}")
    @ResponseBody
    fun diffOrders(@PathVariable id: Long): TableDataInfo {
        val billDate: LocalDate = IdUtil.idToLocalDate(id)
        val zero: BigDecimal = BigDecimal.ZERO.setScale(2)

        val appId = SpringUtils.getBean(WxPayService::class.java).config.appId
        val wxPayBillResult: WxPayBillResult = WeixinExt.downloadAndSaveWxBill(billDate)
            ?: throw Exception("获取微信账单失败")
        val wxBills: List<WxPayBillInfo> = wxPayBillResult.billInfoList.filter { appId == it.appId }

        val checkResponse: CheckResponse = DongruanService.check(
            date = IdUtil.idToLocalDate(id),
            payType = BANK_PAY_TYPE_JHWX
        )
        if (!checkResponse.isOk()) {
            throw Exception(checkResponse.msg)
        }
        val hisBills: List<CheckResponse.Item> = checkResponse.items.filterNot { it.isMipOrder() }

        val diffOrdersMap: MutableMap<String, DiffOrder> = mutableMapOf()
        wxBills.forEach {
            val isPay: Boolean = it.isPay()
            val orderType: String = if (isPay) "充值" else "退款"
            val orderNo: String =
                if (isPay) {
                    it.outTradeNo
                } else {
                    it.outRefundNo
                }
            val wxAmount: BigDecimal =
                if (isPay) {
                    it.totalFee.toBigDecimal()
                } else {
                    it.settlementRefundFee.toBigDecimal()
                }
            val wxOrderTime: String = it.tradeTime
            diffOrdersMap[orderNo] = DiffOrder(
                orderType = orderType,
                patientId = "",
                orderNo = orderNo,
                wxOrderTime = wxOrderTime,
                hisOrderTime = "",
                wxAmount = wxAmount,
                hisAmount = zero,
                diffAmount = wxAmount
            )
        }

        hisBills.forEach {
            var diffOrder: DiffOrder? = diffOrdersMap[it.bankTransNo]
            if (diffOrder == null) {
                diffOrder = DiffOrder(
                    orderType = when {
                        it.isRecharge() -> "充值"
                        it.isRefund() -> "退款"
                        else -> "其它"
                    },
                    patientId = it.cardNo,
                    orderNo = it.bankTransNo,
                    wxOrderTime = "",
                    hisOrderTime = it.transTime,
                    wxAmount = zero,
                    hisAmount = it.amount.abs(),
                    diffAmount = -it.amount.abs()
                )
            } else {
                diffOrder.apply {
                    this.patientId = it.cardNo
                    this.hisOrderTime = it.transTime
                    this.hisAmount = it.amount.abs()
                    this.diffAmount = this.wxAmount - this.hisAmount
                }
            }
            diffOrdersMap[it.bankTransNo] = diffOrder
        }

        val diffOrders: List<DiffOrder> =
            diffOrdersMap.values.filter { it.diffAmount.compareTo(zero) != 0 }.sortedBy { it.orderType + it.orderNo }

        return getDataTable(diffOrders)
    }
}
