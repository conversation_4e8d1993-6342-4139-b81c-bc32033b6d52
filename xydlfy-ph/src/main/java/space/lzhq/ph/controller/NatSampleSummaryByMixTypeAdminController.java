package space.lzhq.ph.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.dromara.hutool.core.date.DateUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import space.lzhq.ph.domain.NatSampleSummaryByMixType;
import space.lzhq.ph.service.INatSampleSummaryByMixTypeService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/natSampleSummaryByMixType")
public class NatSampleSummaryByMixTypeAdminController extends BaseController {

    private final INatSampleSummaryByMixTypeService service;

    public NatSampleSummaryByMixTypeAdminController(INatSampleSummaryByMixTypeService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:view")
    @GetMapping()
    public String natSample() {
        return "ph/natSampleSummaryByMixType/index";
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NatSampleSummaryByMixType example) {
        if (example.getMinCollectTime() == null) {
            example.setMinCollectTime(DateUtil.beginOfDay(new Date()));
        }
        if (example.getMaxCollectTime() == null) {
            example.setMaxCollectTime(new Date());
        }
        List<NatSampleSummaryByMixType> list = service.summary(
                example.getMinCollectTime(),
                example.getMaxCollectTime()
        );
        return getDataTable(list);
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(NatSampleSummaryByMixType example) {
        if (example.getMinCollectTime() == null) {
            example.setMinCollectTime(DateUtil.beginOfDay(new Date()));
        }
        if (example.getMaxCollectTime() == null) {
            example.setMaxCollectTime(new Date());
        }
        List<NatSampleSummaryByMixType> list = service.summary(
                example.getMinCollectTime(),
                example.getMaxCollectTime()
        );
        ExcelUtil<NatSampleSummaryByMixType> util =
                new ExcelUtil<NatSampleSummaryByMixType>(NatSampleSummaryByMixType.class);
        return util.exportEasyExcel(list, "样品统计表");
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:view1")
    @GetMapping("/page1")
    public String natSample1() {
        return "ph/natSampleSummaryByMixType/index1";
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:list1")
    @PostMapping("/list1")
    @ResponseBody
    public TableDataInfo list1(NatSampleSummaryByMixType example) {
        if (example.getMinCollectTime() == null) {
            example.setMinCollectTime(DateUtil.beginOfDay(new Date()));
        }
        if (example.getMaxCollectTime() == null) {
            example.setMaxCollectTime(new Date());
        }
        List<NatSampleSummaryByMixType> list = service.summary1(
                example.getMinCollectTime(),
                example.getMaxCollectTime()
        );
        return getDataTable(list);
    }

    @RequiresPermissions("ph:natSampleSummaryByMixType:export1")
    @PostMapping("/export1")
    @ResponseBody
    public AjaxResult export1(NatSampleSummaryByMixType example) {
        if (example.getMinCollectTime() == null) {
            example.setMinCollectTime(DateUtil.beginOfDay(new Date()));
        }
        if (example.getMaxCollectTime() == null) {
            example.setMaxCollectTime(new Date());
        }
        List<NatSampleSummaryByMixType> list = service.summary1(
                example.getMinCollectTime(),
                example.getMaxCollectTime()
        );
        ExcelUtil<NatSampleSummaryByMixType> util =
                new ExcelUtil<NatSampleSummaryByMixType>(NatSampleSummaryByMixType.class);
        return util.exportEasyExcel(list, "样品统计表");
    }
}
