package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.NatPatient;
import space.lzhq.ph.service.INatPatientService;

import java.util.List;

/**
 * 人员Controller
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Controller
@RequestMapping("/ph/natPatient")
public class NatPatientAdminController extends BaseController {

    private final INatPatientService natPatientService;

    public NatPatientAdminController(INatPatientService natPatientService) {
        this.natPatientService = natPatientService;
    }

    @RequiresPermissions("ph:natPatient:view")
    @GetMapping()
    public String natPatient() {
        return "ph/natPatient/natPatient";
    }

    /**
     * 查询人员列表
     */
    @RequiresPermissions("ph:natPatient:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NatPatient natPatient) {
        startPage("id desc");
        List<NatPatient> list = natPatientService.selectNatPatientList(natPatient);
        return getDataTable(list);
    }

    /**
     * 导出人员列表
     */
    @RequiresPermissions("ph:natPatient:export")
    @Log(title = "人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(NatPatient natPatient) {
        List<NatPatient> list = natPatientService.selectNatPatientList(natPatient);
        ExcelUtil<NatPatient> util = new ExcelUtil<>(NatPatient.class);
        return util.exportExcel(list, "核酸采集-人员");
    }

    /**
     * 新增人员
     */
    @GetMapping("/add")
    public String add() {
        return "ph/natPatient/add";
    }

    /**
     * 新增保存人员
     */
    @RequiresPermissions("ph:natPatient:add")
    @Log(title = "人员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(NatPatient natPatient) {
        if (!natPatientService.isIdCardNoUnique(natPatient.getIdCardNo(), natPatient.getId())) {
            return error("身份证号已存在");
        }
        return toAjax(natPatientService.insertNatPatient(natPatient));
    }

    /**
     * 修改人员
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        NatPatient natPatient = natPatientService.selectNatPatientById(id);
        mmap.put("natPatient", natPatient);
        return "ph/natPatient/edit";
    }

    /**
     * 修改保存人员
     */
    @RequiresPermissions("ph:natPatient:edit")
    @Log(title = "人员", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(NatPatient natPatient) {
        if (!natPatientService.isIdCardNoUnique(natPatient.getIdCardNo(), natPatient.getId())) {
            return error("身份证号已存在");
        }
        return toAjax(natPatientService.updateNatPatient(natPatient));
    }

    /**
     * 删除人员
     */
    @RequiresPermissions("ph:natPatient:remove")
    @Log(title = "人员", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(natPatientService.deleteNatPatientByIds(ids));
    }
}
