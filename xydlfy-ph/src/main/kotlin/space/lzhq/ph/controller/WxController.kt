package space.lzhq.ph.controller

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult
import com.github.binarywang.wxpay.bean.notify.*
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult
import com.github.binarywang.wxpay.bean.transfer.TransferBillsNotifyResult
import com.github.binarywang.wxpay.service.WxPayService
import com.ruoyi.common.config.ServerConfig
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException
import com.ruoyi.common.exception.file.FileSizeLimitExceededException
import jakarta.validation.constraints.NotBlank
import me.chanjar.weixin.common.error.WxErrorException
import org.dromara.hutool.core.data.id.IdUtil
import org.dromara.hutool.core.exception.ExceptionUtil
import org.dromara.hutool.core.io.file.FileUtil
import org.mospital.dongruan.yibao.DongruanYibaoService
import org.mospital.dongruan.yibao.bean.*
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.FileUploader
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.*

@RestController
class WxController : BaseController() {

    @Autowired
    private lateinit var wxMaService: WxMaService

    @Autowired
    private lateinit var sessionService: ISessionService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    @Autowired
    private lateinit var reservationService: IReservationService

    @Autowired
    private lateinit var traceService: ITraceService

    @Autowired
    private lateinit var wxTransferService: IWxTransferService

    @Autowired
    private lateinit var wxPayService: WxPayService

    @Autowired
    private lateinit var tokenService: ITokenService

    @Autowired
    private lateinit var serverConfig: ServerConfig

    @PostMapping("/open/wx/login")
    fun login(@RequestParam code: String): AjaxResult {
        val code2sessionResult: WxMaJscode2SessionResult = try {
            wxMaService.userService.getSessionInfo(code)
        } catch (e: WxErrorException) {
            return AjaxResult.error(e.error.errorMsg)
        }

        val sid: String = IdUtil.fastUUID()

        sessionService.deleteSessionByOpenId(code2sessionResult.openid)
        val newWxSession = Session().apply {
            this.id = sid
            this.clientType = ClientType.WEIXIN.code
            this.openId = code2sessionResult.openid
            this.unionId = code2sessionResult.unionid ?: ""
            this.sessionKey = code2sessionResult.sessionKey
        }
        sessionService.insertSession(newWxSession)

        response.setHeader(HttpHeaders.AUTHORIZATION, sid)
        return AjaxResult.success(mapOf(HttpHeaders.AUTHORIZATION to sid))
    }

    @PostMapping("/open/wx/idCardOcr")
    @RequireSession(ClientType.WEIXIN)
    fun idCardOcr(file: MultipartFile): AjaxResult {
        return try {
            val fileNameLength = file.originalFilename!!.length
            if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
                throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
            }

            val size = file.size
            if (size > 2 * 1024 * 1024) {
                throw FileSizeLimitExceededException(2)
            }

            val extensionName = FileUploader.getExtension(file)
            val tmpFile = FileUtil.createTempFile("ocr-", ".$extensionName", true)
            file.transferTo(tmpFile)
            val result = WeixinExt.idCardOcr(tmpFile)
            FileUtil.del(tmpFile)
            AjaxResult.success(result)
        } catch (e: Exception) {
            logger.warn("idCardOcr failed", e)
            AjaxResult.error(e.message)
        }
    }

    private fun ok(msg: String = "SUCCESS"): String = WxPayNotifyResponse.success(msg)
    private fun fail(msg: String = "FAIL"): String = WxPayNotifyResponse.fail(msg)

    @PostMapping("/open/wx/onWXPay")
    fun onWXPay(@RequestBody xmlData: String): String {
        try {
            var wxPayService = WeixinExt.getWxPayServiceForMenZhen()
            val wxPayOrderNotifyResult = try {
                wxPayService.parseOrderNotifyResult(xmlData)
            } catch (e: Exception) {
                wxPayService = WeixinExt.getWxPayServiceForZhuYuan()
                wxPayService.parseOrderNotifyResult(xmlData)
            }

            val payment = wxPaymentService.selectWxPaymentByZyPayNo(wxPayOrderNotifyResult.outTradeNo)
                ?: return fail("本地无此订单: ${wxPayOrderNotifyResult.outTradeNo}")

            synchronized("WxPayment#${payment.id}".intern()) {
                if (!payment.wxPayNo.isNullOrBlank()) {
                    // 本地订单状态已更新
                    return ok()
                }

                val wxPayOrderQueryResult = wxPayService.queryOrder(null, wxPayOrderNotifyResult.outTradeNo)
                val updateSuccess = wxPaymentService.updateOnPay(payment, wxPayOrderQueryResult)
                if (!updateSuccess) {
                    return fail("更新订单记录的微信交易状态失败")
                }

                HisExt.recharge(payment.id)

                return ok()
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return fail(ExceptionUtil.stacktraceToOneLineString(e, 100))
        }
    }

    @PostMapping("/open/wx/onWXRefund")
    fun onWXRefund(@RequestBody xmlData: String): String {
        try {
            var wxPayService = WeixinExt.getWxPayServiceForMenZhen()
            val wxPayRefundNotifyResult = try {
                wxPayService.parseRefundNotifyResult(xmlData)
            } catch (e: Exception) {
                wxPayService = WeixinExt.getWxPayServiceForZhuYuan()
                wxPayService.parseRefundNotifyResult(xmlData)
            }

            val zyRefundNo = wxPayRefundNotifyResult.reqInfo.outRefundNo
            val wxRefund = wxRefundService.selectWxRefundByZyRefundNo(zyRefundNo)
                ?: return fail("本地无此订单: 掌医退款单号=$zyRefundNo")
            if (!wxRefund.wxRefundNo.isNullOrBlank()) {
                // 本地订单状态已更新
                return ok()
            }

            val wxPayRefundQueryResult = wxPayService.refundQuery(null, null, zyRefundNo, null)
            val refundRecord: WxPayRefundQueryResult.RefundRecord = wxPayRefundQueryResult.refundRecords[0]
            val ok = wxRefundService.updateOnWXRefund(wxRefund, refundRecord)
            return if (ok) {
                ok()
            } else {
                fail("更新退款记录失败")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return fail(ExceptionUtil.stacktraceToOneLineString(e, 100))
        }
    }

    /**
     * 微信转账通知处理
     *
     * 关于 ResponseEntity<String> 的处理:
     * 1. produces = [MediaType.APPLICATION_JSON_VALUE] 声明此接口产生 JSON 响应
     * 2. 响应头将被设置为 Content-Type: application/json
     * 3. 处理规则:
     *    - 如果返回的 String 已经是合法的 JSON 字符串，则直接返回
     *    - 如果是普通字符串，则会被包装成 JSON 字符串（如 "hello" 变成 "\"hello\""）
     *    - WxPayNotifyV3Response.success/fail 返回的已经是格式化好的 JSON 字符串
     */
    @PostMapping(value = ["/open/wx/onWxTransfer"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun onWxTransfer(@RequestBody @NotBlank(message = "请求数据不能为空") jsonString: String): ResponseEntity<String> {
        return try {
            logger.debug("notify: onWxTransfer: $jsonString")

            // 构建签名头信息，用于验证请求的合法性
            val signatureHeader = SignatureHeader.builder()
                .serial(request.getHeader("Wechatpay-Serial"))
                .signature(request.getHeader("Wechatpay-Signature"))
                .timeStamp(request.getHeader("Wechatpay-Timestamp"))
                .nonce(request.getHeader("Wechatpay-Nonce"))
                .build()
            // 解析通知结果，验证签名
            val notifyResult: TransferBillsNotifyResult =
                wxPayService.parseTransferBillsNotifyV3Result(jsonString, signatureHeader)
            logger.debug("notify: onWxTransfer: ${JacksonKit.writeValueAsString(notifyResult.result)}")

            // 处理业务逻辑
            val outBillNo = notifyResult.result.outBillNo
            val wxTransferOptional = wxTransferService.getOneByOutBillNo(outBillNo)
            if (wxTransferOptional.isPresent) {
                val wxTransfer = wxTransferOptional.get()
                wxTransferService.updateTransferState(wxTransfer)
            } else {
                logger.warn("notify: onWxTransfer: 本地无此订单: $outBillNo")
            }

            // 成功响应：
            // 1. 状态码 200
            // 2. WxPayNotifyV3Response.success 返回符合微信规范的 JSON 字符串
            // 3. SpringBoot 将直接返回这个 JSON 字符串，不会进行额外处理
            ResponseEntity.ok(WxPayNotifyV3Response.success("SUCCESS"))
        } catch (e: Exception) {
            logger.error(e.message, e)
            // 失败响应：
            // 1. 状态码 500
            // 2. WxPayNotifyV3Response.fail 返回包含错误信息的 JSON 字符串
            // 3. SpringBoot 将直接返回这个 JSON 字符串，不会进行额外处理
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(WxPayNotifyV3Response.fail(e.message))
        }
    }

    @PostMapping("/open/wx/onWxInsurancePay", produces = ["application/xml;charset=utf-8"])
    fun onWxInsurancePay(@RequestBody xmlData: String): String {
        logger.debug("onWxInsurancePay: $xmlData")
        val signKey = WeixinMipSetting.ma.mipKey
        return try {
            val insuranceOrderNotifyResult = WxInsuranceOrderNotifyResult.fromXML(xmlData)
            logger.debug("onWxInsurancePay: ${insuranceOrderNotifyResult.medTransId}")

            val mipOrder: MipWxOrder = mipWxOrderService.getOneByOutTradeNo(insuranceOrderNotifyResult.hospOutTradeNo)
                ?: throw throw IllegalStateException("本地无此订单: " + insuranceOrderNotifyResult.hospOutTradeNo)

            val yibaoOrderStatusResponse: Response<QueryOrderResult> = DongruanYibaoService.queryOrder(
                form = QueryOrderForm(
                    clinicNo = mipOrder.clinicNo,
                    payOrderId = mipOrder.payOrderId,
                    personName = mipOrder.patientName,
                    idCardNo = mipOrder.patientIdCardNo,
                ),
                thirdType = ThirdType.WEIXIN,
                extendParams = Request.ExtendParams(appId = WeixinMipSetting.ma.orgAppId),
                env = if (mipOrder.testMode == true) "WeixinTest" else "WeixinProd"
            )
            if (yibaoOrderStatusResponse.isOk() && yibaoOrderStatusResponse.data?.orderStatus == "6") {
                HisExt.settleWeixinMipOrder(mipOrder.hospitalOutTradeNo)
            }

            WxInsuranceOrderNotifyResponse.success(signKey = signKey).toXML()
        } catch (e: Exception) {
            logger.error("onWxInsurancePay 处理异常", e)
            WxInsuranceOrderNotifyResponse.fail(signKey = signKey, returnMsg = e.message.orEmpty()).toXML()
        }
    }

    @GetMapping("/open/wx/insuranceOrder/{payOrderId}")
    fun insuranceOrder(@PathVariable payOrderId: String): String {
        logger.debug("return: onWxInsurancePay: $payOrderId")
        return payOrderId
    }

    /**
     * 生成二维码
     *
     * @param markNo 实体卡号
     * @param scene 场景，当前支持的场景有：
     *              - QRCODE_ZJJF：诊间缴费
     * @param authCode 认证码，用于认证请求，当前固定为 N6Qp8hx2QACDDJd4
     * @param envVersion 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
     */
    @GetMapping("/open/wx/qrCode")
    fun generateQrCode(
        @RequestParam("markNo") markNo: String,
        @RequestParam("scene") scene: String,
        @RequestParam("authCode") authCode: String,
        @RequestParam(name = "envVersion", required = false, defaultValue = "release") envVersion: String
    ) {
        check(authCode == "N6Qp8hx2QACDDJd4") {
            throw IllegalArgumentException("认证码不正确")
        }

        val supportedScenes = arrayOf("QRCODE_ZJJF")
        check(scene in supportedScenes) {
            AjaxResult.error("不支持的场景: $scene")
            throw IllegalArgumentException("不支持的场景: $scene")
        }

        val token = tokenService.createAndSaveZJJFToken(markNo)
        val qrCodeFile = wxMaService.qrcodeService.createWxaCodeUnlimit(
            token.id,
            "",
            serverConfig.qrCodeDir,
            true,
            envVersion,
            430,
            true,
            null,
            false
        )

        response.contentType = "image/jpeg"
        response.setHeader("Cache-Control", "no-store")
        response.outputStream.use {
            FileUtil.writeToStream(qrCodeFile, it)
        }
    }
}
