package space.lzhq.ph.domain;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

public class NatCollection implements Serializable {
    @Serial
    private static final long serialVersionUID = 1176606825495228510L;

    private String id;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 年龄
     */
    private String age;

    /**
     * 性别：1=男，2=女
     */
    private String sex;

    /**
     * 采样时间
     */
    private Date collectTime;

    /**
     * 采样人
     */
    private String collectorName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public String getCollectorName() {
        return collectorName;
    }

    public void setCollectorName(String collectorName) {
        this.collectorName = collectorName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NatCollection)) return false;

        NatCollection that = (NatCollection) o;

        if (getId() != null ? !getId().equals(that.getId()) : that.getId() != null) return false;
        if (getIdCardNo() != null ? !getIdCardNo().equals(that.getIdCardNo()) : that.getIdCardNo() != null)
            return false;
        if (getName() != null ? !getName().equals(that.getName()) : that.getName() != null) return false;
        if (getAge() != null ? !getAge().equals(that.getAge()) : that.getAge() != null) return false;
        if (getSex() != null ? !getSex().equals(that.getSex()) : that.getSex() != null) return false;
        if (getCollectTime() != null ? !getCollectTime().equals(that.getCollectTime()) : that.getCollectTime() != null)
            return false;
        if (getCollectorName() != null ? !getCollectorName().equals(that.getCollectorName()) :
                that.getCollectorName() != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = getId() != null ? getId().hashCode() : 0;
        result = 31 * result + (getIdCardNo() != null ? getIdCardNo().hashCode() : 0);
        result = 31 * result + (getName() != null ? getName().hashCode() : 0);
        result = 31 * result + (getAge() != null ? getAge().hashCode() : 0);
        result = 31 * result + (getSex() != null ? getSex().hashCode() : 0);
        result = 31 * result + (getCollectTime() != null ? getCollectTime().hashCode() : 0);
        result = 31 * result + (getCollectorName() != null ? getCollectorName().hashCode() : 0);
        return result;
    }
}
