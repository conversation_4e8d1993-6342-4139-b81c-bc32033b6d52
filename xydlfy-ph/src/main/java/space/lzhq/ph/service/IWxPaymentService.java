package space.lzhq.ph.service;

import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import org.mospital.dongruan.bean.RechargeResponse;
import org.mospital.dongruan.bean.ReserveResponse;
import org.mospital.dongruan.bean.Response;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.Patient;
import space.lzhq.ph.domain.WxPayment;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 充值记录Service接口
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
public interface IWxPaymentService {
    /**
     * 查询充值记录
     *
     * @param id 充值记录ID
     * @return 充值记录
     */
    WxPayment selectWxPaymentById(Long id);


    /**
     * 查找充值记录
     *
     * @param zyPayNo 掌医订单号
     */
    WxPayment selectWxPaymentByZyPayNo(String zyPayNo);

    /**
     * 查询充值记录列表
     *
     * @param wxPayment 充值记录
     * @return 充值记录集合
     */
    List<WxPayment> selectWxPaymentList(WxPayment wxPayment);

    /**
     * 查询可退款的充值订单
     *
     * @param cardNo        虚拟卡号
     * @param minCreateTime 最小下单时间
     * @param maxCreateTime 最大下单时间
     */
    List<WxPayment> selectListForRefund(String cardNo, Date minCreateTime, Date maxCreateTime);

    Long sumAmount(WxPayment wxPayment);

    /**
     * 新增充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int insertWxPayment(WxPayment wxPayment);

    /**
     * 修改充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    int updateWxPayment(WxPayment wxPayment);

    /**
     * 批量删除充值记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxPaymentByIds(String ids);

    /**
     * 删除充值记录信息
     *
     * @param id 充值记录ID
     * @return 结果
     */
    int deleteWxPaymentById(Long id);

    WxPayment buildWxPayment(ServiceType serviceType, Patient patient, WxPayUnifiedOrderRequest request, boolean testMode);

    /**
     * 创建并保存
     *
     * @param serviceType 服务类型
     * @param patient     门诊患者
     * @param request     微信统一下单
     */
    boolean createAndSave(ServiceType serviceType, Patient patient, WxPayUnifiedOrderRequest request, boolean testMode);

    /**
     * 接收到支付通知时，更新充值记录
     *
     * @param wxPayment             充值记录
     * @param wxPayOrderQueryResult 支付通知
     */
    boolean updateOnPay(WxPayment wxPayment, WxPayOrderQueryResult wxPayOrderQueryResult);

    /**
     * HIS充值后，更新充值记录
     *
     * @param wxPayment        充值记录
     * @param rechargeResponse 充值结果
     */
    boolean updateOnRecharge(WxPayment wxPayment, RechargeResponse rechargeResponse);

    boolean updateOnRecharge(WxPayment wxPayment, Response response);

    boolean updateOnReserve(WxPayment wxPayment, ReserveResponse reserveResponse);

    /**
     * HIS退款后，更新充值记录
     *
     * @param wxPayment    充值记录
     * @param refundAmount 退款金额，以分为单位
     */
    boolean updateOnRefund(WxPayment wxPayment, Long refundAmount);

    /**
     * 计算指定日期的微信支付净流入金额，以分为单位
     */
    Long calculateNetInAmount(LocalDate date);

}
