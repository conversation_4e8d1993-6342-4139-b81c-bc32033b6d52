<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.NatSample1Mapper">

    <resultMap type="space.lzhq.ph.domain.NatSample1" id="NatSample1Result">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="personId" column="person_id"/>
        <result property="personName" column="person_name"/>
        <result property="personDepartment" column="person_department"/>
        <result property="personIdCardNo" column="person_id_card_no"/>
        <result property="personMobile" column="person_mobile"/>
        <result property="personNation" column="person_nation"/>
        <result property="personType" column="person_type"/>
        <result property="personDays" column="person_days"/>
        <result property="personAddress" column="person_address"/>
        <result property="collectorName" column="collector_name"/>
        <result property="collectorMobile" column="collector_mobile"/>
        <result property="collectAddress" column="collect_address"/>
        <result property="collectPosition" column="collect_position"/>
        <result property="collectTime" column="collect_time"/>
        <result property="pushState" column="push_state"/>
        <result property="pushTime" column="push_time"/>
        <result property="pushMsg" column="push_msg"/>
        <result property="mixType" column="mix_type"/>
    </resultMap>

    <sql id="selectNatSample1Vo">
        select id,
               code,
               person_id,
               person_name,
               person_department,
               person_id_card_no,
               person_mobile,
               person_nation,
               person_type,
               person_days,
               person_address,
               collector_name,
               collector_mobile,
               collect_address,
               collect_position,
               collect_time,
               push_state,
               push_time,
               push_msg,
               mix_type
        from ph_nat_sample1
    </sql>

    <select id="selectNatSampleList" parameterType="space.lzhq.ph.domain.NatSample1" resultMap="NatSample1Result">
        <include refid="selectNatSample1Vo"/>
        <where>
            <if test="code != null  and code != ''">and code = #{code}</if>
            <if test="personId != null">and person_id = #{personId}</if>
            <if test="personName != null  and personName != ''">and person_name like concat('%', #{personName}, '%')
            </if>
            <if test="personDepartment != null ">and person_department = #{personDepartment}</if>
            <if test="personIdCardNo != null  and personIdCardNo != ''">and person_id_card_no = #{personIdCardNo}</if>
            <if test="personMobile != null  and personMobile != ''">and person_mobile = #{personMobile}</if>
            <if test="personType != null  and personType != ''">and person_type = #{personType}</if>
            <if test="personDays != null ">and person_days = #{personDays}</if>
            <if test="collectorName != null  and collectorName != ''">and collector_name like concat('%',
                #{collectorName}, '%')
            </if>
            <if test="collectorMobile != null  and collectorMobile != ''">and collector_mobile = #{collectorMobile}</if>
            <if test="params.beginCollectTime != null and params.beginCollectTime != '' and params.endCollectTime != null and params.endCollectTime != ''">
                and collect_time between #{params.beginCollectTime} and #{params.endCollectTime}
            </if>
            <if test="pushState != null ">and push_state = #{pushState}</if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="pushMsg != null  and pushMsg != ''">and push_msg = #{pushMsg}</if>
            <if test="mixType != null">and mix_type = #{mixType}</if>
        </where>
    </select>

    <select id="countCodes" parameterType="space.lzhq.ph.domain.NatSample1" resultType="java.lang.Long">
        select count(distinct code) from ph_nat_sample1
        <where>
            <if test="code != null  and code != ''">and code = #{code}</if>
            <if test="personId != null">and person_id = #{personId}</if>
            <if test="personName != null  and personName != ''">and person_name like concat('%', #{personName}, '%')
            </if>
            <if test="personDepartment != null ">and person_department = #{personDepartment}</if>
            <if test="personIdCardNo != null  and personIdCardNo != ''">and person_id_card_no = #{personIdCardNo}</if>
            <if test="personMobile != null  and personMobile != ''">and person_mobile = #{personMobile}</if>
            <if test="personType != null  and personType != ''">and person_type = #{personType}</if>
            <if test="personDays != null ">and person_days = #{personDays}</if>
            <if test="collectorName != null  and collectorName != ''">and collector_name like concat('%',
                #{collectorName}, '%')
            </if>
            <if test="collectorMobile != null  and collectorMobile != ''">and collector_mobile = #{collectorMobile}</if>
            <if test="params.beginCollectTime != null and params.beginCollectTime != '' and params.endCollectTime != null and params.endCollectTime != ''">
                and collect_time between #{params.beginCollectTime} and #{params.endCollectTime}
            </if>
            <if test="pushState != null ">and push_state = #{pushState}</if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="mixType != null ">and mix_type = #{mixType}</if>
            <if test="pushMsg != null  and pushMsg != ''">and push_msg = #{pushMsg}</if>
        </where>
    </select>

    <select id="selectNatSampleById" parameterType="Long" resultMap="NatSample1Result">
        <include refid="selectNatSample1Vo"/>
        where id = #{id}
    </select>

    <insert id="insertNatSample" parameterType="space.lzhq.ph.domain.NatSample1" useGeneratedKeys="true"
            keyProperty="id">
        insert into ph_nat_sample1
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="personId != null">person_id,</if>
            <if test="personName != null and personName != ''">person_name,</if>
            <if test="personDepartment != null">person_department,</if>
            <if test="personIdCardNo != null and personIdCardNo != ''">person_id_card_no,</if>
            <if test="personMobile != null and personMobile != ''">person_mobile,</if>
            <if test="personNation != null">person_nation,</if>
            <if test="personType != null and personType != ''">person_type,</if>
            <if test="personDays != null">person_days,</if>
            <if test="personAddress != null and personAddress != ''">person_address,</if>
            <if test="collectorName != null and collectorName != ''">collector_name,</if>
            <if test="collectorMobile != null and collectorMobile != ''">collector_mobile,</if>
            <if test="collectAddress != null and collectAddress != ''">collect_address,</if>
            <if test="collectPosition != null">collect_position,</if>
            <if test="collectTime != null">collect_time,</if>
            <if test="pushState != null">push_state,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="mixType != null">mix_type,</if>
            <if test="pushMsg != null and pushMsg != ''">push_msg,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="personId != null">#{personId},</if>
            <if test="personName != null and personName != ''">#{personName},</if>
            <if test="personDepartment != null">#{personDepartment},</if>
            <if test="personIdCardNo != null and personIdCardNo != ''">#{personIdCardNo},</if>
            <if test="personMobile != null and personMobile != ''">#{personMobile},</if>
            <if test="personNation != null">#{personNation},</if>
            <if test="personType != null and personType != ''">#{personType},</if>
            <if test="personDays != null">#{personDays},</if>
            <if test="personAddress != null and personAddress != ''">#{personAddress},</if>
            <if test="collectorName != null and collectorName != ''">#{collectorName},</if>
            <if test="collectorMobile != null and collectorMobile != ''">#{collectorMobile},</if>
            <if test="collectAddress != null and collectAddress != ''">#{collectAddress},</if>
            <if test="collectPosition != null">#{collectPosition},</if>
            <if test="collectTime != null">#{collectTime},</if>
            <if test="pushState != null">#{pushState},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="mixType != null">#{mixType},</if>
            <if test="pushMsg != null and pushMsg != ''">#{pushMsg},</if>
        </trim>
    </insert>

    <update id="updateNatSample" parameterType="space.lzhq.ph.domain.NatSample1">
        update ph_nat_sample1
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="personId != null">person_id = #{personId},</if>
            <if test="personName != null and personName != ''">person_name = #{personName},</if>
            <if test="personDepartment != null">person_department = #{personDepartment},</if>
            <if test="personIdCardNo != null and personIdCardNo != ''">person_id_card_no = #{personIdCardNo},</if>
            <if test="personMobile != null and personMobile != ''">person_mobile = #{personMobile},</if>
            <if test="personNation != null">person_nation = #{personNation},</if>
            <if test="personType != null and personType != ''">person_type = #{personType},</if>
            <if test="personDays != null">person_days = #{personDays},</if>
            <if test="personAddress != null and personAddress != ''">person_address = #{personAddress},</if>
            <if test="collectorName != null and collectorName != ''">collector_name = #{collectorName},</if>
            <if test="collectorMobile != null and collectorMobile != ''">collector_mobile = #{collectorMobile},</if>
            <if test="collectAddress != null and collectAddress != ''">collect_address = #{collectAddress},</if>
            <if test="collectPosition != null">collect_position = #{collectPosition},</if>
            <if test="collectTime != null">collect_time = #{collectTime},</if>
            <if test="pushState != null">push_state = #{pushState},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="mixType != null">mix_type = #{mixType},</if>
            <if test="pushMsg != null and pushMsg != ''">push_msg = #{pushMsg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNatSampleById" parameterType="Long">
        delete
        from ph_nat_sample1
        where id = #{id}
    </delete>

    <delete id="deleteNatSampleByIds" parameterType="String">
        delete from ph_nat_sample1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByCode" parameterType="String" resultType="int">
        select count(*)
        from ph_nat_sample1
        where code = #{code}
    </select>

    <update id="updateMixType">
        update ph_nat_sample1
        set mix_type = if(mix_type > #{mixType}, mix_type, #{mixType})
        where code = #{code}
    </update>

</mapper>
