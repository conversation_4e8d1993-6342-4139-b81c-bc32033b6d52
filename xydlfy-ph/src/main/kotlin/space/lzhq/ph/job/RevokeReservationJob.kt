package space.lzhq.ph.job

import org.dromara.hutool.core.date.DateField
import org.dromara.hutool.core.date.DateTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.domain.Reservation.RESERVATION_STATUS_MIP_LOCK_SUCCESS
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.service.IReservationService

/**
 * 释放号源
 * 表达式：0 0/1 * * * ?
 */
@Component("revokeReservationJob")
class RevokeReservationJob {

    @Autowired
    private lateinit var reservationService: IReservationService

    fun execute() {
        val now = DateTime.now()
        val oneDayAgo = now.offsetNew(DateField.DAY_OF_MONTH, -1)
        val twoMinutesAgo = now.offsetNew(DateField.MINUTE, -2)
        val reservations = reservationService.lambdaQuery()
            .between(Reservation::getOperationTime, oneDayAgo, twoMinutesAgo)
            .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
            .eq(Reservation::getStatus, RESERVATION_STATUS_MIP_LOCK_SUCCESS)
            .list()
        reservations.forEach {
            HisExt.revokeReservationWithMip(it)
        }
    }
}