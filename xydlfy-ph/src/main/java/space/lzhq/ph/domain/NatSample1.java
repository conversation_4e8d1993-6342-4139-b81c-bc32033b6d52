package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.dromara.hutool.core.text.StrUtil;

import java.io.Serial;
import java.util.Date;

/**
 * 样品对象 ph_nat_sample1
 *
 * <AUTHOR>
 * @date 2021-12-12
 */
public class NatSample1 extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 混样编码
     */
    @Excel(name = "混样编码")
    private String code;

    /**
     * 混样类型
     * 1=单人单管，5=五人一管，10=十人一管
     */
    @Excel(name = "混样类型", dictType = "nat_mix_type")
    private Integer mixType;

    /**
     * 样品人编号
     */
    private Long personId;

    /**
     * 样品人姓名
     */
    @Excel(name = "样品人姓名")
    private String personName;

    /**
     * 样品人科室
     */
    private Integer personDepartment;

    /**
     * 样品人身份证号
     */
    @Excel(name = "样品人身份证号")
    private String personIdCardNo;

    /**
     * 样品人手机号
     */
    @Excel(name = "样品人手机号")
    private String personMobile;

    /**
     * 样品人民族
     */
    @Excel(name = "样品人民族", dictType = "minzu")
    private Integer personNation;

    /**
     * 样品人类型
     */
    @Excel(name = "样品人类型")
    private String personType;

    /**
     * 几天一检
     */
    @Excel(name = "几天一检")
    private Long personDays;

    /**
     * 样品人现住址
     */
    @Excel(name = "样品人现住址")
    private String personAddress;

    /**
     * 采样人姓名
     */
    @Excel(name = "采样人姓名")
    private String collectorName;

    /**
     * 采样人手机号
     */
    @Excel(name = "采样人手机号")
    private String collectorMobile;

    /**
     * 采样地点
     */
    @Excel(name = "采样地点")
    private String collectAddress;

    /**
     * 采样区域
     */
    @Excel(name = "采样区域", dictType = "nat_collect_position")
    private Integer collectPosition;

    /**
     * 采样时间
     */
    @Excel(name = "采样时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    /**
     * 推送状态
     */
    @Excel(name = "推送状态", dictType = "nat_push_state")
    private Integer pushState;

    /**
     * 推送时间
     */
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 推送状态描述
     */
    @Excel(name = "推送状态描述")
    private String pushMsg;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonDepartment(Integer personDepartment) {
        this.personDepartment = personDepartment;
    }

    public Integer getPersonDepartment() {
        return personDepartment;
    }

    public void setPersonIdCardNo(String personIdCardNo) {
        this.personIdCardNo = personIdCardNo;
    }

    public String getPersonIdCardNo() {
        return personIdCardNo;
    }

    public void setPersonMobile(String personMobile) {
        this.personMobile = personMobile;
    }

    public String getPersonMobile() {
        return personMobile;
    }

    public void setPersonNation(Integer personNation) {
        this.personNation = personNation;
    }

    public Integer getPersonNation() {
        return personNation;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonDays(Long personDays) {
        this.personDays = personDays;
    }

    public Long getPersonDays() {
        return personDays;
    }

    public void setPersonAddress(String personAddress) {
        this.personAddress = personAddress;
    }

    public String getPersonAddress() {
        return personAddress;
    }

    public Integer getCollectPosition() {
        return collectPosition;
    }

    public void setCollectPosition(Integer collectPosition) {
        this.collectPosition = collectPosition;
    }

    public void setCollectorName(String collectorName) {
        this.collectorName = collectorName;
    }

    public String getCollectorName() {
        return collectorName;
    }

    public void setCollectorMobile(String collectorMobile) {
        this.collectorMobile = collectorMobile;
    }

    public String getCollectorMobile() {
        return collectorMobile;
    }

    public void setCollectAddress(String collectAddress) {
        this.collectAddress = collectAddress;
    }

    public String getCollectAddress() {
        return collectAddress;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setPushState(Integer pushState) {
        this.pushState = pushState;
    }

    public Integer getPushState() {
        return pushState;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushMsg(String pushMsg) {
        this.pushMsg = StrUtil.sub(pushMsg, 0, 100);
    }

    public String getPushMsg() {
        return pushMsg;
    }

    public Integer getMixType() {
        return mixType;
    }

    public void setMixType(Integer mixType) {
        this.mixType = mixType;
    }

    public boolean isPushOk() {
        return pushState != null && pushState == 1;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("code", getCode())
                .append("personId", getPersonId())
                .append("personName", getPersonName())
                .append("personDepartment", getPersonDepartment())
                .append("personIdCardNo", getPersonIdCardNo())
                .append("personMobile", getPersonMobile())
                .append("personNation", getPersonNation())
                .append("personType", getPersonType())
                .append("personDays", getPersonDays())
                .append("personAddress", getPersonAddress())
                .append("collectorName", getCollectorName())
                .append("collectorMobile", getCollectorMobile())
                .append("collectAddress", getCollectAddress())
                .append("collectTime", getCollectTime())
                .append("pushState", getPushState())
                .append("pushTime", getPushTime())
                .append("pushMsg", getPushMsg())
                .toString();
    }

}
