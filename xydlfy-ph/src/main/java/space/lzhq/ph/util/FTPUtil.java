package space.lzhq.ph.util;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;

public class FTPUtil {

    private static boolean doFtpUpload(String host, int port, String username, String password,
                                       String remotePath, String remoteFileName,
                                       InputStreamSupplier streamSupplier) throws IOException {
        FTPClient ftpClient = new FTPClient();
        try {
            // 连接FTP服务器
            ftpClient.connect(host, port);
            ftpClient.login(username, password);

            // 设置文件类型为二进制
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 进入被动模式
            ftpClient.enterLocalPassiveMode();

            // 切换到目标目录
            ftpClient.changeWorkingDirectory(remotePath);

            // 上传文件
            try (InputStream is = streamSupplier.get()) {
                return ftpClient.storeFile(remoteFileName, is);
            }
        } finally {
            // 确保关闭连接
            if (ftpClient.isConnected()) {
                ftpClient.logout();
                ftpClient.disconnect();
            }
        }
    }

    @FunctionalInterface
    private interface InputStreamSupplier {
        InputStream get() throws IOException;
    }

    public static boolean uploadFile(String host, int port, String username, String password,
                                     String remotePath, String remoteFileName, Path localFile) throws IOException {
        return doFtpUpload(host, port, username, password, remotePath, remoteFileName,
                () -> Files.newInputStream(localFile));
    }

    public static boolean uploadString(String host, int port, String username, String password,
                                       String remotePath, String remoteFileName, String content,
                                       Charset charset) throws IOException {
        return doFtpUpload(host, port, username, password, remotePath, remoteFileName,
                () -> new ByteArrayInputStream(content.getBytes(charset)));
    }

}
