# TraceId使用指南

## 概述

TraceId是一个唯一的追踪标识符，用于在分布式系统中跟踪请求的完整生命周期。在我们的短信服务中，每个短信发送和查询操作都会分配一个唯一的TraceId，方便日志检索和问题排查。

## 功能特性

- **自动生成**: 每个请求自动生成唯一的TraceId
- **线程安全**: 使用MDC（Mapped Diagnostic Context）确保线程安全
- **日志集成**: 所有日志自动包含TraceId信息
- **灵活管理**: 支持自动和手动管理TraceId生命周期

## 日志格式

启用TraceId后，日志格式如下：

```
HH:mm:ss.SSS [thread] LEVEL [traceId] logger - [method,line] - message
```

示例：

```
14:30:25.123 [http-nio-8080-exec-1] INFO [a1b2c3d4e5f6] space.lzhq.ph.service.impl.SmsServiceImpl - [sendSms,85] - 开始发送短信 - mobile: 138****8888, content: 【测试】验证码短信
```

## 使用方法

### 1. 自动管理TraceId（推荐）

```java
// 方式1: 无返回值操作
TraceIdUtil.executeWithTraceId(() ->{
        log.

info("开始处理业务");
// 业务逻辑
    log.

info("业务处理完成");
});

// 方式2: 有返回值操作
SmsResponse response = TraceIdUtil.executeWithTraceId(() -> {
    log.info("开始发送短信");
    return smsService.sendSms(mobile, content);
});
```

### 2. 手动管理TraceId

```java
String traceId = TraceIdUtil.ensureTraceId();
try{
        log.

info("开始处理业务");
// 业务逻辑
}finally{
        TraceIdUtil.

clearTraceIdIfCreated(traceId);
}
```

### 3. 使用指定TraceId

```java
String customTraceId = "SMS-" + orderId;
TraceIdUtil.

executeWithSpecificTraceId(customTraceId, () ->{
        log.

info("处理订单相关短信");
// 业务逻辑
});
```

## 日志检索方法

### 1. 本地日志文件检索

```bash
# 搜索特定TraceId的所有日志
grep "a1b2c3d4e5f6" /path/to/logs/sys.log

# 搜索并显示上下文
grep -C 5 "a1b2c3d4e5f6" /path/to/logs/sys.log

# 搜索多个日志文件
grep "a1b2c3d4e5f6" /path/to/logs/*.log
```

### 2. ELK Stack检索

在Kibana中使用以下查询：

```
traceId:"a1b2c3d4e5f6"
```

或者组合查询：

```
traceId:"a1b2c3d4e5f6" AND level:ERROR
```

### 3. Graylog检索

```
traceId:a1b2c3d4e5f6
```

组合查询：

```
traceId:a1b2c3d4e5f6 AND level:3
```

### 4. 实时日志监控

```bash
# 实时监控包含特定TraceId的日志
tail -f /path/to/logs/sys.log | grep "a1b2c3d4e5f6"
```

## 短信服务中的TraceId应用

### 发送短信流程

1. **请求开始**: 生成TraceId
2. **参数验证**: 记录验证过程
3. **构建请求**: 记录请求构建详情
4. **发送请求**: 记录HTTP请求
5. **处理响应**: 记录响应处理
6. **清理资源**: 自动清理TraceId

### 查询短信状态流程

1. **查询开始**: 使用或生成TraceId
2. **参数验证**: 记录ID验证
3. **发送查询**: 记录查询请求
4. **处理结果**: 记录查询结果
5. **返回状态**: 记录最终状态

## 最佳实践

### 1. TraceId命名规范

- **自动生成**: 使用默认的UUID格式
- **业务相关**: 使用业务前缀，如 `SMS-{timestamp}` 或 `ORDER-{orderId}`
- **避免敏感信息**: 不要在TraceId中包含敏感数据

### 2. 日志记录规范

```java
// 好的做法
log.info("开始发送短信 - mobile: {}, content: {}",mobile, content);
log.

info("短信发送成功 - sismsid: {}",sismsid);

// 避免的做法
log.

info("发送短信: "+mobile +", "+content); // 字符串拼接
log.

info("短信发送成功"); // 缺少关键信息
```

### 3. 异常处理

```java
try{
        // 业务逻辑
        }catch(Exception e){
        log.

error("业务处理失败 - error: {}",e.getMessage(),e);
        // TraceId会自动包含在日志中
        }
```

## 故障排查示例

### 场景1: 短信发送失败

1. **获取TraceId**: 从用户反馈或监控告警中获取TraceId
2. **检索日志**: 使用TraceId检索所有相关日志
3. **分析流程**: 查看完整的发送流程
4. **定位问题**: 找到失败的具体步骤和原因

```bash
# 检索示例
grep "a1b2c3d4e5f6" /path/to/logs/sys.log

# 输出示例
14:30:25.123 [thread] INFO [a1b2c3d4e5f6] SmsServiceImpl - 开始发送短信
14:30:25.124 [thread] DEBUG [a1b2c3d4e5f6] SmsServiceImpl - 参数验证通过
14:30:25.125 [thread] DEBUG [a1b2c3d4e5f6] SmsServiceImpl - 构建短信请求完成
14:30:25.126 [thread] INFO [a1b2c3d4e5f6] SmsServiceImpl - 发送短信请求到服务器
14:30:25.500 [thread] ERROR [a1b2c3d4e5f6] SmsServiceImpl - 短信发送请求异常
```

### 场景2: 性能问题分析

1. **识别慢请求**: 通过TraceId找到耗时较长的请求
2. **分析时间分布**: 查看各个步骤的耗时
3. **定位瓶颈**: 找到性能瓶颈所在

## 配置说明

### 日志配置

在 `logback.xml` 中已配置TraceId支持：

```xml

<property name="log.pattern"
          value="%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{50} - [%method,%line] - %msg%n"/>
```

### Graylog配置

Graylog已配置为包含MDC数据：

```xml

<encoder class="de.siegmar.logbackgelf.GelfEncoder">
    <includeMdcData>true</includeMdcData>
</encoder>
```

## 注意事项

1. **性能影响**: TraceId对性能影响很小，可以在生产环境中使用
2. **内存管理**: 使用完毕后会自动清理，避免内存泄漏
3. **线程安全**: 基于ThreadLocal实现，确保线程安全
4. **日志大小**: TraceId会增加少量日志大小，但带来的价值远大于成本

## 扩展应用

### 1. 与监控系统集成

可以将TraceId传递给监控系统，实现日志和监控数据的关联：

```java
// 将TraceId添加到监控标签
Metrics.counter("sms.send.count","traceId",TraceIdUtil.getTraceId()).

increment();
```

### 2. 与分布式追踪集成

可以与Zipkin、Jaeger等分布式追踪系统集成：

```java
// 将TraceId设置为Span标签
Span span = tracer.nextSpan().tag("traceId", TraceIdUtil.getTraceId());
```

### 3. API响应中包含TraceId

在API响应中返回TraceId，方便前端和客户端进行问题排查：

```java

@RestController
public class SmsController {

    @PostMapping("/send")
    public ResponseEntity<SmsResponse> sendSms(@RequestBody SmsRequest request) {
        String traceId = TraceIdUtil.getTraceId();
        SmsResponse response = smsService.sendSms(request.getMobile(), request.getContent());

        return ResponseEntity.ok()
                .header("X-Trace-Id", traceId)
                .body(response);
    }
}
```

## 总结

TraceId是一个强大的日志追踪工具，能够显著提升问题排查效率。通过合理使用TraceId，可以：

- 快速定位问题根因
- 分析系统性能瓶颈
- 提升运维效率
- 改善用户体验

建议在所有重要的业务流程中使用TraceId，特别是涉及外部服务调用的场景。 