package space.lzhq.ph.domain;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LisReportItem implements Serializable {
    @Serial
    private static final long serialVersionUID = 6394388847728997332L;

    private String inspectionId;
    private String itemName;
    private String resultValue;
    private String unit;
    private String referenceValue;
    private String remark;

    public String getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(String inspectionId) {
        this.inspectionId = inspectionId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getResultValue() {
        return resultValue;
    }

    public void setResultValue(String resultValue) {
        this.resultValue = resultValue;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getReferenceValue() {
        return referenceValue;
    }

    public void setReferenceValue(String referenceValue) {
        this.referenceValue = referenceValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LisReportItem)) return false;

        LisReportItem that = (LisReportItem) o;

        if (!getInspectionId().equals(that.getInspectionId())) return false;
        if (getItemName() != null ? !getItemName().equals(that.getItemName()) : that.getItemName() != null)
            return false;
        if (getResultValue() != null ? !getResultValue().equals(that.getResultValue()) : that.getResultValue() != null)
            return false;
        if (getUnit() != null ? !getUnit().equals(that.getUnit()) : that.getUnit() != null) return false;
        if (getReferenceValue() != null ? !getReferenceValue().equals(that.getReferenceValue()) :
                that.getReferenceValue() != null)
            return false;
        if (getRemark() != null ? !getRemark().equals(that.getRemark()) : that.getRemark() != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = getInspectionId().hashCode();
        result = 31 * result + (getItemName() != null ? getItemName().hashCode() : 0);
        result = 31 * result + (getResultValue() != null ? getResultValue().hashCode() : 0);
        result = 31 * result + (getUnit() != null ? getUnit().hashCode() : 0);
        result = 31 * result + (getReferenceValue() != null ? getReferenceValue().hashCode() : 0);
        result = 31 * result + (getRemark() != null ? getRemark().hashCode() : 0);
        return result;
    }
}
