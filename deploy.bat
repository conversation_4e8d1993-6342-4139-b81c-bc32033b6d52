@echo off
setlocal
chcp 65001

set "MOSPITAL_PATH=d:\projects\mospital"
set "ZHANGYI_PATH=d:\projects\zhangyi-yiliyouyi"
set "ZYXCX_JAR_PATH=d:\zyxcx\zyxcx.jar"

set "SKIP_MOSPITAL="
if "%1"=="--skip-mospital" set "SKIP_MOSPITAL=1"

if not defined SKIP_MOSPITAL (
    rem 打包 mospital
    echo "正在打包 mospital..."
    cd "%MOSPITAL_PATH%"
    git fetch --all
    if %ERRORLEVEL% neq 0 goto :git_error
    git checkout master
    if %ERRORLEVEL% neq 0 goto :git_error
    git reset --hard origin/master
    if %ERRORLEVEL% neq 0 goto :git_error
    call mvn clean install -T 2C -Dmaven.test.skip=true
    if %ERRORLEVEL% neq 0 goto :maven_error
    echo "mospital 打包成功"
) else (
    echo "跳过 mospital 打包"
)

rem 打包 zhangyi
echo "正在打包 zhangyi..."
cd "%ZHANGYI_PATH%"
git fetch --all
if %ERRORLEVEL% neq 0 goto :git_error
git checkout master
if %ERRORLEVEL% neq 0 goto :git_error
git reset --hard origin/master
if %ERRORLEVEL% neq 0 goto :git_error
call mvn clean package -T 2C -Dmaven.test.skip=true
if %ERRORLEVEL% neq 0 goto :maven_error
echo "zhangyi 打包成功"

rem 停止 zyxcx 服务
echo "正在停止 zyxcx 服务..."
call :stop_service zyxcx
if %ERRORLEVEL% neq 0 goto :service_error

rem 复制 jar 包
echo "正在复制 jar 包..."
xcopy "%ZHANGYI_PATH%\ruoyi-admin\target\zyxcx.jar" "%ZYXCX_JAR_PATH%" /Y
if %ERRORLEVEL% neq 0 goto :copy_error

rem 启动 zyxcx 服务
echo "正在启动 zyxcx 服务..."
sc start zyxcx
if %ERRORLEVEL% neq 0 goto :service_error
echo "部署完成"
goto :eof

:stop_service
sc stop %1
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

set "retry_count=0"
:check_status
if %retry_count% geq 15 (
    echo "警告：服务 %1 停止操作超时，将继续执行后续操作..."
    exit /b 0
)
sc query %1 | find "STATE" | find "STOPPED" > nul
if %ERRORLEVEL% neq 0 (
    timeout /t 2 /nobreak > nul
    set /a retry_count+=1
    goto :check_status
)
echo "服务 %1 已成功停止"
exit /b 0

:git_error
echo "Git 操作失败，错误代码：%ERRORLEVEL%"
goto :error
:maven_error
echo "Maven 构建失败，错误代码：%ERRORLEVEL%"
goto :error
:service_error
echo "服务操作失败，错误代码：%ERRORLEVEL%"
goto :error
:copy_error
echo "文件复制失败，错误代码：%ERRORLEVEL%"
goto :error
:error
echo "发生错误，脚本退出。错误代码：%ERRORLEVEL%"
exit /b 1
