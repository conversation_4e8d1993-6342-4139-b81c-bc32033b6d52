package space.lzhq.ph.service.impl;

import com.alipay.api.response.AlipayCommerceAppAuthUploadResponse;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.json.JSONUtil;
import org.mospital.alipay.AlipayService;
import org.mospital.alipay.AlipaySetting;
import org.mospital.alipay.HospitalOrderStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.domain.Session;
import space.lzhq.ph.mapper.ReservationMapper;
import space.lzhq.ph.service.IReservationService;
import space.lzhq.ph.service.ISessionService;

import java.math.BigDecimal;
import java.util.Date;

import static space.lzhq.ph.domain.Reservation.RESERVATION_STATUS_CANCELED;

@Service
public class ReservationServiceImpl extends ServiceImpl<ReservationMapper, Reservation> implements IReservationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationServiceImpl.class);

    private final ISessionService sessionService;

    public ReservationServiceImpl(ISessionService sessionService) {
        this.sessionService = sessionService;
    }

    @Override
    public void pushAlipayHospitalOrder(Reservation reservation,
                                        HospitalOrderStatus hospitalOrderStatus,
                                        Date createTime,
                                        Date updateTime,
                                        Long reservationId
    ) {
        if (RESERVATION_STATUS_CANCELED.equals(reservation.getStatus()) && hospitalOrderStatus != HospitalOrderStatus.MERCHANT_CLOSED) {
            LOGGER.warn("订单已取消，不再推送支付宝挂号单智能消息：{}", hospitalOrderStatus.getDescription());
            return;
        }

        if (!reservation.getOpenid().startsWith(Constants.ALIPAY_USER_ID_PREFIX)) {
            LOGGER.warn("只能向支付宝用户推送挂号单");
            return;
        }

        Session session = sessionService.selectSessionByOpenId(reservation.getOpenid());
        if (session == null) {
            LOGGER.warn("患者已解绑");
            return;
        }
        if (CharSequenceUtil.isBlank(session.getAhoat())) {
            LOGGER.warn("患者未授权推送智能消息");
            return;
        }

        AlipayCommerceAppAuthUploadResponse commerceAppAuthUploadResponse = AlipayService.INSTANCE.pushHospitalOrder(
                session.getAhoat(),
                reservation.getOpenid(),
                reservation.getId() + "#" + reservation.getClinicNo(),
                createTime,
                updateTime,
                BigDecimal.ZERO,
                "",
                hospitalOrderStatus,
                reservation.getDepartmentCode() + "#" + reservation.getDoctorCode(),
                CharSequenceUtil.defaultIfBlank(reservation.getDepartmentName(), ""),
                CharSequenceUtil.defaultIfBlank(reservation.getDepartmentCode(), ""),
                CharSequenceUtil.defaultIfBlank(reservation.getDepartmentLocation(), "暂未提供"),
                "",
                CharSequenceUtil.defaultIfBlank(reservation.getDoctorName(), ""),
                "",
                CharSequenceUtil.defaultIfBlank(reservation.getDoctorCode(), ""),
                "",
                reservation.getName(),
                reservation.getJzTime(),
                "",
                "",
                "",
                "",
                reservation.getClinicNo(),
                "alipays://platformapi/startapp?appId=" + AlipaySetting.INSTANCE.getMaAppId() + "&page=pages_yuyue" +
                        "/YuyueDetail/YuyueDetail&query=id%3D" +
                        (reservationId == null ? reservation.getId() : reservationId)
        );
        if (commerceAppAuthUploadResponse.isSuccess()) {
            if (CharSequenceUtil.isBlank(reservation.getAlipayHospitalOrderId())) {
                String alipayHospitalOrderId = JSONUtil.parseObj(
                        commerceAppAuthUploadResponse.getData().getResponse()
                ).getStr("order_id");
                reservation.setAlipayHospitalOrderId(alipayHospitalOrderId);
                lambdaUpdate()
                        .set(Reservation::getAlipayHospitalOrderId, alipayHospitalOrderId)
                        .eq(Reservation::getId, reservation.getId())
                        .update();
            }
        } else {
            LOGGER.warn("推送支付宝挂号单智能消息失败：{}-{}", commerceAppAuthUploadResponse.getSubCode(), commerceAppAuthUploadResponse.getSubMsg());
        }
    }

}
