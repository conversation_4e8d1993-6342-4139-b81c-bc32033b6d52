# 短信发送记录功能文档

## 概述
短信发送记录功能用于记录系统发送的所有短信，包括发送状态、响应信息、业务关联等，便于审计和问题追踪。

## 功能特性
- 自动记录所有短信发送情况
- 支持发送状态跟踪（待发送、成功、失败）
- **智能重试机制**：重试时不会创建重复记录，确保数据一致性
- 提供多维度查询功能
- 支持统计分析

## 数据库设计

### 表结构：ph_sms_record

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|---------|------|
| id | bigint | - | NO | 自增 | 主键ID |
| sismsid | varchar | 64 | YES | NULL | 短信ID |
| mobile | varchar | 20 | NO | - | 手机号码 |
| content | text | - | YES | NULL | 短信内容 |
| send_time | datetime | - | YES | NULL | 发送时间 |
| send_status | int | - | YES | 0 | 发送状态(0-待发送,1-成功,2-失败) |
| response_code | varchar | 20 | YES | NULL | 响应代码 |
| response_message | varchar | 255 | YES | NULL | 响应消息 |
| business_type | varchar | 50 | YES | NULL | 业务类型 |
| business_id | bigint | - | YES | NULL | 业务ID |
| extcode | varchar | 20 | YES | NULL | 扩展码 |
| send_method | int | - | YES | 0 | 发送方式(0-普通,9-验证码) |
| retry_count | int | - | YES | 0 | 重试次数 |
| error_message | varchar | 500 | YES | NULL | 错误信息 |
| create_time | datetime | - | YES | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | YES | CURRENT_TIMESTAMP | 更新时间 |

## 技术架构

### 重构说明（2024年更新）
原实现使用XML映射文件，现已重构为使用MyBatis-Plus的Lambda查询方式，具有以下优势：

#### 重构前（XML方式）
- 需要维护XML映射文件
- SQL语句分散在XML中，不易查找
- 字段名修改时需要同步修改XML
- IDE支持有限，容易出现拼写错误

#### 重构后（Lambda查询方式）
- **无需XML文件**：完全基于Java代码，利用Lambda表达式实现类型安全查询
- **编译时检查**：字段名错误可在编译期发现
- **IDE友好**：完整的代码提示和重构支持
- **维护简便**：所有查询逻辑集中在Mapper接口中

### 核心类说明

#### 1. SmsRecord 实体类
```java
@Data
@Accessors(chain = true)
@TableName("ph_sms_record")
public class SmsRecord implements Serializable {
    // 使用Lombok @Data注解自动生成getter/setter
    // 支持MyBatis-Plus Lambda表达式
}
```

#### 2. SmsRecordMapper 接口（重构后）
```java
@Mapper
public interface SmsRecordMapper extends BaseMapper<SmsRecord> {
    
    // 使用default方法 + Lambda查询替代XML
    default List<SmsRecord> selectByBusinessTypeAndId(String businessType, Long businessId) {
        LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SmsRecord::getBusinessType, businessType);
        if (businessId != null) {
            wrapper.eq(SmsRecord::getBusinessId, businessId);
        }
        wrapper.orderByDesc(SmsRecord::getCreateTime);
        return selectList(wrapper);
    }
    
    // 其他查询方法...
}
```

#### 3. 查询方法重构对比

| 功能 | 重构前（XML） | 重构后（Lambda） |
|------|---------------|------------------|
| 按业务类型查询 | XML + @Param注解 | Lambda表达式 + default方法 |
| 按手机号时间范围查询 | XML动态SQL | 条件式Lambda构建 |
| 按发送状态查询 | XML + limit语句 | Lambda + last()方法 |
| 统计查询 | XML count语句 | selectCount(wrapper) |
| 按短信ID查询 | XML简单查询 | selectOne(wrapper) |

### 重构优势

1. **类型安全**
   ```java
   // 重构前：字段名字符串，容易拼写错误
   wrapper.eq("business_type", businessType);
   
   // 重构后：使用方法引用，编译期检查
   wrapper.eq(SmsRecord::getBusinessType, businessType);
   ```

2. **代码集中**
   - 所有查询逻辑在Mapper接口中
   - 无需维护额外的XML文件
   - 便于代码审查和维护

3. **IDE支持**
   - 完整的代码提示
   - 自动重构支持
   - 跳转到定义功能

4. **动态查询构建**
   ```java
   LambdaQueryWrapper<SmsRecord> wrapper = Wrappers.lambdaQuery();
   wrapper.eq(SmsRecord::getMobile, mobile);
   if (startTime != null) {
       wrapper.ge(SmsRecord::getSendTime, startTime);
   }
   if (endTime != null) {
       wrapper.le(SmsRecord::getSendTime, endTime);
   }
   ```

#### 4. ISmsRecordService 服务接口
定义业务操作方法：
- 保存短信记录
- 更新发送结果
- 查询功能
- 重试发送

#### 5. SmsRecordServiceImpl 服务实现
实现具体业务逻辑，继承ServiceImpl获得基础CRUD能力。

## API接口

### 测试接口
```
GET /test/sms-record/test-lambda-query          # 测试Lambda查询
GET /test/sms-record/test-lambda-query-mobile   # 测试按手机号查询
GET /test/sms-record/test-lambda-count          # 测试统计查询
```

### 事务优化测试接口
```
POST /test/sms-record/create-test-record                    # 创建测试用的失败短信记录
POST /test/sms-record/test-transaction-methods/{recordId}   # 测试事务优化的独立方法
POST /test/sms-record/test-full-retry/{recordId}           # 测试完整的事务优化重试流程
GET  /test/sms-record/record/{recordId}                    # 查询记录详情
```

#### 使用示例
1. **创建测试记录**
   ```bash
   curl -X POST http://localhost:8080/test/sms-record/create-test-record
   ```
   返回：
   ```json
   {
     "success": true,
     "recordId": 123,
     "sismsid": "TX_TEST_1703123456789",
     "message": "测试记录创建成功"
   }
   ```

2. **测试事务优化方法**
   ```bash
   curl -X POST http://localhost:8080/test/sms-record/test-transaction-methods/123
   ```
   返回：
   ```json
   {
     "success": true,
     "updateToPending": true,
     "pendingStatus": 0,
     "updateWithException": true,
     "exceptionStatus": 2,
     "exceptionRetryCount": 2,
     "updateWithSuccess": true,
     "successStatus": 1,
     "successRetryCount": 3,
     "message": "事务优化方法测试完成"
   }
   ```

3. **测试完整重试流程**
   ```bash
   curl -X POST http://localhost:8080/test/sms-record/test-full-retry/123
   ```

## 业务类型常量
```java
public static final String CARD_REFUND_APPLICATION = "CARD_REFUND_APPLICATION";  // 退卡申请
public static final String VERIFICATION_CODE = "VERIFICATION_CODE";              // 验证码
public static final String GENERAL_NOTIFICATION = "GENERAL_NOTIFICATION";        // 通用通知
```

## 状态枚举

### 发送状态
- `0` - 待发送
- `1` - 发送成功  
- `2` - 发送失败

### 发送方式
- `0` - 普通短信
- `9` - 验证码

## 使用示例

### 1. 保存短信记录
```java
SmsRecord record = smsRecordService.saveSmsRecord(
    sismsid, mobile, content, sendMethod, extcode, businessType, businessId
);
```

### 2. 更新发送结果
```java
smsRecordService.updateSmsResult(sismsid, smsResponse);
```

### 3. 查询业务相关短信
```java
List<SmsRecord> records = smsRecordService.getByBusinessTypeAndId(
    SmsRecord.BusinessType.CARD_REFUND_APPLICATION, applicationId
);
```

### 4. 重试失败的短信
```java
boolean success = smsRecordService.retrySendSms(recordId);
```

## 集成说明

在短信服务(SmsService)中已集成自动记录功能：

```java
@Override
public SmsResponse sendSms(String mobile, String content, String extcode, Integer sendMethod) {
    // 1. 生成短信ID
    String sismsid = generateSmsId();
    
    // 2. 保存发送记录
    smsRecordService.saveSmsRecord(sismsid, mobile, content, sendMethod, extcode, null, null);
    
    try {
        // 3. 实际发送短信
        SmsResponse response = doSendSms(mobile, content, extcode, sendMethod);
        
        // 4. 更新发送结果
        smsRecordService.updateSmsResult(sismsid, response);
        
        return response;
    } catch (Exception e) {
        // 5. 更新错误信息
        smsRecordService.updateSmsError(sismsid, e.getMessage());
        throw e;
    }
}
```

## 重试机制优化（重要更新）

### 问题描述
原始实现中存在两个关键问题：

1. **重复记录问题**：`retrySendSms`方法调用`smsService.sendSms()`会创建新的短信记录，导致每次重试都产生重复记录。

2. **重试计数重复递增问题**：重试逻辑中`retryCount`会被重复递增：
   ```java
   // 问题流程：
   retrySendSms() {
       record.setRetryCount(record.getRetryCount() + 1);  // 第1次递增
       try {
           smsService.retrySendSms(...);
       } catch (Exception e) {
           updateSmsError(...);  // 内部会再次递增retryCount (第2次)
       }
   }
   ```

### 解决方案
引入专门的重试机制，同时修复计数问题：

#### 1. 新增重试专用接口
```java
// ISmsService接口新增方法
SmsResponse retrySendSms(String sismsid, String mobile, String content, String extcode, Integer sendmethod);
```

#### 2. 修复重试计数逻辑
```java
@Override
public boolean retrySendSms(Long id) {
    // 1. 获取原记录
    SmsRecord record = this.getById(id);
    
    // 2. 更新状态为待发送（不在此处递增retryCount）
    record.setSendTime(LocalDateTime.now());
    record.setSendStatus(SmsRecord.SendStatus.PENDING.getCode());
    this.updateById(record);
    
    try {
        // 3. 使用重试专用方法（不创建新记录）
        SmsResponse response = smsService.retrySendSms(...);
        
        // 4. 根据结果统一处理retryCount递增
        if (response.isSuccess()) {
            record.setSendStatus(SUCCESS)
                  .setRetryCount(record.getRetryCount() + 1);  // 成功时递增
        } else {
            record.setSendStatus(FAILED)
                  .setRetryCount(record.getRetryCount() + 1);  // 失败时递增
        }
        this.updateById(record);
        
    } catch (Exception e) {
        // 5. 异常时也只递增一次
        record.setSendStatus(FAILED)
              .setRetryCount(record.getRetryCount() + 1);  // 异常时递增
        this.updateById(record);
    }
}
```

#### 3. 关键差异对比

| 方面 | 修复前 | 修复后 |
|------|---------|---------|
| 记录创建 | 每次重试创建新记录 | 重试时更新现有记录 |
| sismsid | 每次生成新ID | 使用原有ID |
| retryCount递增 | 可能递增2次（异常时） | 每次重试只递增1次 |
| 数据一致性 | 存在重复记录 | 保证唯一性 |
| 追踪能力 | 难以关联重试记录 | 完整追踪重试历史 |

#### 4. 优势
- **数据一致性**：确保一条短信只有一条记录
- **准确计数**：`retry_count`准确反映实际重试次数
- **完整追踪**：通过retry_count字段跟踪重试次数
- **性能优化**：避免不必要的记录创建
- **查询简化**：业务查询时不会返回重复记录

### 测试验证
```java
@Test
@DisplayName("测试短信重试功能（验证不创建重复记录）")
void testSmsRetryWithoutDuplicateRecords() {
    // 创建失败记录
    SmsRecord record = smsRecordService.saveSmsRecord(...);
    smsRecordService.updateSmsError(sismsid, "模拟失败");
    
    // 记录重试前数量和计数
    int countBefore = getRecordCount(businessType, businessId);
    int retryCountBefore = record.getRetryCount();
    
    // 执行重试
    smsRecordService.retrySendSms(record.getId());
    
    // 验证记录数量未增加，重试次数只递增1
    int countAfter = getRecordCount(businessType, businessId);
    int retryCountAfter = getUpdatedRecord().getRetryCount();
    
    assertEquals(countBefore, countAfter);
    assertEquals(retryCountBefore + 1, retryCountAfter);
}
```

## 测试验证

### Lambda查询功能测试
访问以下测试端点验证重构后的功能：

1. **基础Lambda查询测试**
   ```
   GET /test/sms-record/test-lambda-query
   ```

2. **按手机号查询测试**
   ```
   GET /test/sms-record/test-lambda-query-mobile?mobile=***********
   ```

3. **统计查询测试**
   ```
   GET /test/sms-record/test-lambda-count
   ```

### 重试功能测试
使用JUnit测试验证重试机制：
```java
@Test
@DisplayName("测试短信重试功能（验证不创建重复记录）")
void testSmsRetryWithoutDuplicateRecords()
```

## 性能优化建议

1. **索引优化**
   ```sql
   -- 业务查询索引
   CREATE INDEX idx_business_type_id ON ph_sms_record(business_type, business_id);
   
   -- 手机号时间查询索引
   CREATE INDEX idx_mobile_sendtime ON ph_sms_record(mobile, send_time);
   
   -- 状态查询索引
   CREATE INDEX idx_send_status ON ph_sms_record(send_status);
   ```

2. **分页查询**
   使用MyBatis-Plus分页插件：
   ```java
   Page<SmsRecord> page = new Page<>(current, size);
   IPage<SmsRecord> result = smsRecordService.page(page, wrapper);
   ```

3. **批量操作**
   对于大量数据使用批量操作：
   ```java
   smsRecordService.saveBatch(recordList);
   ```

## 注意事项

1. **并发安全**：使用@Transactional确保操作原子性
2. **异常处理**：妥善处理短信发送异常，确保记录状态正确
3. **数据清理**：定期清理过期的短信记录
4. **隐私保护**：敏感信息需要适当脱敏处理
5. **Lambda查询优势**：充分利用编译期检查和IDE支持特性
6. **重试机制**：确保使用`retrySendSms`而不是普通`sendSms`进行重试

## 更新日志

### 2024年12月 - 循环依赖修复
- **重大修复**：解决`SmsServiceImpl`和`SmsRecordServiceImpl`之间的循环依赖问题
- **解决方案**：在`SmsRecordServiceImpl`中对`ISmsService`依赖使用`@Lazy`注解
- **技术细节**：
  - `SmsServiceImpl` 需要 `ISmsRecordService` 来保存和更新短信记录
  - `SmsRecordServiceImpl` 需要 `ISmsService` 来进行重试发送
  - 使用`@Lazy`延迟初始化重试功能中的SMS服务依赖
- **类型修复**：修复测试代码中的类型不匹配问题，使用枚举类型而不是Integer
- **测试恢复**：重新创建`SmsRecordTestController`测试控制器

### 2024年12月 - 事务优化
- **重大优化**：解决重试方法中的事务管理问题
- **事务分离**：将重试流程拆分为三个独立事务，避免长时间数据库锁
- **性能提升**：外部SMS服务调用不再阻塞数据库事务
- **并发改善**：减少事务等待时间，提高系统并发性能
- **新增方法**：
  - `updateRecordToPending()` - 独立事务更新状态为PENDING
  - `updateRecordWithResult()` - 独立事务更新最终结果
  - `updateRecordWithException()` - 独立事务处理异常情况
- **测试完善**：新增`testTransactionOptimizedSmsRetry`测试方法

### 2024年重试机制优化
- **重大修复**：解决重试时创建重复记录的问题
- **计数修复**：解决`retryCount`重复递增的问题，确保每次重试只递增1次
- **新增接口**：`ISmsService.retrySendSms()`专用重试方法
- **数据一致性**：确保一条短信只有一条记录
- **完整追踪**：通过retry_count准确记录重试历史
- **逻辑优化**：重试成功、失败、异常三种情况下统一处理计数递增
- **新增测试**：验证重试不创建重复记录且计数正确的测试用例

### 2024年重构
- **重大改进**：从XML映射文件重构为MyBatis-Plus Lambda查询方式
- **删除文件**：移除`SmsRecordMapper.xml`文件
- **增强功能**：添加编译期类型检查和IDE支持
- **代码简化**：减少维护成本，提高开发效率
- **新增测试**：添加Lambda查询功能测试端点

### 初始版本
- 实现基础短信记录功能
- 支持发送状态跟踪
- 提供重试机制