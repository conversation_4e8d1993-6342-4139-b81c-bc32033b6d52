package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.SmsRecord;
import space.lzhq.ph.dto.sms.SmsResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信发送记录服务接口
 *
 * <AUTHOR>
 */
public interface ISmsRecordService extends IService<SmsRecord> {

    /**
     * 保存短信发送记录
     *
     * @param sismsid      短信ID
     * @param mobile       手机号码
     * @param content      短信内容
     * @param sendMethod   发送方式
     * @param extcode      扩展码
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 短信记录
     */
 SmsRecord saveSmsRecord(String sismsid, String mobile, String content,
                        SmsRecord.SendMethod sendMethod, String extcode,
                           String businessType, Long businessId);

    /**
     * 更新短信发送结果
     *
     * @param sismsid     短信ID
     * @param smsResponse 短信发送响应
     */
    void updateSmsResult(String sismsid, SmsResponse smsResponse);

    /**
     * 更新短信发送错误信息
     *
     * @param sismsid      短信ID
     * @param errorMessage 错误信息
     */
    void updateSmsError(String sismsid, String errorMessage);

    /**
     * 根据业务类型和业务ID查询短信记录
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 短信记录列表
     */
    List<SmsRecord> getByBusinessTypeAndId(String businessType, Long businessId);

    /**
     * 根据手机号码和时间范围查询短信记录
     *
     * @param mobile    手机号码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 短信记录列表
     */
    List<SmsRecord> getByMobileAndTimeRange(String mobile, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据发送状态查询短信记录
     *
     * @param sendStatus 发送状态
     * @param limit      限制数量
     * @return 短信记录列表
     */
    List<SmsRecord> getBySendStatus(Integer sendStatus, Integer limit);

    /**
     * 统计指定时间范围内的短信发送情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数量
     */
    Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据短信ID查询记录
     *
     * @param sismsid 短信ID
     * @return 短信记录
     */
    SmsRecord getBySismsid(String sismsid);

    /**
     * 重试发送失败的短信
     *
     * @param id 短信记录ID
     * @return 是否成功
     */
    boolean retrySendSms(Long id);
} 