package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.CardRefundApplication;
import space.lzhq.ph.domain.CardRefundApplicationSearchForm;
import space.lzhq.ph.service.ICardRefundApplicationService;

import java.util.List;

@RestController
@RequestMapping("/ph/cardRefundApplication")
public class CardRefundApplicationAdminController extends BaseController {

    private static final String PREFIX = "ph/cardRefundApplication";

    private final ICardRefundApplicationService cardRefundApplicationService;

    public CardRefundApplicationAdminController(ICardRefundApplicationService cardRefundApplicationService) {
        this.cardRefundApplicationService = cardRefundApplicationService;
    }

    @RequiresPermissions("ph:cardRefundApplication:view")
    @GetMapping()
    public ModelAndView cardRefund() {
        return new ModelAndView(PREFIX + "/cardRefundApplication");
    }

    /**
     * 查询就诊卡余额退款申请列表
     */
    @RequiresPermissions("ph:cardRefundApplication:list")
    @PostMapping("/list")
    public TableDataInfo list(CardRefundApplicationSearchForm searchForm) {
        startPage("id desc");
        List<CardRefundApplication> list = cardRefundApplicationService.getListBySearchForm(searchForm);
        return getDataTable(list);
    }

    /**
     * 导出就诊卡余额退款申请列表
     */
    @RequiresPermissions("ph:cardRefundApplication:export")
    @Log(title = "就诊卡余额退款申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(CardRefundApplicationSearchForm searchForm) {
        List<CardRefundApplication> list = cardRefundApplicationService.getListBySearchForm(searchForm);
        ExcelUtil<CardRefundApplication> util = new ExcelUtil<>(CardRefundApplication.class);
        return util.exportExcel(list, "就诊卡余额退款申请");
    }

    /**
     * 详情就诊卡余额退款申请
     */
    @RequiresPermissions("ph:cardRefundApplication:detail")
    @GetMapping("/detail/{id}")
    public ModelAndView detail(@PathVariable("id") Long id) {
        CardRefundApplication cardRefundApplication = cardRefundApplicationService.getById(id);
        ModelAndView mav = new ModelAndView(PREFIX + "/detail");
        mav.addObject("cardRefundApplication", cardRefundApplication);
        return mav;
    }
}
